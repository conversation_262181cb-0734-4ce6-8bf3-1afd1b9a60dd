/* AUTO-GENERATED FILE. DO NOT MODIFY.
 *
 * This class was automatically generated by
 * .NET for Android from the resource data it found.
 * It should not be modified by hand.
 */
package androidx.navigation.ui;

public final class R {
	public static final class anim {
		public static final int nav_default_enter_anim = 0x7f010030;
		public static final int nav_default_exit_anim = 0x7f010031;
		public static final int nav_default_pop_enter_anim = 0x7f010032;
		public static final int nav_default_pop_exit_anim = 0x7f010033;
	}
	public static final class animator {
		public static final int nav_default_enter_anim = 0x7f020022;
		public static final int nav_default_exit_anim = 0x7f020023;
		public static final int nav_default_pop_enter_anim = 0x7f020024;
		public static final int nav_default_pop_exit_anim = 0x7f020025;
	}
	public static final class integer {
		public static final int config_navAnimTime = 0x7f090005;
	}
	public static final class string {
		public static final int dest_title = 0x7f0f002f;
		public static final int nav_app_bar_navigate_up_description = 0x7f0f00a0;
		public static final int nav_app_bar_open_drawer_description = 0x7f0f00a1;
	}
}
