{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "css/app.css", "AssetFile": "css/app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000556173526"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1797"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A=\""}, {"Name": "ETag", "Value": "W/\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.css", "AssetFile": "css/app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3376"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.css.gz", "AssetFile": "css/app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1797"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A="}]}, {"Route": "css/app.e5tk7yf482.css", "AssetFile": "css/app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000556173526"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1797"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A=\""}, {"Name": "ETag", "Value": "W/\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}, {"Name": "label", "Value": "css/app.css"}]}, {"Route": "css/app.e5tk7yf482.css", "AssetFile": "css/app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3376"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}, {"Name": "label", "Value": "css/app.css"}]}, {"Route": "css/app.e5tk7yf482.css.gz", "AssetFile": "css/app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1797"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "integrity", "Value": "sha256-ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A="}, {"Name": "label", "Value": "css/app.css.gz"}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css", "AssetFile": "css/bootstrap/bootstrap.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041912905"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23858"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8=\""}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css", "AssetFile": "css/bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "162726"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css.gz", "AssetFile": "css/bootstrap/bootstrap.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23858"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "integrity", "Value": "sha256-dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.gz"}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "css/bootstrap/bootstrap.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041912905"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23858"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8=\""}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "css/bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162726"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013397104"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map.gz", "AssetFile": "css/bootstrap/bootstrap.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map.gz"}]}, {"Route": "css/bootstrap/bootstrap.min.css.gz", "AssetFile": "css/bootstrap/bootstrap.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23858"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013397104"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map.gz", "AssetFile": "css/bootstrap/bootstrap.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E="}]}, {"Route": "css/input.4jtf2pvl4b.css", "AssetFile": "css/input.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003891050584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs=\""}, {"Name": "ETag", "Value": "W/\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}, {"Name": "label", "Value": "css/input.css"}]}, {"Route": "css/input.4jtf2pvl4b.css", "AssetFile": "css/input.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "476"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}, {"Name": "label", "Value": "css/input.css"}]}, {"Route": "css/input.4jtf2pvl4b.css.gz", "AssetFile": "css/input.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "integrity", "Value": "sha256-dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs="}, {"Name": "label", "Value": "css/input.css.gz"}]}, {"Route": "css/input.css", "AssetFile": "css/input.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003891050584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs=\""}, {"Name": "ETag", "Value": "W/\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.css", "AssetFile": "css/input.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "476"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.css.gz", "AssetFile": "css/input.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs="}]}, {"Route": "css/tailwind.css", "AssetFile": "css/tailwind.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000189286390"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5282"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c=\""}, {"Name": "ETag", "Value": "W/\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.css", "AssetFile": "css/tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23768"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.css.gz", "AssetFile": "css/tailwind.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5282"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c="}]}, {"Route": "css/tailwind.onmyqsxya3.css", "AssetFile": "css/tailwind.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000189286390"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5282"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c=\""}, {"Name": "ETag", "Value": "W/\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "onmyqsxya3"}, {"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}, {"Name": "label", "Value": "css/tailwind.css"}]}, {"Route": "css/tailwind.onmyqsxya3.css", "AssetFile": "css/tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "23768"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "onmyqsxya3"}, {"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}, {"Name": "label", "Value": "css/tailwind.css"}]}, {"Route": "css/tailwind.onmyqsxya3.css.gz", "AssetFile": "css/tailwind.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5282"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "onmyqsxya3"}, {"Name": "integrity", "Value": "sha256-aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c="}, {"Name": "label", "Value": "css/tailwind.css.gz"}]}, {"Route": "images/onboarding-1.png", "AssetFile": "images/onboarding-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "52398"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"YRu27oDvSdtyiSMOuSQXP/XQ6+akSjYEsTsFh6/XwXk=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 19:36:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YRu27oDvSdtyiSMOuSQXP/XQ6+akSjYEsTsFh6/XwXk="}]}, {"Route": "images/onboarding-1.xc90gfwpea.png", "AssetFile": "images/onboarding-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "52398"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"YRu27oDvSdtyiSMOuSQXP/XQ6+akSjYEsTsFh6/XwXk=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 19:36:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xc90gfwpea"}, {"Name": "integrity", "Value": "sha256-YRu27oDvSdtyiSMOuSQXP/XQ6+akSjYEsTsFh6/XwXk="}, {"Name": "label", "Value": "images/onboarding-1.png"}]}, {"Route": "index.55aay04al6.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002309468822"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "432"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4=\""}, {"Name": "ETag", "Value": "W/\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.55aay04al6.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.55aay04al6.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "432"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "integrity", "Value": "sha256-HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002309468822"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "432"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4=\""}, {"Name": "ETag", "Value": "W/\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "432"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4="}]}]}