@page "/parentalconsent"
@inject NavigationManager Navigation

<div class="parental-consent-container">
    <!-- Header with back button -->
    <div class="header-section">
        <button class="back-button" @onclick="GoBack">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M15 18L9 12L15 6" stroke="#333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
        </button>
        <h1 class="header-title">Setup</h1>
        <div class="header-spacer"></div>
    </div>

    <!-- Illustration section -->
    <div class="illustration-section">
        <div class="image-wrapper">
            <img src="/images/parental.png" alt="Parental Consent" class="illustration-image" />
            <div class="floating-icon">
                <svg class="question-svg" width="24" height="24" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="11" fill="#4CAF50" stroke="white" stroke-width="2" stroke-dasharray="2,2" />
                    <path d="M13,19H11V17H13V19ZM15.07,11.25L14.17,12.17C13.45,12.9 13,13.5 13,15H11V14.5
                             C11,13.4 11.45,12.4 12.17,11.67L13.41,10.41C13.78,10.05 14,9.55 14,9
                             C14,7.9 13.1,7 12,7C10.9,7 10,7.9 10,9H8C8,6.79 9.79,5 12,5C14.21,5
                             16,6.79 16,9C16,9.88 15.64,10.68 15.07,11.25Z" fill="white" />
                </svg>
            </div>
        </div>
    </div>

    <!-- Content section -->
    <div class="content-section">
        <h2 class="main-title">Parental Consent Required</h2>
        <p class="description">
            We need your parent's approval before you can start using GateSale.
        </p>
    </div>

    <!-- Form -->
    <div class="form-section">
        <div class="input-group">
            <label class="input-label">Parent's Full Name</label>
            <input type="text" class="form-input" placeholder="Enter parent's full name" @bind="ParentName" />
        </div>
        <div class="input-group">
            <label class="input-label">Parent's Email Address</label>
            <input type="email" class="form-input" placeholder="<EMAIL>" @bind="ParentEmail" />
        </div>
    </div>

    <!-- Info -->
    <div class="info-section">
        <div class="info-card">
            <div class="info-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M20 6L9 17L4 12" stroke="#4CAF50" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
            </div>
            <p class="info-text">
                Your parent will receive a secure email with instructions to approve your account. This helps keep you safe online.
            </p>
        </div>
    </div>

    <!-- Button -->
    <div class="button-section">
        <button class="consent-button" @onclick="SendConsentRequest">
            Send Consent Request
        </button>
    </div>

    <!-- Footer -->
    <div class="footer-section">
        <p class="footer-text">
            By continuing, you agree to our <span class="link-text">Terms of Service</span> and <span class="link-text">Privacy Policy</span>
        </p>
    </div>
</div>

<style>
    .parental-consent-container {
        display: flex;
        flex-direction: column;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: #FFFFFF;
        min-height: 100vh;
        width: 375px;
        margin: 0 auto;
    }

    .header-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        margin-top: 40px;
        border-bottom: 1px solid #E0E0E0;
    }

    .back-button {
        background: none;
        border: none;
        padding: 8px;
        cursor: pointer;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

        .back-button:hover {
            background-color: #f5f5f5;
        }

    .header-title {
        font-size: 18px;
        font-weight: 600;
        color: #000000;
        margin: 0;
    }

    .header-spacer {
        width: 40px;
    }

    .illustration-section {
        display: flex;
        justify-content: center;
        margin: 30px 0;
    }

    .image-wrapper {
        position: relative;
        width: 190px;
        height: 190px;
        background-color: #FFE0E6;
        border-radius: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .illustration-image {
        width: 190px;
        height: auto;
        object-fit: contain;
    }

    .floating-icon {
        position: absolute;
        bottom: -10px;
        right: -10px;
        width: 40px;
        height: 40px;
        background-color: #4CAF50;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    }

    .content-section {
        text-align: center;
        padding: 0 20px;
        margin-bottom: 30px;
    }

    .main-title {
        font-size: 24px;
        font-weight: 800;
        color: #000000;
        margin-bottom: 12px;
    }

    .description {
        font-size: 16px;
        color: #666666;
        margin: 0;
    }

    .form-section {
        padding: 0 20px;
        display: flex;
        flex-direction: column;
        gap: 20px;
        margin-bottom: 20px;
    }

    .input-group {
        display: flex;
        flex-direction: column;
    }

    .input-label {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 6px;
    }

    .form-input {
        height: 48px;
        padding: 12px 16px;
        border: 1px solid #E0E0E0;
        border-radius: 8px;
        font-size: 16px;
        background-color: #FAFAFA;
    }

        .form-input:focus {
            outline: none;
            border-color: #00BFFF;
            background-color: #fff;
            box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.1);
        }

        .form-input::placeholder {
            color: #AAAAAA;
        }

    .info-section {
        padding: 0 20px;
        margin-bottom: 30px;
    }

    .info-card {
        background: #F0FDF4;
        border: 1px solid #BBF7D0;
        border-radius: 12px;
        padding: 16px;
        display: flex;
        gap: 12px;
    }

    .info-text {
        font-size: 14px;
        color: black;
    }

    .button-section {
        padding: 0 20px;
        margin-bottom: 20px;
    }

    .consent-button {
        width: 100%;
        height: 50px;
        background-color: #00BFFF;
        color: white;
        border: none;
        border-radius: 15px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        box-shadow: 0 3px 8px rgba(0, 191, 255, 0.3);
    }

       
    .footer-section {
        padding: 0 20px 40px 20px;
        text-align: center;
    }

    .footer-text {
        font-size: 12px;
        color: #999999;
    }

    .link-text {
        color: #00BFFF;
        cursor: pointer;
    }

        .link-text:hover {
            text-decoration: underline;
        }
</style>

@code {
    private string ParentName = "";
    private string ParentEmail = "";

    private void GoBack()
    {
        Navigation.NavigateTo("/");
    }

    private void SendConsentRequest()
    {
        Navigation.NavigateTo("/almostthere");
    }
}
