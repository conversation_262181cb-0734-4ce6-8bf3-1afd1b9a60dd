/* AUTO-GENERATED FILE. DO NOT MODIFY.
 *
 * This class was automatically generated by
 * .NET for Android from the resource data it found.
 * It should not be modified by hand.
 */
package androidx.appcompat;

public final class R {
	public static final class anim {
		public static final int abc_fade_in = 0x7f010000;
		public static final int abc_fade_out = 0x7f010001;
		public static final int abc_grow_fade_in_from_bottom = 0x7f010002;
		public static final int abc_popup_enter = 0x7f010003;
		public static final int abc_popup_exit = 0x7f010004;
		public static final int abc_shrink_fade_out_from_bottom = 0x7f010005;
		public static final int abc_slide_in_bottom = 0x7f010006;
		public static final int abc_slide_in_top = 0x7f010007;
		public static final int abc_slide_out_bottom = 0x7f010008;
		public static final int abc_slide_out_top = 0x7f010009;
		public static final int abc_tooltip_enter = 0x7f01000a;
		public static final int abc_tooltip_exit = 0x7f01000b;
		public static final int btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c;
		public static final int btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d;
		public static final int btn_checkbox_to_checked_icon_null_animation = 0x7f01000e;
		public static final int btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f;
		public static final int btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010;
		public static final int btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011;
		public static final int btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012;
		public static final int btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013;
		public static final int btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014;
		public static final int btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015;
		public static final int btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016;
		public static final int btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017;
	}
	public static final class attr {
		public static final int actionBarDivider = 0x7f030003;
		public static final int actionBarItemBackground = 0x7f030004;
		public static final int actionBarPopupTheme = 0x7f030005;
		public static final int actionBarSize = 0x7f030006;
		public static final int actionBarSplitStyle = 0x7f030007;
		public static final int actionBarStyle = 0x7f030008;
		public static final int actionBarTabBarStyle = 0x7f030009;
		public static final int actionBarTabStyle = 0x7f03000a;
		public static final int actionBarTabTextStyle = 0x7f03000b;
		public static final int actionBarTheme = 0x7f03000c;
		public static final int actionBarWidgetTheme = 0x7f03000d;
		public static final int actionButtonStyle = 0x7f03000e;
		public static final int actionDropDownStyle = 0x7f03000f;
		public static final int actionLayout = 0x7f030010;
		public static final int actionMenuTextAppearance = 0x7f030011;
		public static final int actionMenuTextColor = 0x7f030012;
		public static final int actionModeBackground = 0x7f030013;
		public static final int actionModeCloseButtonStyle = 0x7f030014;
		public static final int actionModeCloseContentDescription = 0x7f030015;
		public static final int actionModeCloseDrawable = 0x7f030016;
		public static final int actionModeCopyDrawable = 0x7f030017;
		public static final int actionModeCutDrawable = 0x7f030018;
		public static final int actionModeFindDrawable = 0x7f030019;
		public static final int actionModePasteDrawable = 0x7f03001a;
		public static final int actionModePopupWindowStyle = 0x7f03001b;
		public static final int actionModeSelectAllDrawable = 0x7f03001c;
		public static final int actionModeShareDrawable = 0x7f03001d;
		public static final int actionModeSplitBackground = 0x7f03001e;
		public static final int actionModeStyle = 0x7f03001f;
		public static final int actionModeTheme = 0x7f030020;
		public static final int actionModeWebSearchDrawable = 0x7f030021;
		public static final int actionOverflowButtonStyle = 0x7f030022;
		public static final int actionOverflowMenuStyle = 0x7f030023;
		public static final int actionProviderClass = 0x7f030024;
		public static final int actionViewClass = 0x7f030026;
		public static final int activityChooserViewStyle = 0x7f030029;
		public static final int alertDialogButtonGroupStyle = 0x7f03002c;
		public static final int alertDialogCenterButtons = 0x7f03002d;
		public static final int alertDialogStyle = 0x7f03002e;
		public static final int alertDialogTheme = 0x7f03002f;
		public static final int allowStacking = 0x7f030030;
		public static final int alphabeticModifiers = 0x7f030032;
		public static final int arrowHeadLength = 0x7f03003f;
		public static final int arrowShaftLength = 0x7f030040;
		public static final int autoCompleteTextViewStyle = 0x7f030044;
		public static final int autoSizeMaxTextSize = 0x7f030046;
		public static final int autoSizeMinTextSize = 0x7f030047;
		public static final int autoSizePresetSizes = 0x7f030048;
		public static final int autoSizeStepGranularity = 0x7f030049;
		public static final int autoSizeTextType = 0x7f03004a;
		public static final int background = 0x7f03004d;
		public static final int backgroundSplit = 0x7f030054;
		public static final int backgroundStacked = 0x7f030055;
		public static final int backgroundTint = 0x7f030056;
		public static final int backgroundTintMode = 0x7f030057;
		public static final int barLength = 0x7f030069;
		public static final int borderlessButtonStyle = 0x7f03007d;
		public static final int buttonBarButtonStyle = 0x7f030091;
		public static final int buttonBarNegativeButtonStyle = 0x7f030092;
		public static final int buttonBarNeutralButtonStyle = 0x7f030093;
		public static final int buttonBarPositiveButtonStyle = 0x7f030094;
		public static final int buttonBarStyle = 0x7f030095;
		public static final int buttonCompat = 0x7f030096;
		public static final int buttonGravity = 0x7f030097;
		public static final int buttonIconDimen = 0x7f030099;
		public static final int buttonPanelSideLayout = 0x7f03009c;
		public static final int buttonStyle = 0x7f03009d;
		public static final int buttonStyleSmall = 0x7f03009e;
		public static final int buttonTint = 0x7f03009f;
		public static final int buttonTintMode = 0x7f0300a0;
		public static final int checkMarkCompat = 0x7f0300b6;
		public static final int checkMarkTint = 0x7f0300b7;
		public static final int checkMarkTintMode = 0x7f0300b8;
		public static final int checkboxStyle = 0x7f0300b9;
		public static final int checkedTextViewStyle = 0x7f0300c4;
		public static final int closeIcon = 0x7f0300e7;
		public static final int closeItemLayout = 0x7f0300ee;
		public static final int collapseContentDescription = 0x7f0300ef;
		public static final int collapseIcon = 0x7f0300f0;
		public static final int color = 0x7f0300fb;
		public static final int colorAccent = 0x7f0300fc;
		public static final int colorBackgroundFloating = 0x7f0300fd;
		public static final int colorButtonNormal = 0x7f0300fe;
		public static final int colorControlActivated = 0x7f030100;
		public static final int colorControlHighlight = 0x7f030101;
		public static final int colorControlNormal = 0x7f030102;
		public static final int colorError = 0x7f030103;
		public static final int colorPrimary = 0x7f03011c;
		public static final int colorPrimaryDark = 0x7f03011e;
		public static final int colorSwitchThumbNormal = 0x7f030133;
		public static final int commitIcon = 0x7f030138;
		public static final int contentDescription = 0x7f030142;
		public static final int contentInsetEnd = 0x7f030143;
		public static final int contentInsetEndWithActions = 0x7f030144;
		public static final int contentInsetLeft = 0x7f030145;
		public static final int contentInsetRight = 0x7f030146;
		public static final int contentInsetStart = 0x7f030147;
		public static final int contentInsetStartWithNavigation = 0x7f030148;
		public static final int controlBackground = 0x7f030152;
		public static final int customNavigationLayout = 0x7f030171;
		public static final int defaultQueryHint = 0x7f03017e;
		public static final int dialogCornerRadius = 0x7f030185;
		public static final int dialogPreferredPadding = 0x7f030186;
		public static final int dialogTheme = 0x7f030187;
		public static final int displayOptions = 0x7f030188;
		public static final int divider = 0x7f030189;
		public static final int dividerHorizontal = 0x7f03018b;
		public static final int dividerPadding = 0x7f03018e;
		public static final int dividerVertical = 0x7f030190;
		public static final int drawableBottomCompat = 0x7f030195;
		public static final int drawableEndCompat = 0x7f030196;
		public static final int drawableLeftCompat = 0x7f030197;
		public static final int drawableRightCompat = 0x7f030198;
		public static final int drawableSize = 0x7f030199;
		public static final int drawableStartCompat = 0x7f03019a;
		public static final int drawableTint = 0x7f03019b;
		public static final int drawableTintMode = 0x7f03019c;
		public static final int drawableTopCompat = 0x7f03019d;
		public static final int drawerArrowStyle = 0x7f03019e;
		public static final int dropDownListViewStyle = 0x7f0301a2;
		public static final int dropdownListPreferredItemHeight = 0x7f0301a3;
		public static final int editTextBackground = 0x7f0301a6;
		public static final int editTextColor = 0x7f0301a7;
		public static final int editTextStyle = 0x7f0301a8;
		public static final int elevation = 0x7f0301a9;
		public static final int emojiCompatEnabled = 0x7f0301ad;
		public static final int expandActivityOverflowButtonDrawable = 0x7f0301c6;
		public static final int firstBaselineToTopHeight = 0x7f0301ea;
		public static final int fontFamily = 0x7f03020e;
		public static final int fontVariationSettings = 0x7f030218;
		public static final int gapBetweenBars = 0x7f03021e;
		public static final int goIcon = 0x7f030220;
		public static final int height = 0x7f030231;
		public static final int hideOnContentScroll = 0x7f030239;
		public static final int homeAsUpIndicator = 0x7f03023f;
		public static final int homeLayout = 0x7f030240;
		public static final int icon = 0x7f030244;
		public static final int iconTint = 0x7f03024a;
		public static final int iconTintMode = 0x7f03024b;
		public static final int iconifiedByDefault = 0x7f03024c;
		public static final int imageButtonStyle = 0x7f03024f;
		public static final int indeterminateProgressStyle = 0x7f030255;
		public static final int initialActivityCount = 0x7f03025c;
		public static final int isLightTheme = 0x7f03025e;
		public static final int itemPadding = 0x7f03026c;
		public static final int lastBaselineToBottomHeight = 0x7f030288;
		public static final int layout = 0x7f03028b;
		public static final int lineHeight = 0x7f0302d8;
		public static final int listChoiceBackgroundIndicator = 0x7f0302db;
		public static final int listChoiceIndicatorMultipleAnimated = 0x7f0302dc;
		public static final int listChoiceIndicatorSingleAnimated = 0x7f0302dd;
		public static final int listDividerAlertDialog = 0x7f0302de;
		public static final int listItemLayout = 0x7f0302df;
		public static final int listLayout = 0x7f0302e0;
		public static final int listMenuViewStyle = 0x7f0302e1;
		public static final int listPopupWindowStyle = 0x7f0302e2;
		public static final int listPreferredItemHeight = 0x7f0302e3;
		public static final int listPreferredItemHeightLarge = 0x7f0302e4;
		public static final int listPreferredItemHeightSmall = 0x7f0302e5;
		public static final int listPreferredItemPaddingEnd = 0x7f0302e6;
		public static final int listPreferredItemPaddingLeft = 0x7f0302e7;
		public static final int listPreferredItemPaddingRight = 0x7f0302e8;
		public static final int listPreferredItemPaddingStart = 0x7f0302e9;
		public static final int logo = 0x7f0302ea;
		public static final int logoDescription = 0x7f0302ec;
		public static final int maxButtonHeight = 0x7f030325;
		public static final int measureWithLargestChild = 0x7f03032d;
		public static final int menu = 0x7f03032e;
		public static final int multiChoiceItemLayout = 0x7f03036c;
		public static final int navigationContentDescription = 0x7f03036e;
		public static final int navigationIcon = 0x7f03036f;
		public static final int navigationMode = 0x7f030371;
		public static final int numericModifiers = 0x7f030379;
		public static final int overlapAnchor = 0x7f030382;
		public static final int paddingBottomNoButtons = 0x7f030384;
		public static final int paddingEnd = 0x7f030386;
		public static final int paddingStart = 0x7f030389;
		public static final int paddingTopNoTitle = 0x7f03038b;
		public static final int panelBackground = 0x7f03038d;
		public static final int panelMenuListTheme = 0x7f03038e;
		public static final int panelMenuListWidth = 0x7f03038f;
		public static final int popupMenuStyle = 0x7f0303a9;
		public static final int popupTheme = 0x7f0303aa;
		public static final int popupWindowStyle = 0x7f0303ab;
		public static final int preserveIconSpacing = 0x7f0303af;
		public static final int progressBarPadding = 0x7f0303b2;
		public static final int progressBarStyle = 0x7f0303b3;
		public static final int queryBackground = 0x7f0303b7;
		public static final int queryHint = 0x7f0303b8;
		public static final int radioButtonStyle = 0x7f0303ba;
		public static final int ratingBarStyle = 0x7f0303bc;
		public static final int ratingBarStyleIndicator = 0x7f0303bd;
		public static final int ratingBarStyleSmall = 0x7f0303be;
		public static final int searchHintIcon = 0x7f0303d6;
		public static final int searchIcon = 0x7f0303d7;
		public static final int searchViewStyle = 0x7f0303d9;
		public static final int seekBarStyle = 0x7f0303dc;
		public static final int selectableItemBackground = 0x7f0303dd;
		public static final int selectableItemBackgroundBorderless = 0x7f0303de;
		public static final int showAsAction = 0x7f0303f0;
		public static final int showDividers = 0x7f0303f2;
		public static final int showText = 0x7f0303f6;
		public static final int showTitle = 0x7f0303f7;
		public static final int singleChoiceItemLayout = 0x7f0303ff;
		public static final int spinBars = 0x7f030408;
		public static final int spinnerDropDownItemStyle = 0x7f030409;
		public static final int spinnerStyle = 0x7f03040a;
		public static final int splitTrack = 0x7f030412;
		public static final int srcCompat = 0x7f030418;
		public static final int state_above_anchor = 0x7f030424;
		public static final int subMenuArrow = 0x7f030433;
		public static final int submitBackground = 0x7f030438;
		public static final int subtitle = 0x7f030439;
		public static final int subtitleTextAppearance = 0x7f03043b;
		public static final int subtitleTextColor = 0x7f03043c;
		public static final int subtitleTextStyle = 0x7f03043d;
		public static final int suggestionRowLayout = 0x7f030441;
		public static final int switchMinWidth = 0x7f030443;
		public static final int switchPadding = 0x7f030444;
		public static final int switchStyle = 0x7f030445;
		public static final int switchTextAppearance = 0x7f030446;
		public static final int textAllCaps = 0x7f03046a;
		public static final int textAppearanceLargePopupMenu = 0x7f030481;
		public static final int textAppearanceListItem = 0x7f030483;
		public static final int textAppearanceListItemSecondary = 0x7f030484;
		public static final int textAppearanceListItemSmall = 0x7f030485;
		public static final int textAppearancePopupMenuHeader = 0x7f030487;
		public static final int textAppearanceSearchResultSubtitle = 0x7f030488;
		public static final int textAppearanceSearchResultTitle = 0x7f030489;
		public static final int textAppearanceSmallPopupMenu = 0x7f03048a;
		public static final int textColorAlertDialogListItem = 0x7f030495;
		public static final int textColorSearchUrl = 0x7f030496;
		public static final int textLocale = 0x7f0304a1;
		public static final int theme = 0x7f0304ab;
		public static final int thickness = 0x7f0304ac;
		public static final int thumbTextPadding = 0x7f0304b7;
		public static final int thumbTint = 0x7f0304b8;
		public static final int thumbTintMode = 0x7f0304b9;
		public static final int tickMark = 0x7f0304bf;
		public static final int tickMarkTint = 0x7f0304c0;
		public static final int tickMarkTintMode = 0x7f0304c1;
		public static final int tint = 0x7f0304c5;
		public static final int tintMode = 0x7f0304c6;
		public static final int title = 0x7f0304c8;
		public static final int titleMargin = 0x7f0304cc;
		public static final int titleMarginBottom = 0x7f0304cd;
		public static final int titleMarginEnd = 0x7f0304ce;
		public static final int titleMarginStart = 0x7f0304cf;
		public static final int titleMarginTop = 0x7f0304d0;
		public static final int titleMargins = 0x7f0304d1;
		public static final int titleTextAppearance = 0x7f0304d3;
		public static final int titleTextColor = 0x7f0304d4;
		public static final int titleTextStyle = 0x7f0304d6;
		public static final int toolbarNavigationButtonStyle = 0x7f0304d9;
		public static final int toolbarStyle = 0x7f0304da;
		public static final int tooltipForegroundColor = 0x7f0304dc;
		public static final int tooltipFrameBackground = 0x7f0304dd;
		public static final int tooltipText = 0x7f0304df;
		public static final int track = 0x7f0304e4;
		public static final int trackTint = 0x7f0304f0;
		public static final int trackTintMode = 0x7f0304f1;
		public static final int viewInflaterClass = 0x7f030504;
		public static final int voiceIcon = 0x7f03050a;
		public static final int windowActionBar = 0x7f030512;
		public static final int windowActionBarOverlay = 0x7f030513;
		public static final int windowActionModeOverlay = 0x7f030514;
		public static final int windowFixedHeightMajor = 0x7f030515;
		public static final int windowFixedHeightMinor = 0x7f030516;
		public static final int windowFixedWidthMajor = 0x7f030517;
		public static final int windowFixedWidthMinor = 0x7f030518;
		public static final int windowMinWidthMajor = 0x7f030519;
		public static final int windowMinWidthMinor = 0x7f03051a;
		public static final int windowNoTitle = 0x7f03051b;
	}
	public static final class bool {
		public static final int abc_action_bar_embed_tabs = 0x7f040000;
		public static final int abc_config_actionMenuItemAllCaps = 0x7f040001;
	}
	public static final class color {
		public static final int abc_background_cache_hint_selector_material_dark = 0x7f050000;
		public static final int abc_background_cache_hint_selector_material_light = 0x7f050001;
		public static final int abc_btn_colored_borderless_text_material = 0x7f050002;
		public static final int abc_btn_colored_text_material = 0x7f050003;
		public static final int abc_color_highlight_material = 0x7f050004;
		public static final int abc_decor_view_status_guard = 0x7f050005;
		public static final int abc_decor_view_status_guard_light = 0x7f050006;
		public static final int abc_hint_foreground_material_dark = 0x7f050007;
		public static final int abc_hint_foreground_material_light = 0x7f050008;
		public static final int abc_primary_text_disable_only_material_dark = 0x7f050009;
		public static final int abc_primary_text_disable_only_material_light = 0x7f05000a;
		public static final int abc_primary_text_material_dark = 0x7f05000b;
		public static final int abc_primary_text_material_light = 0x7f05000c;
		public static final int abc_search_url_text = 0x7f05000d;
		public static final int abc_search_url_text_normal = 0x7f05000e;
		public static final int abc_search_url_text_pressed = 0x7f05000f;
		public static final int abc_search_url_text_selected = 0x7f050010;
		public static final int abc_secondary_text_material_dark = 0x7f050011;
		public static final int abc_secondary_text_material_light = 0x7f050012;
		public static final int abc_tint_btn_checkable = 0x7f050013;
		public static final int abc_tint_default = 0x7f050014;
		public static final int abc_tint_edittext = 0x7f050015;
		public static final int abc_tint_seek_thumb = 0x7f050016;
		public static final int abc_tint_spinner = 0x7f050017;
		public static final int abc_tint_switch_track = 0x7f050018;
		public static final int accent_material_dark = 0x7f050019;
		public static final int accent_material_light = 0x7f05001a;
		public static final int background_floating_material_dark = 0x7f05001d;
		public static final int background_floating_material_light = 0x7f05001e;
		public static final int background_material_dark = 0x7f05001f;
		public static final int background_material_light = 0x7f050020;
		public static final int bright_foreground_disabled_material_dark = 0x7f050021;
		public static final int bright_foreground_disabled_material_light = 0x7f050022;
		public static final int bright_foreground_inverse_material_dark = 0x7f050023;
		public static final int bright_foreground_inverse_material_light = 0x7f050024;
		public static final int bright_foreground_material_dark = 0x7f050025;
		public static final int bright_foreground_material_light = 0x7f050026;
		public static final int button_material_dark = 0x7f05002b;
		public static final int button_material_light = 0x7f05002c;
		public static final int dim_foreground_disabled_material_dark = 0x7f05005d;
		public static final int dim_foreground_disabled_material_light = 0x7f05005e;
		public static final int dim_foreground_material_dark = 0x7f05005f;
		public static final int dim_foreground_material_light = 0x7f050060;
		public static final int error_color_material_dark = 0x7f050061;
		public static final int error_color_material_light = 0x7f050062;
		public static final int foreground_material_dark = 0x7f050063;
		public static final int foreground_material_light = 0x7f050064;
		public static final int highlighted_text_material_dark = 0x7f050065;
		public static final int highlighted_text_material_light = 0x7f050066;
		public static final int material_blue_grey_800 = 0x7f05021a;
		public static final int material_blue_grey_900 = 0x7f05021b;
		public static final int material_blue_grey_950 = 0x7f05021c;
		public static final int material_deep_teal_200 = 0x7f05021e;
		public static final int material_deep_teal_500 = 0x7f05021f;
		public static final int material_grey_100 = 0x7f05026a;
		public static final int material_grey_300 = 0x7f05026b;
		public static final int material_grey_50 = 0x7f05026c;
		public static final int material_grey_600 = 0x7f05026d;
		public static final int material_grey_800 = 0x7f05026e;
		public static final int material_grey_850 = 0x7f05026f;
		public static final int material_grey_900 = 0x7f050270;
		public static final int primary_dark_material_dark = 0x7f0502f8;
		public static final int primary_dark_material_light = 0x7f0502f9;
		public static final int primary_material_dark = 0x7f0502fa;
		public static final int primary_material_light = 0x7f0502fb;
		public static final int primary_text_default_material_dark = 0x7f0502fc;
		public static final int primary_text_default_material_light = 0x7f0502fd;
		public static final int primary_text_disabled_material_dark = 0x7f0502fe;
		public static final int primary_text_disabled_material_light = 0x7f0502ff;
		public static final int ripple_material_dark = 0x7f050300;
		public static final int ripple_material_light = 0x7f050301;
		public static final int secondary_text_default_material_dark = 0x7f050302;
		public static final int secondary_text_default_material_light = 0x7f050303;
		public static final int secondary_text_disabled_material_dark = 0x7f050304;
		public static final int secondary_text_disabled_material_light = 0x7f050305;
		public static final int switch_thumb_disabled_material_dark = 0x7f050306;
		public static final int switch_thumb_disabled_material_light = 0x7f050307;
		public static final int switch_thumb_material_dark = 0x7f050308;
		public static final int switch_thumb_material_light = 0x7f050309;
		public static final int switch_thumb_normal_material_dark = 0x7f05030a;
		public static final int switch_thumb_normal_material_light = 0x7f05030b;
		public static final int tooltip_background_dark = 0x7f05030c;
		public static final int tooltip_background_light = 0x7f05030d;
	}
	public static final class dimen {
		public static final int abc_action_bar_content_inset_material = 0x7f060000;
		public static final int abc_action_bar_content_inset_with_nav = 0x7f060001;
		public static final int abc_action_bar_default_height_material = 0x7f060002;
		public static final int abc_action_bar_default_padding_end_material = 0x7f060003;
		public static final int abc_action_bar_default_padding_start_material = 0x7f060004;
		public static final int abc_action_bar_elevation_material = 0x7f060005;
		public static final int abc_action_bar_icon_vertical_padding_material = 0x7f060006;
		public static final int abc_action_bar_overflow_padding_end_material = 0x7f060007;
		public static final int abc_action_bar_overflow_padding_start_material = 0x7f060008;
		public static final int abc_action_bar_stacked_max_height = 0x7f060009;
		public static final int abc_action_bar_stacked_tab_max_width = 0x7f06000a;
		public static final int abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b;
		public static final int abc_action_bar_subtitle_top_margin_material = 0x7f06000c;
		public static final int abc_action_button_min_height_material = 0x7f06000d;
		public static final int abc_action_button_min_width_material = 0x7f06000e;
		public static final int abc_action_button_min_width_overflow_material = 0x7f06000f;
		public static final int abc_alert_dialog_button_bar_height = 0x7f060010;
		public static final int abc_alert_dialog_button_dimen = 0x7f060011;
		public static final int abc_button_inset_horizontal_material = 0x7f060012;
		public static final int abc_button_inset_vertical_material = 0x7f060013;
		public static final int abc_button_padding_horizontal_material = 0x7f060014;
		public static final int abc_button_padding_vertical_material = 0x7f060015;
		public static final int abc_cascading_menus_min_smallest_width = 0x7f060016;
		public static final int abc_config_prefDialogWidth = 0x7f060017;
		public static final int abc_control_corner_material = 0x7f060018;
		public static final int abc_control_inset_material = 0x7f060019;
		public static final int abc_control_padding_material = 0x7f06001a;
		public static final int abc_dialog_corner_radius_material = 0x7f06001b;
		public static final int abc_dialog_fixed_height_major = 0x7f06001c;
		public static final int abc_dialog_fixed_height_minor = 0x7f06001d;
		public static final int abc_dialog_fixed_width_major = 0x7f06001e;
		public static final int abc_dialog_fixed_width_minor = 0x7f06001f;
		public static final int abc_dialog_list_padding_bottom_no_buttons = 0x7f060020;
		public static final int abc_dialog_list_padding_top_no_title = 0x7f060021;
		public static final int abc_dialog_min_width_major = 0x7f060022;
		public static final int abc_dialog_min_width_minor = 0x7f060023;
		public static final int abc_dialog_padding_material = 0x7f060024;
		public static final int abc_dialog_padding_top_material = 0x7f060025;
		public static final int abc_dialog_title_divider_material = 0x7f060026;
		public static final int abc_disabled_alpha_material_dark = 0x7f060027;
		public static final int abc_disabled_alpha_material_light = 0x7f060028;
		public static final int abc_dropdownitem_icon_width = 0x7f060029;
		public static final int abc_dropdownitem_text_padding_left = 0x7f06002a;
		public static final int abc_dropdownitem_text_padding_right = 0x7f06002b;
		public static final int abc_edit_text_inset_bottom_material = 0x7f06002c;
		public static final int abc_edit_text_inset_horizontal_material = 0x7f06002d;
		public static final int abc_edit_text_inset_top_material = 0x7f06002e;
		public static final int abc_floating_window_z = 0x7f06002f;
		public static final int abc_list_item_height_large_material = 0x7f060030;
		public static final int abc_list_item_height_material = 0x7f060031;
		public static final int abc_list_item_height_small_material = 0x7f060032;
		public static final int abc_list_item_padding_horizontal_material = 0x7f060033;
		public static final int abc_panel_menu_list_width = 0x7f060034;
		public static final int abc_progress_bar_height_material = 0x7f060035;
		public static final int abc_search_view_preferred_height = 0x7f060036;
		public static final int abc_search_view_preferred_width = 0x7f060037;
		public static final int abc_seekbar_track_background_height_material = 0x7f060038;
		public static final int abc_seekbar_track_progress_height_material = 0x7f060039;
		public static final int abc_select_dialog_padding_start_material = 0x7f06003a;
		public static final int abc_star_big = 0x7f06003b;
		public static final int abc_star_medium = 0x7f06003c;
		public static final int abc_star_small = 0x7f06003d;
		public static final int abc_switch_padding = 0x7f06003e;
		public static final int abc_text_size_body_1_material = 0x7f06003f;
		public static final int abc_text_size_body_2_material = 0x7f060040;
		public static final int abc_text_size_button_material = 0x7f060041;
		public static final int abc_text_size_caption_material = 0x7f060042;
		public static final int abc_text_size_display_1_material = 0x7f060043;
		public static final int abc_text_size_display_2_material = 0x7f060044;
		public static final int abc_text_size_display_3_material = 0x7f060045;
		public static final int abc_text_size_display_4_material = 0x7f060046;
		public static final int abc_text_size_headline_material = 0x7f060047;
		public static final int abc_text_size_large_material = 0x7f060048;
		public static final int abc_text_size_medium_material = 0x7f060049;
		public static final int abc_text_size_menu_header_material = 0x7f06004a;
		public static final int abc_text_size_menu_material = 0x7f06004b;
		public static final int abc_text_size_small_material = 0x7f06004c;
		public static final int abc_text_size_subhead_material = 0x7f06004d;
		public static final int abc_text_size_subtitle_material_toolbar = 0x7f06004e;
		public static final int abc_text_size_title_material = 0x7f06004f;
		public static final int abc_text_size_title_material_toolbar = 0x7f060050;
		public static final int disabled_alpha_material_dark = 0x7f060090;
		public static final int disabled_alpha_material_light = 0x7f060091;
		public static final int highlight_alpha_material_colored = 0x7f060095;
		public static final int highlight_alpha_material_dark = 0x7f060096;
		public static final int highlight_alpha_material_light = 0x7f060097;
		public static final int hint_alpha_material_dark = 0x7f060098;
		public static final int hint_alpha_material_light = 0x7f060099;
		public static final int hint_pressed_alpha_material_dark = 0x7f06009a;
		public static final int hint_pressed_alpha_material_light = 0x7f06009b;
		public static final int tooltip_corner_radius = 0x7f06031e;
		public static final int tooltip_horizontal_padding = 0x7f06031f;
		public static final int tooltip_margin = 0x7f060320;
		public static final int tooltip_precise_anchor_extra_offset = 0x7f060321;
		public static final int tooltip_precise_anchor_threshold = 0x7f060322;
		public static final int tooltip_vertical_padding = 0x7f060323;
		public static final int tooltip_y_offset_non_touch = 0x7f060324;
		public static final int tooltip_y_offset_touch = 0x7f060325;
	}
	public static final class drawable {
		public static final int abc_ab_share_pack_mtrl_alpha = 0x7f070028;
		public static final int abc_action_bar_item_background_material = 0x7f070029;
		public static final int abc_btn_borderless_material = 0x7f07002a;
		public static final int abc_btn_check_material = 0x7f07002b;
		public static final int abc_btn_check_material_anim = 0x7f07002c;
		public static final int abc_btn_check_to_on_mtrl_000 = 0x7f07002d;
		public static final int abc_btn_check_to_on_mtrl_015 = 0x7f07002e;
		public static final int abc_btn_colored_material = 0x7f07002f;
		public static final int abc_btn_default_mtrl_shape = 0x7f070030;
		public static final int abc_btn_radio_material = 0x7f070031;
		public static final int abc_btn_radio_material_anim = 0x7f070032;
		public static final int abc_btn_radio_to_on_mtrl_000 = 0x7f070033;
		public static final int abc_btn_radio_to_on_mtrl_015 = 0x7f070034;
		public static final int abc_btn_switch_to_on_mtrl_00001 = 0x7f070035;
		public static final int abc_btn_switch_to_on_mtrl_00012 = 0x7f070036;
		public static final int abc_cab_background_internal_bg = 0x7f070037;
		public static final int abc_cab_background_top_material = 0x7f070038;
		public static final int abc_cab_background_top_mtrl_alpha = 0x7f070039;
		public static final int abc_control_background_material = 0x7f07003a;
		public static final int abc_dialog_material_background = 0x7f07003b;
		public static final int abc_edit_text_material = 0x7f07003c;
		public static final int abc_ic_ab_back_material = 0x7f07003d;
		public static final int abc_ic_arrow_drop_right_black_24dp = 0x7f07003e;
		public static final int abc_ic_clear_material = 0x7f07003f;
		public static final int abc_ic_commit_search_api_mtrl_alpha = 0x7f070040;
		public static final int abc_ic_go_search_api_material = 0x7f070041;
		public static final int abc_ic_menu_copy_mtrl_am_alpha = 0x7f070042;
		public static final int abc_ic_menu_cut_mtrl_alpha = 0x7f070043;
		public static final int abc_ic_menu_overflow_material = 0x7f070044;
		public static final int abc_ic_menu_paste_mtrl_am_alpha = 0x7f070045;
		public static final int abc_ic_menu_selectall_mtrl_alpha = 0x7f070046;
		public static final int abc_ic_menu_share_mtrl_alpha = 0x7f070047;
		public static final int abc_ic_search_api_material = 0x7f070048;
		public static final int abc_ic_voice_search_api_material = 0x7f070049;
		public static final int abc_item_background_holo_dark = 0x7f07004a;
		public static final int abc_item_background_holo_light = 0x7f07004b;
		public static final int abc_list_divider_material = 0x7f07004c;
		public static final int abc_list_divider_mtrl_alpha = 0x7f07004d;
		public static final int abc_list_focused_holo = 0x7f07004e;
		public static final int abc_list_longpressed_holo = 0x7f07004f;
		public static final int abc_list_pressed_holo_dark = 0x7f070050;
		public static final int abc_list_pressed_holo_light = 0x7f070051;
		public static final int abc_list_selector_background_transition_holo_dark = 0x7f070052;
		public static final int abc_list_selector_background_transition_holo_light = 0x7f070053;
		public static final int abc_list_selector_disabled_holo_dark = 0x7f070054;
		public static final int abc_list_selector_disabled_holo_light = 0x7f070055;
		public static final int abc_list_selector_holo_dark = 0x7f070056;
		public static final int abc_list_selector_holo_light = 0x7f070057;
		public static final int abc_menu_hardkey_panel_mtrl_mult = 0x7f070058;
		public static final int abc_popup_background_mtrl_mult = 0x7f070059;
		public static final int abc_ratingbar_indicator_material = 0x7f07005a;
		public static final int abc_ratingbar_material = 0x7f07005b;
		public static final int abc_ratingbar_small_material = 0x7f07005c;
		public static final int abc_scrubber_control_off_mtrl_alpha = 0x7f07005d;
		public static final int abc_scrubber_control_to_pressed_mtrl_000 = 0x7f07005e;
		public static final int abc_scrubber_control_to_pressed_mtrl_005 = 0x7f07005f;
		public static final int abc_scrubber_primary_mtrl_alpha = 0x7f070060;
		public static final int abc_scrubber_track_mtrl_alpha = 0x7f070061;
		public static final int abc_seekbar_thumb_material = 0x7f070062;
		public static final int abc_seekbar_tick_mark_material = 0x7f070063;
		public static final int abc_seekbar_track_material = 0x7f070064;
		public static final int abc_spinner_mtrl_am_alpha = 0x7f070065;
		public static final int abc_spinner_textfield_background_material = 0x7f070066;
		public static final int abc_star_black_48dp = 0x7f070067;
		public static final int abc_star_half_black_48dp = 0x7f070068;
		public static final int abc_switch_thumb_material = 0x7f070069;
		public static final int abc_switch_track_mtrl_alpha = 0x7f07006a;
		public static final int abc_tab_indicator_material = 0x7f07006b;
		public static final int abc_tab_indicator_mtrl_alpha = 0x7f07006c;
		public static final int abc_text_cursor_material = 0x7f07006d;
		public static final int abc_text_select_handle_left_mtrl = 0x7f07006e;
		public static final int abc_text_select_handle_middle_mtrl = 0x7f07006f;
		public static final int abc_text_select_handle_right_mtrl = 0x7f070070;
		public static final int abc_textfield_activated_mtrl_alpha = 0x7f070071;
		public static final int abc_textfield_default_mtrl_alpha = 0x7f070072;
		public static final int abc_textfield_search_activated_mtrl_alpha = 0x7f070073;
		public static final int abc_textfield_search_default_mtrl_alpha = 0x7f070074;
		public static final int abc_textfield_search_material = 0x7f070075;
		public static final int btn_checkbox_checked_mtrl = 0x7f070079;
		public static final int btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f07007a;
		public static final int btn_checkbox_unchecked_mtrl = 0x7f07007b;
		public static final int btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f07007c;
		public static final int btn_radio_off_mtrl = 0x7f07007d;
		public static final int btn_radio_off_to_on_mtrl_animation = 0x7f07007e;
		public static final int btn_radio_on_mtrl = 0x7f07007f;
		public static final int btn_radio_on_to_off_mtrl_animation = 0x7f070080;
		public static final int test_level_drawable = 0x7f0700e4;
		public static final int tooltip_frame_dark = 0x7f0700e5;
		public static final int tooltip_frame_light = 0x7f0700e6;
	}
	public static final class id {
		public static final int action_bar = 0x7f080034;
		public static final int action_bar_activity_content = 0x7f080035;
		public static final int action_bar_container = 0x7f080036;
		public static final int action_bar_root = 0x7f080037;
		public static final int action_bar_spinner = 0x7f080038;
		public static final int action_bar_subtitle = 0x7f080039;
		public static final int action_bar_title = 0x7f08003a;
		public static final int action_context_bar = 0x7f08003c;
		public static final int action_menu_divider = 0x7f08003f;
		public static final int action_menu_presenter = 0x7f080040;
		public static final int action_mode_bar = 0x7f080041;
		public static final int action_mode_bar_stub = 0x7f080042;
		public static final int action_mode_close_button = 0x7f080043;
		public static final int activity_chooser_view_content = 0x7f080046;
		public static final int add = 0x7f080047;
		public static final int alertTitle = 0x7f080049;
		public static final int buttonPanel = 0x7f08006f;
		public static final int checkbox = 0x7f08007c;
		public static final int checked = 0x7f08007d;
		public static final int content = 0x7f08008a;
		public static final int contentPanel = 0x7f08008b;
		public static final int custom = 0x7f080093;
		public static final int customPanel = 0x7f080094;
		public static final int decor_content_parent = 0x7f080099;
		public static final int default_activity_button = 0x7f08009a;
		public static final int edit_query = 0x7f0800b8;
		public static final int expand_activities_button = 0x7f0800c2;
		public static final int expanded_menu = 0x7f0800c3;
		public static final int group_divider = 0x7f0800dc;
		public static final int home = 0x7f0800e2;
		public static final int icon = 0x7f0800e7;
		public static final int image = 0x7f0800ec;
		public static final int listMode = 0x7f080100;
		public static final int list_item = 0x7f080101;
		public static final int message = 0x7f08011f;
		public static final int multiply = 0x7f080140;
		public static final int none = 0x7f080155;
		public static final int normal = 0x7f080156;
		public static final int off = 0x7f08015b;
		public static final int on = 0x7f08015c;
		public static final int parentPanel = 0x7f080172;
		public static final int progress_circular = 0x7f08017f;
		public static final int progress_horizontal = 0x7f080180;
		public static final int radio = 0x7f080181;
		public static final int screen = 0x7f080191;
		public static final int scrollIndicatorDown = 0x7f080193;
		public static final int scrollIndicatorUp = 0x7f080194;
		public static final int scrollView = 0x7f080195;
		public static final int search_badge = 0x7f080197;
		public static final int search_bar = 0x7f080198;
		public static final int search_button = 0x7f080199;
		public static final int search_close_btn = 0x7f08019a;
		public static final int search_edit_frame = 0x7f08019b;
		public static final int search_go_btn = 0x7f08019c;
		public static final int search_mag_icon = 0x7f08019d;
		public static final int search_plate = 0x7f08019e;
		public static final int search_src_text = 0x7f08019f;
		public static final int search_voice_btn = 0x7f0801a0;
		public static final int select_dialog_listview = 0x7f0801a1;
		public static final int shortcut = 0x7f0801a7;
		public static final int spacer = 0x7f0801b6;
		public static final int split_action_bar = 0x7f0801b9;
		public static final int src_atop = 0x7f0801be;
		public static final int src_in = 0x7f0801bf;
		public static final int src_over = 0x7f0801c0;
		public static final int submenuarrow = 0x7f0801ca;
		public static final int submit_area = 0x7f0801cb;
		public static final int tabMode = 0x7f0801cd;
		public static final int textSpacerNoButtons = 0x7f0801de;
		public static final int textSpacerNoTitle = 0x7f0801df;
		public static final int title = 0x7f0801ec;
		public static final int titleDividerNoCustom = 0x7f0801ed;
		public static final int title_template = 0x7f0801ee;
		public static final int topPanel = 0x7f0801f1;
		public static final int unchecked = 0x7f0801ff;
		public static final int uniform = 0x7f080200;
		public static final int up = 0x7f080202;
		public static final int wrap_content = 0x7f080213;
	}
	public static final class integer {
		public static final int abc_config_activityDefaultDur = 0x7f090000;
		public static final int abc_config_activityShortDur = 0x7f090001;
		public static final int cancel_button_image_alpha = 0x7f090004;
		public static final int config_tooltipAnimTime = 0x7f090006;
	}
	public static final class interpolator {
		public static final int btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0a0000;
		public static final int btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0a0001;
		public static final int btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0a0002;
		public static final int btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0a0003;
		public static final int btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0a0004;
		public static final int btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0a0005;
		public static final int fast_out_slow_in = 0x7f0a0006;
	}
	public static final class layout {
		public static final int abc_action_bar_title_item = 0x7f0b0000;
		public static final int abc_action_bar_up_container = 0x7f0b0001;
		public static final int abc_action_menu_item_layout = 0x7f0b0002;
		public static final int abc_action_menu_layout = 0x7f0b0003;
		public static final int abc_action_mode_bar = 0x7f0b0004;
		public static final int abc_action_mode_close_item_material = 0x7f0b0005;
		public static final int abc_activity_chooser_view = 0x7f0b0006;
		public static final int abc_activity_chooser_view_list_item = 0x7f0b0007;
		public static final int abc_alert_dialog_button_bar_material = 0x7f0b0008;
		public static final int abc_alert_dialog_material = 0x7f0b0009;
		public static final int abc_alert_dialog_title_material = 0x7f0b000a;
		public static final int abc_cascading_menu_item_layout = 0x7f0b000b;
		public static final int abc_dialog_title_material = 0x7f0b000c;
		public static final int abc_expanded_menu_layout = 0x7f0b000d;
		public static final int abc_list_menu_item_checkbox = 0x7f0b000e;
		public static final int abc_list_menu_item_icon = 0x7f0b000f;
		public static final int abc_list_menu_item_layout = 0x7f0b0010;
		public static final int abc_list_menu_item_radio = 0x7f0b0011;
		public static final int abc_popup_menu_header_item_layout = 0x7f0b0012;
		public static final int abc_popup_menu_item_layout = 0x7f0b0013;
		public static final int abc_screen_content_include = 0x7f0b0014;
		public static final int abc_screen_simple = 0x7f0b0015;
		public static final int abc_screen_simple_overlay_action_mode = 0x7f0b0016;
		public static final int abc_screen_toolbar = 0x7f0b0017;
		public static final int abc_search_dropdown_item_icons_2line = 0x7f0b0018;
		public static final int abc_search_view = 0x7f0b0019;
		public static final int abc_select_dialog_material = 0x7f0b001a;
		public static final int abc_tooltip = 0x7f0b001b;
		public static final int select_dialog_item_material = 0x7f0b006d;
		public static final int select_dialog_multichoice_material = 0x7f0b006e;
		public static final int select_dialog_singlechoice_material = 0x7f0b006f;
		public static final int support_simple_spinner_dropdown_item = 0x7f0b0071;
	}
	public static final class string {
		public static final int abc_action_bar_home_description = 0x7f0f0000;
		public static final int abc_action_bar_up_description = 0x7f0f0001;
		public static final int abc_action_menu_overflow_description = 0x7f0f0002;
		public static final int abc_action_mode_done = 0x7f0f0003;
		public static final int abc_activity_chooser_view_see_all = 0x7f0f0004;
		public static final int abc_activitychooserview_choose_application = 0x7f0f0005;
		public static final int abc_capital_off = 0x7f0f0006;
		public static final int abc_capital_on = 0x7f0f0007;
		public static final int abc_menu_alt_shortcut_label = 0x7f0f0008;
		public static final int abc_menu_ctrl_shortcut_label = 0x7f0f0009;
		public static final int abc_menu_delete_shortcut_label = 0x7f0f000a;
		public static final int abc_menu_enter_shortcut_label = 0x7f0f000b;
		public static final int abc_menu_function_shortcut_label = 0x7f0f000c;
		public static final int abc_menu_meta_shortcut_label = 0x7f0f000d;
		public static final int abc_menu_shift_shortcut_label = 0x7f0f000e;
		public static final int abc_menu_space_shortcut_label = 0x7f0f000f;
		public static final int abc_menu_sym_shortcut_label = 0x7f0f0010;
		public static final int abc_prepend_shortcut_label = 0x7f0f0011;
		public static final int abc_search_hint = 0x7f0f0012;
		public static final int abc_searchview_description_clear = 0x7f0f0013;
		public static final int abc_searchview_description_query = 0x7f0f0014;
		public static final int abc_searchview_description_search = 0x7f0f0015;
		public static final int abc_searchview_description_submit = 0x7f0f0016;
		public static final int abc_searchview_description_voice = 0x7f0f0017;
		public static final int abc_shareactionprovider_share_with = 0x7f0f0018;
		public static final int abc_shareactionprovider_share_with_application = 0x7f0f0019;
		public static final int abc_toolbar_collapse_description = 0x7f0f001a;
		public static final int search_menu_title = 0x7f0f00a8;
	}
	public static final class style {
		public static final int AlertDialog_AppCompat = 0x7f100001;
		public static final int AlertDialog_AppCompat_Light = 0x7f100002;
		public static final int Animation_AppCompat_Dialog = 0x7f100003;
		public static final int Animation_AppCompat_DropDownUp = 0x7f100004;
		public static final int Animation_AppCompat_Tooltip = 0x7f100005;
		public static final int Base_AlertDialog_AppCompat = 0x7f10000e;
		public static final int Base_AlertDialog_AppCompat_Light = 0x7f10000f;
		public static final int Base_Animation_AppCompat_Dialog = 0x7f100010;
		public static final int Base_Animation_AppCompat_DropDownUp = 0x7f100011;
		public static final int Base_Animation_AppCompat_Tooltip = 0x7f100012;
		public static final int Base_DialogWindowTitleBackground_AppCompat = 0x7f100015;
		public static final int Base_DialogWindowTitle_AppCompat = 0x7f100014;
		public static final int Base_TextAppearance_AppCompat = 0x7f100019;
		public static final int Base_TextAppearance_AppCompat_Body1 = 0x7f10001a;
		public static final int Base_TextAppearance_AppCompat_Body2 = 0x7f10001b;
		public static final int Base_TextAppearance_AppCompat_Button = 0x7f10001c;
		public static final int Base_TextAppearance_AppCompat_Caption = 0x7f10001d;
		public static final int Base_TextAppearance_AppCompat_Display1 = 0x7f10001e;
		public static final int Base_TextAppearance_AppCompat_Display2 = 0x7f10001f;
		public static final int Base_TextAppearance_AppCompat_Display3 = 0x7f100020;
		public static final int Base_TextAppearance_AppCompat_Display4 = 0x7f100021;
		public static final int Base_TextAppearance_AppCompat_Headline = 0x7f100022;
		public static final int Base_TextAppearance_AppCompat_Inverse = 0x7f100023;
		public static final int Base_TextAppearance_AppCompat_Large = 0x7f100024;
		public static final int Base_TextAppearance_AppCompat_Large_Inverse = 0x7f100025;
		public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f100026;
		public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f100027;
		public static final int Base_TextAppearance_AppCompat_Medium = 0x7f100028;
		public static final int Base_TextAppearance_AppCompat_Medium_Inverse = 0x7f100029;
		public static final int Base_TextAppearance_AppCompat_Menu = 0x7f10002a;
		public static final int Base_TextAppearance_AppCompat_SearchResult = 0x7f10002b;
		public static final int Base_TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f10002c;
		public static final int Base_TextAppearance_AppCompat_SearchResult_Title = 0x7f10002d;
		public static final int Base_TextAppearance_AppCompat_Small = 0x7f10002e;
		public static final int Base_TextAppearance_AppCompat_Small_Inverse = 0x7f10002f;
		public static final int Base_TextAppearance_AppCompat_Subhead = 0x7f100030;
		public static final int Base_TextAppearance_AppCompat_Subhead_Inverse = 0x7f100031;
		public static final int Base_TextAppearance_AppCompat_Title = 0x7f100032;
		public static final int Base_TextAppearance_AppCompat_Title_Inverse = 0x7f100033;
		public static final int Base_TextAppearance_AppCompat_Tooltip = 0x7f100034;
		public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f100035;
		public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f100036;
		public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f100037;
		public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f100038;
		public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f100039;
		public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f10003a;
		public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f10003b;
		public static final int Base_TextAppearance_AppCompat_Widget_Button = 0x7f10003c;
		public static final int Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f10003d;
		public static final int Base_TextAppearance_AppCompat_Widget_Button_Colored = 0x7f10003e;
		public static final int Base_TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f10003f;
		public static final int Base_TextAppearance_AppCompat_Widget_DropDownItem = 0x7f100040;
		public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f100041;
		public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f100042;
		public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f100043;
		public static final int Base_TextAppearance_AppCompat_Widget_Switch = 0x7f100044;
		public static final int Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f100045;
		public static final int Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f10004b;
		public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f10004c;
		public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f10004d;
		public static final int Base_ThemeOverlay_AppCompat = 0x7f10007b;
		public static final int Base_ThemeOverlay_AppCompat_ActionBar = 0x7f10007c;
		public static final int Base_ThemeOverlay_AppCompat_Dark = 0x7f10007d;
		public static final int Base_ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f10007e;
		public static final int Base_ThemeOverlay_AppCompat_Dialog = 0x7f10007f;
		public static final int Base_ThemeOverlay_AppCompat_Dialog_Alert = 0x7f100080;
		public static final int Base_ThemeOverlay_AppCompat_Light = 0x7f100081;
		public static final int Base_Theme_AppCompat = 0x7f10004e;
		public static final int Base_Theme_AppCompat_CompactMenu = 0x7f10004f;
		public static final int Base_Theme_AppCompat_Dialog = 0x7f100050;
		public static final int Base_Theme_AppCompat_DialogWhenLarge = 0x7f100054;
		public static final int Base_Theme_AppCompat_Dialog_Alert = 0x7f100051;
		public static final int Base_Theme_AppCompat_Dialog_FixedSize = 0x7f100052;
		public static final int Base_Theme_AppCompat_Dialog_MinWidth = 0x7f100053;
		public static final int Base_Theme_AppCompat_Light = 0x7f100055;
		public static final int Base_Theme_AppCompat_Light_DarkActionBar = 0x7f100056;
		public static final int Base_Theme_AppCompat_Light_Dialog = 0x7f100057;
		public static final int Base_Theme_AppCompat_Light_DialogWhenLarge = 0x7f10005b;
		public static final int Base_Theme_AppCompat_Light_Dialog_Alert = 0x7f100058;
		public static final int Base_Theme_AppCompat_Light_Dialog_FixedSize = 0x7f100059;
		public static final int Base_Theme_AppCompat_Light_Dialog_MinWidth = 0x7f10005a;
		public static final int Base_V21_ThemeOverlay_AppCompat_Dialog = 0x7f1000ac;
		public static final int Base_V21_Theme_AppCompat = 0x7f1000a4;
		public static final int Base_V21_Theme_AppCompat_Dialog = 0x7f1000a5;
		public static final int Base_V21_Theme_AppCompat_Light = 0x7f1000a6;
		public static final int Base_V21_Theme_AppCompat_Light_Dialog = 0x7f1000a7;
		public static final int Base_V22_Theme_AppCompat = 0x7f1000b0;
		public static final int Base_V22_Theme_AppCompat_Light = 0x7f1000b1;
		public static final int Base_V23_Theme_AppCompat = 0x7f1000b2;
		public static final int Base_V23_Theme_AppCompat_Light = 0x7f1000b3;
		public static final int Base_V26_Theme_AppCompat = 0x7f1000b8;
		public static final int Base_V26_Theme_AppCompat_Light = 0x7f1000b9;
		public static final int Base_V26_Widget_AppCompat_Toolbar = 0x7f1000ba;
		public static final int Base_V28_Theme_AppCompat = 0x7f1000bb;
		public static final int Base_V28_Theme_AppCompat_Light = 0x7f1000bc;
		public static final int Base_V7_ThemeOverlay_AppCompat_Dialog = 0x7f1000c1;
		public static final int Base_V7_Theme_AppCompat = 0x7f1000bd;
		public static final int Base_V7_Theme_AppCompat_Dialog = 0x7f1000be;
		public static final int Base_V7_Theme_AppCompat_Light = 0x7f1000bf;
		public static final int Base_V7_Theme_AppCompat_Light_Dialog = 0x7f1000c0;
		public static final int Base_V7_Widget_AppCompat_AutoCompleteTextView = 0x7f1000c2;
		public static final int Base_V7_Widget_AppCompat_EditText = 0x7f1000c3;
		public static final int Base_V7_Widget_AppCompat_Toolbar = 0x7f1000c4;
		public static final int Base_Widget_AppCompat_ActionBar = 0x7f1000c5;
		public static final int Base_Widget_AppCompat_ActionBar_Solid = 0x7f1000c6;
		public static final int Base_Widget_AppCompat_ActionBar_TabBar = 0x7f1000c7;
		public static final int Base_Widget_AppCompat_ActionBar_TabText = 0x7f1000c8;
		public static final int Base_Widget_AppCompat_ActionBar_TabView = 0x7f1000c9;
		public static final int Base_Widget_AppCompat_ActionButton = 0x7f1000ca;
		public static final int Base_Widget_AppCompat_ActionButton_CloseMode = 0x7f1000cb;
		public static final int Base_Widget_AppCompat_ActionButton_Overflow = 0x7f1000cc;
		public static final int Base_Widget_AppCompat_ActionMode = 0x7f1000cd;
		public static final int Base_Widget_AppCompat_ActivityChooserView = 0x7f1000ce;
		public static final int Base_Widget_AppCompat_AutoCompleteTextView = 0x7f1000cf;
		public static final int Base_Widget_AppCompat_Button = 0x7f1000d0;
		public static final int Base_Widget_AppCompat_ButtonBar = 0x7f1000d6;
		public static final int Base_Widget_AppCompat_ButtonBar_AlertDialog = 0x7f1000d7;
		public static final int Base_Widget_AppCompat_Button_Borderless = 0x7f1000d1;
		public static final int Base_Widget_AppCompat_Button_Borderless_Colored = 0x7f1000d2;
		public static final int Base_Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f1000d3;
		public static final int Base_Widget_AppCompat_Button_Colored = 0x7f1000d4;
		public static final int Base_Widget_AppCompat_Button_Small = 0x7f1000d5;
		public static final int Base_Widget_AppCompat_CompoundButton_CheckBox = 0x7f1000d8;
		public static final int Base_Widget_AppCompat_CompoundButton_RadioButton = 0x7f1000d9;
		public static final int Base_Widget_AppCompat_CompoundButton_Switch = 0x7f1000da;
		public static final int Base_Widget_AppCompat_DrawerArrowToggle = 0x7f1000db;
		public static final int Base_Widget_AppCompat_DrawerArrowToggle_Common = 0x7f1000dc;
		public static final int Base_Widget_AppCompat_DropDownItem_Spinner = 0x7f1000dd;
		public static final int Base_Widget_AppCompat_EditText = 0x7f1000de;
		public static final int Base_Widget_AppCompat_ImageButton = 0x7f1000df;
		public static final int Base_Widget_AppCompat_Light_ActionBar = 0x7f1000e0;
		public static final int Base_Widget_AppCompat_Light_ActionBar_Solid = 0x7f1000e1;
		public static final int Base_Widget_AppCompat_Light_ActionBar_TabBar = 0x7f1000e2;
		public static final int Base_Widget_AppCompat_Light_ActionBar_TabText = 0x7f1000e3;
		public static final int Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f1000e4;
		public static final int Base_Widget_AppCompat_Light_ActionBar_TabView = 0x7f1000e5;
		public static final int Base_Widget_AppCompat_Light_PopupMenu = 0x7f1000e6;
		public static final int Base_Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f1000e7;
		public static final int Base_Widget_AppCompat_ListMenuView = 0x7f1000e8;
		public static final int Base_Widget_AppCompat_ListPopupWindow = 0x7f1000e9;
		public static final int Base_Widget_AppCompat_ListView = 0x7f1000ea;
		public static final int Base_Widget_AppCompat_ListView_DropDown = 0x7f1000eb;
		public static final int Base_Widget_AppCompat_ListView_Menu = 0x7f1000ec;
		public static final int Base_Widget_AppCompat_PopupMenu = 0x7f1000ed;
		public static final int Base_Widget_AppCompat_PopupMenu_Overflow = 0x7f1000ee;
		public static final int Base_Widget_AppCompat_PopupWindow = 0x7f1000ef;
		public static final int Base_Widget_AppCompat_ProgressBar = 0x7f1000f0;
		public static final int Base_Widget_AppCompat_ProgressBar_Horizontal = 0x7f1000f1;
		public static final int Base_Widget_AppCompat_RatingBar = 0x7f1000f2;
		public static final int Base_Widget_AppCompat_RatingBar_Indicator = 0x7f1000f3;
		public static final int Base_Widget_AppCompat_RatingBar_Small = 0x7f1000f4;
		public static final int Base_Widget_AppCompat_SearchView = 0x7f1000f5;
		public static final int Base_Widget_AppCompat_SearchView_ActionBar = 0x7f1000f6;
		public static final int Base_Widget_AppCompat_SeekBar = 0x7f1000f7;
		public static final int Base_Widget_AppCompat_SeekBar_Discrete = 0x7f1000f8;
		public static final int Base_Widget_AppCompat_Spinner = 0x7f1000f9;
		public static final int Base_Widget_AppCompat_Spinner_Underlined = 0x7f1000fa;
		public static final int Base_Widget_AppCompat_TextView = 0x7f1000fb;
		public static final int Base_Widget_AppCompat_TextView_SpinnerItem = 0x7f1000fc;
		public static final int Base_Widget_AppCompat_Toolbar = 0x7f1000fd;
		public static final int Base_Widget_AppCompat_Toolbar_Button_Navigation = 0x7f1000fe;
		public static final int Platform_AppCompat = 0x7f100143;
		public static final int Platform_AppCompat_Light = 0x7f100144;
		public static final int Platform_ThemeOverlay_AppCompat = 0x7f100149;
		public static final int Platform_ThemeOverlay_AppCompat_Dark = 0x7f10014a;
		public static final int Platform_ThemeOverlay_AppCompat_Light = 0x7f10014b;
		public static final int Platform_V21_AppCompat = 0x7f10014c;
		public static final int Platform_V21_AppCompat_Light = 0x7f10014d;
		public static final int Platform_V25_AppCompat = 0x7f10014e;
		public static final int Platform_V25_AppCompat_Light = 0x7f10014f;
		public static final int Platform_Widget_AppCompat_Spinner = 0x7f100150;
		public static final int RtlOverlay_DialogWindowTitle_AppCompat = 0x7f100151;
		public static final int RtlOverlay_Widget_AppCompat_ActionBar_TitleItem = 0x7f100152;
		public static final int RtlOverlay_Widget_AppCompat_DialogTitle_Icon = 0x7f100153;
		public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem = 0x7f100154;
		public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup = 0x7f100155;
		public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut = 0x7f100156;
		public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow = 0x7f100157;
		public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Text = 0x7f100158;
		public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Title = 0x7f100159;
		public static final int RtlOverlay_Widget_AppCompat_SearchView_MagIcon = 0x7f10015f;
		public static final int RtlOverlay_Widget_AppCompat_Search_DropDown = 0x7f10015a;
		public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 = 0x7f10015b;
		public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 = 0x7f10015c;
		public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Query = 0x7f10015d;
		public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Text = 0x7f10015e;
		public static final int RtlUnderlay_Widget_AppCompat_ActionButton = 0x7f100160;
		public static final int RtlUnderlay_Widget_AppCompat_ActionButton_Overflow = 0x7f100161;
		public static final int TextAppearance_AppCompat = 0x7f1001a0;
		public static final int TextAppearance_AppCompat_Body1 = 0x7f1001a1;
		public static final int TextAppearance_AppCompat_Body2 = 0x7f1001a2;
		public static final int TextAppearance_AppCompat_Button = 0x7f1001a3;
		public static final int TextAppearance_AppCompat_Caption = 0x7f1001a4;
		public static final int TextAppearance_AppCompat_Display1 = 0x7f1001a5;
		public static final int TextAppearance_AppCompat_Display2 = 0x7f1001a6;
		public static final int TextAppearance_AppCompat_Display3 = 0x7f1001a7;
		public static final int TextAppearance_AppCompat_Display4 = 0x7f1001a8;
		public static final int TextAppearance_AppCompat_Headline = 0x7f1001a9;
		public static final int TextAppearance_AppCompat_Inverse = 0x7f1001aa;
		public static final int TextAppearance_AppCompat_Large = 0x7f1001ab;
		public static final int TextAppearance_AppCompat_Large_Inverse = 0x7f1001ac;
		public static final int TextAppearance_AppCompat_Light_SearchResult_Subtitle = 0x7f1001ad;
		public static final int TextAppearance_AppCompat_Light_SearchResult_Title = 0x7f1001ae;
		public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f1001af;
		public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f1001b0;
		public static final int TextAppearance_AppCompat_Medium = 0x7f1001b1;
		public static final int TextAppearance_AppCompat_Medium_Inverse = 0x7f1001b2;
		public static final int TextAppearance_AppCompat_Menu = 0x7f1001b3;
		public static final int TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f1001b4;
		public static final int TextAppearance_AppCompat_SearchResult_Title = 0x7f1001b5;
		public static final int TextAppearance_AppCompat_Small = 0x7f1001b6;
		public static final int TextAppearance_AppCompat_Small_Inverse = 0x7f1001b7;
		public static final int TextAppearance_AppCompat_Subhead = 0x7f1001b8;
		public static final int TextAppearance_AppCompat_Subhead_Inverse = 0x7f1001b9;
		public static final int TextAppearance_AppCompat_Title = 0x7f1001ba;
		public static final int TextAppearance_AppCompat_Title_Inverse = 0x7f1001bb;
		public static final int TextAppearance_AppCompat_Tooltip = 0x7f1001bc;
		public static final int TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f1001bd;
		public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f1001be;
		public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f1001bf;
		public static final int TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f1001c0;
		public static final int TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f1001c1;
		public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f1001c2;
		public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse = 0x7f1001c3;
		public static final int TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f1001c4;
		public static final int TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse = 0x7f1001c5;
		public static final int TextAppearance_AppCompat_Widget_Button = 0x7f1001c6;
		public static final int TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f1001c7;
		public static final int TextAppearance_AppCompat_Widget_Button_Colored = 0x7f1001c8;
		public static final int TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f1001c9;
		public static final int TextAppearance_AppCompat_Widget_DropDownItem = 0x7f1001ca;
		public static final int TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f1001cb;
		public static final int TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f1001cc;
		public static final int TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f1001cd;
		public static final int TextAppearance_AppCompat_Widget_Switch = 0x7f1001ce;
		public static final int TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f1001cf;
		public static final int TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f100215;
		public static final int TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f100216;
		public static final int TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f100217;
		public static final int ThemeOverlay_AppCompat = 0x7f100281;
		public static final int ThemeOverlay_AppCompat_ActionBar = 0x7f100282;
		public static final int ThemeOverlay_AppCompat_Dark = 0x7f100283;
		public static final int ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f100284;
		public static final int ThemeOverlay_AppCompat_DayNight = 0x7f100285;
		public static final int ThemeOverlay_AppCompat_DayNight_ActionBar = 0x7f100286;
		public static final int ThemeOverlay_AppCompat_Dialog = 0x7f100287;
		public static final int ThemeOverlay_AppCompat_Dialog_Alert = 0x7f100288;
		public static final int ThemeOverlay_AppCompat_Light = 0x7f100289;
		public static final int Theme_AppCompat = 0x7f100218;
		public static final int Theme_AppCompat_CompactMenu = 0x7f100219;
		public static final int Theme_AppCompat_DayNight = 0x7f10021a;
		public static final int Theme_AppCompat_DayNight_DarkActionBar = 0x7f10021b;
		public static final int Theme_AppCompat_DayNight_Dialog = 0x7f10021c;
		public static final int Theme_AppCompat_DayNight_DialogWhenLarge = 0x7f10021f;
		public static final int Theme_AppCompat_DayNight_Dialog_Alert = 0x7f10021d;
		public static final int Theme_AppCompat_DayNight_Dialog_MinWidth = 0x7f10021e;
		public static final int Theme_AppCompat_DayNight_NoActionBar = 0x7f100220;
		public static final int Theme_AppCompat_Dialog = 0x7f100221;
		public static final int Theme_AppCompat_DialogWhenLarge = 0x7f100224;
		public static final int Theme_AppCompat_Dialog_Alert = 0x7f100222;
		public static final int Theme_AppCompat_Dialog_MinWidth = 0x7f100223;
		public static final int Theme_AppCompat_Empty = 0x7f100225;
		public static final int Theme_AppCompat_Light = 0x7f100226;
		public static final int Theme_AppCompat_Light_DarkActionBar = 0x7f100227;
		public static final int Theme_AppCompat_Light_Dialog = 0x7f100228;
		public static final int Theme_AppCompat_Light_DialogWhenLarge = 0x7f10022b;
		public static final int Theme_AppCompat_Light_Dialog_Alert = 0x7f100229;
		public static final int Theme_AppCompat_Light_Dialog_MinWidth = 0x7f10022a;
		public static final int Theme_AppCompat_Light_NoActionBar = 0x7f10022c;
		public static final int Theme_AppCompat_NoActionBar = 0x7f10022d;
		public static final int Widget_AppCompat_ActionBar = 0x7f1002f3;
		public static final int Widget_AppCompat_ActionBar_Solid = 0x7f1002f4;
		public static final int Widget_AppCompat_ActionBar_TabBar = 0x7f1002f5;
		public static final int Widget_AppCompat_ActionBar_TabText = 0x7f1002f6;
		public static final int Widget_AppCompat_ActionBar_TabView = 0x7f1002f7;
		public static final int Widget_AppCompat_ActionButton = 0x7f1002f8;
		public static final int Widget_AppCompat_ActionButton_CloseMode = 0x7f1002f9;
		public static final int Widget_AppCompat_ActionButton_Overflow = 0x7f1002fa;
		public static final int Widget_AppCompat_ActionMode = 0x7f1002fb;
		public static final int Widget_AppCompat_ActivityChooserView = 0x7f1002fc;
		public static final int Widget_AppCompat_AutoCompleteTextView = 0x7f1002fd;
		public static final int Widget_AppCompat_Button = 0x7f1002fe;
		public static final int Widget_AppCompat_ButtonBar = 0x7f100304;
		public static final int Widget_AppCompat_ButtonBar_AlertDialog = 0x7f100305;
		public static final int Widget_AppCompat_Button_Borderless = 0x7f1002ff;
		public static final int Widget_AppCompat_Button_Borderless_Colored = 0x7f100300;
		public static final int Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f100301;
		public static final int Widget_AppCompat_Button_Colored = 0x7f100302;
		public static final int Widget_AppCompat_Button_Small = 0x7f100303;
		public static final int Widget_AppCompat_CompoundButton_CheckBox = 0x7f100306;
		public static final int Widget_AppCompat_CompoundButton_RadioButton = 0x7f100307;
		public static final int Widget_AppCompat_CompoundButton_Switch = 0x7f100308;
		public static final int Widget_AppCompat_DrawerArrowToggle = 0x7f100309;
		public static final int Widget_AppCompat_DropDownItem_Spinner = 0x7f10030a;
		public static final int Widget_AppCompat_EditText = 0x7f10030b;
		public static final int Widget_AppCompat_ImageButton = 0x7f10030c;
		public static final int Widget_AppCompat_Light_ActionBar = 0x7f10030d;
		public static final int Widget_AppCompat_Light_ActionBar_Solid = 0x7f10030e;
		public static final int Widget_AppCompat_Light_ActionBar_Solid_Inverse = 0x7f10030f;
		public static final int Widget_AppCompat_Light_ActionBar_TabBar = 0x7f100310;
		public static final int Widget_AppCompat_Light_ActionBar_TabBar_Inverse = 0x7f100311;
		public static final int Widget_AppCompat_Light_ActionBar_TabText = 0x7f100312;
		public static final int Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f100313;
		public static final int Widget_AppCompat_Light_ActionBar_TabView = 0x7f100314;
		public static final int Widget_AppCompat_Light_ActionBar_TabView_Inverse = 0x7f100315;
		public static final int Widget_AppCompat_Light_ActionButton = 0x7f100316;
		public static final int Widget_AppCompat_Light_ActionButton_CloseMode = 0x7f100317;
		public static final int Widget_AppCompat_Light_ActionButton_Overflow = 0x7f100318;
		public static final int Widget_AppCompat_Light_ActionMode_Inverse = 0x7f100319;
		public static final int Widget_AppCompat_Light_ActivityChooserView = 0x7f10031a;
		public static final int Widget_AppCompat_Light_AutoCompleteTextView = 0x7f10031b;
		public static final int Widget_AppCompat_Light_DropDownItem_Spinner = 0x7f10031c;
		public static final int Widget_AppCompat_Light_ListPopupWindow = 0x7f10031d;
		public static final int Widget_AppCompat_Light_ListView_DropDown = 0x7f10031e;
		public static final int Widget_AppCompat_Light_PopupMenu = 0x7f10031f;
		public static final int Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f100320;
		public static final int Widget_AppCompat_Light_SearchView = 0x7f100321;
		public static final int Widget_AppCompat_Light_Spinner_DropDown_ActionBar = 0x7f100322;
		public static final int Widget_AppCompat_ListMenuView = 0x7f100323;
		public static final int Widget_AppCompat_ListPopupWindow = 0x7f100324;
		public static final int Widget_AppCompat_ListView = 0x7f100325;
		public static final int Widget_AppCompat_ListView_DropDown = 0x7f100326;
		public static final int Widget_AppCompat_ListView_Menu = 0x7f100327;
		public static final int Widget_AppCompat_PopupMenu = 0x7f100328;
		public static final int Widget_AppCompat_PopupMenu_Overflow = 0x7f100329;
		public static final int Widget_AppCompat_PopupWindow = 0x7f10032a;
		public static final int Widget_AppCompat_ProgressBar = 0x7f10032b;
		public static final int Widget_AppCompat_ProgressBar_Horizontal = 0x7f10032c;
		public static final int Widget_AppCompat_RatingBar = 0x7f10032d;
		public static final int Widget_AppCompat_RatingBar_Indicator = 0x7f10032e;
		public static final int Widget_AppCompat_RatingBar_Small = 0x7f10032f;
		public static final int Widget_AppCompat_SearchView = 0x7f100330;
		public static final int Widget_AppCompat_SearchView_ActionBar = 0x7f100331;
		public static final int Widget_AppCompat_SeekBar = 0x7f100332;
		public static final int Widget_AppCompat_SeekBar_Discrete = 0x7f100333;
		public static final int Widget_AppCompat_Spinner = 0x7f100334;
		public static final int Widget_AppCompat_Spinner_DropDown = 0x7f100335;
		public static final int Widget_AppCompat_Spinner_DropDown_ActionBar = 0x7f100336;
		public static final int Widget_AppCompat_Spinner_Underlined = 0x7f100337;
		public static final int Widget_AppCompat_TextView = 0x7f100338;
		public static final int Widget_AppCompat_TextView_SpinnerItem = 0x7f100339;
		public static final int Widget_AppCompat_Toolbar = 0x7f10033a;
		public static final int Widget_AppCompat_Toolbar_Button_Navigation = 0x7f10033b;
	}
	public static final class styleable {
		public static final int[] ActionBar = new int[] { 0x7f03004d, 0x7f030054, 0x7f030055, 0x7f030143, 0x7f030144, 0x7f030145, 0x7f030146, 0x7f030147, 0x7f030148, 0x7f030171, 0x7f030188, 0x7f030189, 0x7f0301a9, 0x7f030231, 0x7f030239, 0x7f03023f, 0x7f030240, 0x7f030244, 0x7f030255, 0x7f03026c, 0x7f0302ea, 0x7f030371, 0x7f0303aa, 0x7f0303b2, 0x7f0303b3, 0x7f030439, 0x7f03043d, 0x7f0304c8, 0x7f0304d6 };
		public static final int ActionBar_background = 0;
		public static final int ActionBar_backgroundSplit = 1;
		public static final int ActionBar_backgroundStacked = 2;
		public static final int ActionBar_contentInsetEnd = 3;
		public static final int ActionBar_contentInsetEndWithActions = 4;
		public static final int ActionBar_contentInsetLeft = 5;
		public static final int ActionBar_contentInsetRight = 6;
		public static final int ActionBar_contentInsetStart = 7;
		public static final int ActionBar_contentInsetStartWithNavigation = 8;
		public static final int ActionBar_customNavigationLayout = 9;
		public static final int ActionBar_displayOptions = 10;
		public static final int ActionBar_divider = 11;
		public static final int ActionBar_elevation = 12;
		public static final int ActionBar_height = 13;
		public static final int ActionBar_hideOnContentScroll = 14;
		public static final int ActionBar_homeAsUpIndicator = 15;
		public static final int ActionBar_homeLayout = 16;
		public static final int ActionBar_icon = 17;
		public static final int ActionBar_indeterminateProgressStyle = 18;
		public static final int ActionBar_itemPadding = 19;
		public static final int ActionBar_logo = 20;
		public static final int ActionBar_navigationMode = 21;
		public static final int ActionBar_popupTheme = 22;
		public static final int ActionBar_progressBarPadding = 23;
		public static final int ActionBar_progressBarStyle = 24;
		public static final int ActionBar_subtitle = 25;
		public static final int ActionBar_subtitleTextStyle = 26;
		public static final int ActionBar_title = 27;
		public static final int ActionBar_titleTextStyle = 28;
		public static final int[] ActionBarLayout = new int[] { 0x010100b3 };
		public static final int ActionBarLayout_android_layout_gravity = 0;
		public static final int[] ActionMenuItemView = new int[] { 0x0101013f };
		public static final int ActionMenuItemView_android_minWidth = 0;
		public static final int[] ActionMenuView = new int[] { };
		public static final int[] ActionMode = new int[] { 0x7f03004d, 0x7f030054, 0x7f0300ee, 0x7f030231, 0x7f03043d, 0x7f0304d6 };
		public static final int ActionMode_background = 0;
		public static final int ActionMode_backgroundSplit = 1;
		public static final int ActionMode_closeItemLayout = 2;
		public static final int ActionMode_height = 3;
		public static final int ActionMode_subtitleTextStyle = 4;
		public static final int ActionMode_titleTextStyle = 5;
		public static final int[] ActivityChooserView = new int[] { 0x7f0301c6, 0x7f03025c };
		public static final int ActivityChooserView_expandActivityOverflowButtonDrawable = 0;
		public static final int ActivityChooserView_initialActivityCount = 1;
		public static final int[] AlertDialog = new int[] { 0x010100f2, 0x7f030099, 0x7f03009c, 0x7f0302df, 0x7f0302e0, 0x7f03036c, 0x7f0303f7, 0x7f0303ff };
		public static final int AlertDialog_android_layout = 0;
		public static final int AlertDialog_buttonIconDimen = 1;
		public static final int AlertDialog_buttonPanelSideLayout = 2;
		public static final int AlertDialog_listItemLayout = 3;
		public static final int AlertDialog_listLayout = 4;
		public static final int AlertDialog_multiChoiceItemLayout = 5;
		public static final int AlertDialog_showTitle = 6;
		public static final int AlertDialog_singleChoiceItemLayout = 7;
		public static final int[] AppCompatEmojiHelper = new int[] { };
		public static final int[] AppCompatImageView = new int[] { 0x01010119, 0x7f030418, 0x7f0304c5, 0x7f0304c6 };
		public static final int AppCompatImageView_android_src = 0;
		public static final int AppCompatImageView_srcCompat = 1;
		public static final int AppCompatImageView_tint = 2;
		public static final int AppCompatImageView_tintMode = 3;
		public static final int[] AppCompatSeekBar = new int[] { 0x01010142, 0x7f0304bf, 0x7f0304c0, 0x7f0304c1 };
		public static final int AppCompatSeekBar_android_thumb = 0;
		public static final int AppCompatSeekBar_tickMark = 1;
		public static final int AppCompatSeekBar_tickMarkTint = 2;
		public static final int AppCompatSeekBar_tickMarkTintMode = 3;
		public static final int[] AppCompatTextHelper = new int[] { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 };
		public static final int AppCompatTextHelper_android_drawableBottom = 2;
		public static final int AppCompatTextHelper_android_drawableEnd = 6;
		public static final int AppCompatTextHelper_android_drawableLeft = 3;
		public static final int AppCompatTextHelper_android_drawableRight = 4;
		public static final int AppCompatTextHelper_android_drawableStart = 5;
		public static final int AppCompatTextHelper_android_drawableTop = 1;
		public static final int AppCompatTextHelper_android_textAppearance = 0;
		public static final int[] AppCompatTextView = new int[] { 0x01010034, 0x7f030046, 0x7f030047, 0x7f030048, 0x7f030049, 0x7f03004a, 0x7f030195, 0x7f030196, 0x7f030197, 0x7f030198, 0x7f03019a, 0x7f03019b, 0x7f03019c, 0x7f03019d, 0x7f0301ad, 0x7f0301ea, 0x7f03020e, 0x7f030218, 0x7f030288, 0x7f0302d8, 0x7f03046a, 0x7f0304a1 };
		public static final int AppCompatTextView_android_textAppearance = 0;
		public static final int AppCompatTextView_autoSizeMaxTextSize = 1;
		public static final int AppCompatTextView_autoSizeMinTextSize = 2;
		public static final int AppCompatTextView_autoSizePresetSizes = 3;
		public static final int AppCompatTextView_autoSizeStepGranularity = 4;
		public static final int AppCompatTextView_autoSizeTextType = 5;
		public static final int AppCompatTextView_drawableBottomCompat = 6;
		public static final int AppCompatTextView_drawableEndCompat = 7;
		public static final int AppCompatTextView_drawableLeftCompat = 8;
		public static final int AppCompatTextView_drawableRightCompat = 9;
		public static final int AppCompatTextView_drawableStartCompat = 10;
		public static final int AppCompatTextView_drawableTint = 11;
		public static final int AppCompatTextView_drawableTintMode = 12;
		public static final int AppCompatTextView_drawableTopCompat = 13;
		public static final int AppCompatTextView_emojiCompatEnabled = 14;
		public static final int AppCompatTextView_firstBaselineToTopHeight = 15;
		public static final int AppCompatTextView_fontFamily = 16;
		public static final int AppCompatTextView_fontVariationSettings = 17;
		public static final int AppCompatTextView_lastBaselineToBottomHeight = 18;
		public static final int AppCompatTextView_lineHeight = 19;
		public static final int AppCompatTextView_textAllCaps = 20;
		public static final int AppCompatTextView_textLocale = 21;
		public static final int[] AppCompatTheme = new int[] { 0x01010057, 0x010100ae, 0x7f030003, 0x7f030004, 0x7f030005, 0x7f030006, 0x7f030007, 0x7f030008, 0x7f030009, 0x7f03000a, 0x7f03000b, 0x7f03000c, 0x7f03000d, 0x7f03000e, 0x7f03000f, 0x7f030011, 0x7f030012, 0x7f030013, 0x7f030014, 0x7f030015, 0x7f030016, 0x7f030017, 0x7f030018, 0x7f030019, 0x7f03001a, 0x7f03001b, 0x7f03001c, 0x7f03001d, 0x7f03001e, 0x7f03001f, 0x7f030020, 0x7f030021, 0x7f030022, 0x7f030023, 0x7f030029, 0x7f03002c, 0x7f03002d, 0x7f03002e, 0x7f03002f, 0x7f030044, 0x7f03007d, 0x7f030091, 0x7f030092, 0x7f030093, 0x7f030094, 0x7f030095, 0x7f03009d, 0x7f03009e, 0x7f0300b9, 0x7f0300c4, 0x7f0300fc, 0x7f0300fd, 0x7f0300fe, 0x7f030100, 0x7f030101, 0x7f030102, 0x7f030103, 0x7f03011c, 0x7f03011e, 0x7f030133, 0x7f030152, 0x7f030185, 0x7f030186, 0x7f030187, 0x7f03018b, 0x7f030190, 0x7f0301a2, 0x7f0301a3, 0x7f0301a6, 0x7f0301a7, 0x7f0301a8, 0x7f03023f, 0x7f03024f, 0x7f0302db, 0x7f0302dc, 0x7f0302dd, 0x7f0302de, 0x7f0302e1, 0x7f0302e2, 0x7f0302e3, 0x7f0302e4, 0x7f0302e5, 0x7f0302e6, 0x7f0302e7, 0x7f0302e8, 0x7f0302e9, 0x7f03038d, 0x7f03038e, 0x7f03038f, 0x7f0303a9, 0x7f0303ab, 0x7f0303ba, 0x7f0303bc, 0x7f0303bd, 0x7f0303be, 0x7f0303d9, 0x7f0303dc, 0x7f0303dd, 0x7f0303de, 0x7f030409, 0x7f03040a, 0x7f030445, 0x7f030481, 0x7f030483, 0x7f030484, 0x7f030485, 0x7f030487, 0x7f030488, 0x7f030489, 0x7f03048a, 0x7f030495, 0x7f030496, 0x7f0304d9, 0x7f0304da, 0x7f0304dc, 0x7f0304dd, 0x7f030504, 0x7f030512, 0x7f030513, 0x7f030514, 0x7f030515, 0x7f030516, 0x7f030517, 0x7f030518, 0x7f030519, 0x7f03051a, 0x7f03051b };
		public static final int AppCompatTheme_actionBarDivider = 2;
		public static final int AppCompatTheme_actionBarItemBackground = 3;
		public static final int AppCompatTheme_actionBarPopupTheme = 4;
		public static final int AppCompatTheme_actionBarSize = 5;
		public static final int AppCompatTheme_actionBarSplitStyle = 6;
		public static final int AppCompatTheme_actionBarStyle = 7;
		public static final int AppCompatTheme_actionBarTabBarStyle = 8;
		public static final int AppCompatTheme_actionBarTabStyle = 9;
		public static final int AppCompatTheme_actionBarTabTextStyle = 10;
		public static final int AppCompatTheme_actionBarTheme = 11;
		public static final int AppCompatTheme_actionBarWidgetTheme = 12;
		public static final int AppCompatTheme_actionButtonStyle = 13;
		public static final int AppCompatTheme_actionDropDownStyle = 14;
		public static final int AppCompatTheme_actionMenuTextAppearance = 15;
		public static final int AppCompatTheme_actionMenuTextColor = 16;
		public static final int AppCompatTheme_actionModeBackground = 17;
		public static final int AppCompatTheme_actionModeCloseButtonStyle = 18;
		public static final int AppCompatTheme_actionModeCloseContentDescription = 19;
		public static final int AppCompatTheme_actionModeCloseDrawable = 20;
		public static final int AppCompatTheme_actionModeCopyDrawable = 21;
		public static final int AppCompatTheme_actionModeCutDrawable = 22;
		public static final int AppCompatTheme_actionModeFindDrawable = 23;
		public static final int AppCompatTheme_actionModePasteDrawable = 24;
		public static final int AppCompatTheme_actionModePopupWindowStyle = 25;
		public static final int AppCompatTheme_actionModeSelectAllDrawable = 26;
		public static final int AppCompatTheme_actionModeShareDrawable = 27;
		public static final int AppCompatTheme_actionModeSplitBackground = 28;
		public static final int AppCompatTheme_actionModeStyle = 29;
		public static final int AppCompatTheme_actionModeTheme = 30;
		public static final int AppCompatTheme_actionModeWebSearchDrawable = 31;
		public static final int AppCompatTheme_actionOverflowButtonStyle = 32;
		public static final int AppCompatTheme_actionOverflowMenuStyle = 33;
		public static final int AppCompatTheme_activityChooserViewStyle = 34;
		public static final int AppCompatTheme_alertDialogButtonGroupStyle = 35;
		public static final int AppCompatTheme_alertDialogCenterButtons = 36;
		public static final int AppCompatTheme_alertDialogStyle = 37;
		public static final int AppCompatTheme_alertDialogTheme = 38;
		public static final int AppCompatTheme_android_windowAnimationStyle = 1;
		public static final int AppCompatTheme_android_windowIsFloating = 0;
		public static final int AppCompatTheme_autoCompleteTextViewStyle = 39;
		public static final int AppCompatTheme_borderlessButtonStyle = 40;
		public static final int AppCompatTheme_buttonBarButtonStyle = 41;
		public static final int AppCompatTheme_buttonBarNegativeButtonStyle = 42;
		public static final int AppCompatTheme_buttonBarNeutralButtonStyle = 43;
		public static final int AppCompatTheme_buttonBarPositiveButtonStyle = 44;
		public static final int AppCompatTheme_buttonBarStyle = 45;
		public static final int AppCompatTheme_buttonStyle = 46;
		public static final int AppCompatTheme_buttonStyleSmall = 47;
		public static final int AppCompatTheme_checkboxStyle = 48;
		public static final int AppCompatTheme_checkedTextViewStyle = 49;
		public static final int AppCompatTheme_colorAccent = 50;
		public static final int AppCompatTheme_colorBackgroundFloating = 51;
		public static final int AppCompatTheme_colorButtonNormal = 52;
		public static final int AppCompatTheme_colorControlActivated = 53;
		public static final int AppCompatTheme_colorControlHighlight = 54;
		public static final int AppCompatTheme_colorControlNormal = 55;
		public static final int AppCompatTheme_colorError = 56;
		public static final int AppCompatTheme_colorPrimary = 57;
		public static final int AppCompatTheme_colorPrimaryDark = 58;
		public static final int AppCompatTheme_colorSwitchThumbNormal = 59;
		public static final int AppCompatTheme_controlBackground = 60;
		public static final int AppCompatTheme_dialogCornerRadius = 61;
		public static final int AppCompatTheme_dialogPreferredPadding = 62;
		public static final int AppCompatTheme_dialogTheme = 63;
		public static final int AppCompatTheme_dividerHorizontal = 64;
		public static final int AppCompatTheme_dividerVertical = 65;
		public static final int AppCompatTheme_dropDownListViewStyle = 66;
		public static final int AppCompatTheme_dropdownListPreferredItemHeight = 67;
		public static final int AppCompatTheme_editTextBackground = 68;
		public static final int AppCompatTheme_editTextColor = 69;
		public static final int AppCompatTheme_editTextStyle = 70;
		public static final int AppCompatTheme_homeAsUpIndicator = 71;
		public static final int AppCompatTheme_imageButtonStyle = 72;
		public static final int AppCompatTheme_listChoiceBackgroundIndicator = 73;
		public static final int AppCompatTheme_listChoiceIndicatorMultipleAnimated = 74;
		public static final int AppCompatTheme_listChoiceIndicatorSingleAnimated = 75;
		public static final int AppCompatTheme_listDividerAlertDialog = 76;
		public static final int AppCompatTheme_listMenuViewStyle = 77;
		public static final int AppCompatTheme_listPopupWindowStyle = 78;
		public static final int AppCompatTheme_listPreferredItemHeight = 79;
		public static final int AppCompatTheme_listPreferredItemHeightLarge = 80;
		public static final int AppCompatTheme_listPreferredItemHeightSmall = 81;
		public static final int AppCompatTheme_listPreferredItemPaddingEnd = 82;
		public static final int AppCompatTheme_listPreferredItemPaddingLeft = 83;
		public static final int AppCompatTheme_listPreferredItemPaddingRight = 84;
		public static final int AppCompatTheme_listPreferredItemPaddingStart = 85;
		public static final int AppCompatTheme_panelBackground = 86;
		public static final int AppCompatTheme_panelMenuListTheme = 87;
		public static final int AppCompatTheme_panelMenuListWidth = 88;
		public static final int AppCompatTheme_popupMenuStyle = 89;
		public static final int AppCompatTheme_popupWindowStyle = 90;
		public static final int AppCompatTheme_radioButtonStyle = 91;
		public static final int AppCompatTheme_ratingBarStyle = 92;
		public static final int AppCompatTheme_ratingBarStyleIndicator = 93;
		public static final int AppCompatTheme_ratingBarStyleSmall = 94;
		public static final int AppCompatTheme_searchViewStyle = 95;
		public static final int AppCompatTheme_seekBarStyle = 96;
		public static final int AppCompatTheme_selectableItemBackground = 97;
		public static final int AppCompatTheme_selectableItemBackgroundBorderless = 98;
		public static final int AppCompatTheme_spinnerDropDownItemStyle = 99;
		public static final int AppCompatTheme_spinnerStyle = 100;
		public static final int AppCompatTheme_switchStyle = 101;
		public static final int AppCompatTheme_textAppearanceLargePopupMenu = 102;
		public static final int AppCompatTheme_textAppearanceListItem = 103;
		public static final int AppCompatTheme_textAppearanceListItemSecondary = 104;
		public static final int AppCompatTheme_textAppearanceListItemSmall = 105;
		public static final int AppCompatTheme_textAppearancePopupMenuHeader = 106;
		public static final int AppCompatTheme_textAppearanceSearchResultSubtitle = 107;
		public static final int AppCompatTheme_textAppearanceSearchResultTitle = 108;
		public static final int AppCompatTheme_textAppearanceSmallPopupMenu = 109;
		public static final int AppCompatTheme_textColorAlertDialogListItem = 110;
		public static final int AppCompatTheme_textColorSearchUrl = 111;
		public static final int AppCompatTheme_toolbarNavigationButtonStyle = 112;
		public static final int AppCompatTheme_toolbarStyle = 113;
		public static final int AppCompatTheme_tooltipForegroundColor = 114;
		public static final int AppCompatTheme_tooltipFrameBackground = 115;
		public static final int AppCompatTheme_viewInflaterClass = 116;
		public static final int AppCompatTheme_windowActionBar = 117;
		public static final int AppCompatTheme_windowActionBarOverlay = 118;
		public static final int AppCompatTheme_windowActionModeOverlay = 119;
		public static final int AppCompatTheme_windowFixedHeightMajor = 120;
		public static final int AppCompatTheme_windowFixedHeightMinor = 121;
		public static final int AppCompatTheme_windowFixedWidthMajor = 122;
		public static final int AppCompatTheme_windowFixedWidthMinor = 123;
		public static final int AppCompatTheme_windowMinWidthMajor = 124;
		public static final int AppCompatTheme_windowMinWidthMinor = 125;
		public static final int AppCompatTheme_windowNoTitle = 126;
		public static final int[] ButtonBarLayout = new int[] { 0x7f030030 };
		public static final int ButtonBarLayout_allowStacking = 0;
		public static final int[] CheckedTextView = new int[] { 0x01010108, 0x7f0300b6, 0x7f0300b7, 0x7f0300b8 };
		public static final int CheckedTextView_android_checkMark = 0;
		public static final int CheckedTextView_checkMarkCompat = 1;
		public static final int CheckedTextView_checkMarkTint = 2;
		public static final int CheckedTextView_checkMarkTintMode = 3;
		public static final int[] CompoundButton = new int[] { 0x01010107, 0x7f030096, 0x7f03009f, 0x7f0300a0 };
		public static final int CompoundButton_android_button = 0;
		public static final int CompoundButton_buttonCompat = 1;
		public static final int CompoundButton_buttonTint = 2;
		public static final int CompoundButton_buttonTintMode = 3;
		public static final int[] DrawerArrowToggle = new int[] { 0x7f03003f, 0x7f030040, 0x7f030069, 0x7f0300fb, 0x7f030199, 0x7f03021e, 0x7f030408, 0x7f0304ac };
		public static final int DrawerArrowToggle_arrowHeadLength = 0;
		public static final int DrawerArrowToggle_arrowShaftLength = 1;
		public static final int DrawerArrowToggle_barLength = 2;
		public static final int DrawerArrowToggle_color = 3;
		public static final int DrawerArrowToggle_drawableSize = 4;
		public static final int DrawerArrowToggle_gapBetweenBars = 5;
		public static final int DrawerArrowToggle_spinBars = 6;
		public static final int DrawerArrowToggle_thickness = 7;
		public static final int[] LinearLayoutCompat = new int[] { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f030189, 0x7f03018e, 0x7f03032d, 0x7f0303f2 };
		public static final int LinearLayoutCompat_android_baselineAligned = 2;
		public static final int LinearLayoutCompat_android_baselineAlignedChildIndex = 3;
		public static final int LinearLayoutCompat_android_gravity = 0;
		public static final int LinearLayoutCompat_android_orientation = 1;
		public static final int LinearLayoutCompat_android_weightSum = 4;
		public static final int LinearLayoutCompat_divider = 5;
		public static final int LinearLayoutCompat_dividerPadding = 6;
		public static final int LinearLayoutCompat_measureWithLargestChild = 7;
		public static final int LinearLayoutCompat_showDividers = 8;
		public static final int[] LinearLayoutCompat_Layout = new int[] { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 };
		public static final int LinearLayoutCompat_Layout_android_layout_gravity = 0;
		public static final int LinearLayoutCompat_Layout_android_layout_height = 2;
		public static final int LinearLayoutCompat_Layout_android_layout_weight = 3;
		public static final int LinearLayoutCompat_Layout_android_layout_width = 1;
		public static final int[] ListPopupWindow = new int[] { 0x010102ac, 0x010102ad };
		public static final int ListPopupWindow_android_dropDownHorizontalOffset = 0;
		public static final int ListPopupWindow_android_dropDownVerticalOffset = 1;
		public static final int[] MenuGroup = new int[] { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 };
		public static final int MenuGroup_android_checkableBehavior = 5;
		public static final int MenuGroup_android_enabled = 0;
		public static final int MenuGroup_android_id = 1;
		public static final int MenuGroup_android_menuCategory = 3;
		public static final int MenuGroup_android_orderInCategory = 4;
		public static final int MenuGroup_android_visible = 2;
		public static final int[] MenuItem = new int[] { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f030010, 0x7f030024, 0x7f030026, 0x7f030032, 0x7f030142, 0x7f03024a, 0x7f03024b, 0x7f030379, 0x7f0303f0, 0x7f0304df };
		public static final int MenuItem_actionLayout = 13;
		public static final int MenuItem_actionProviderClass = 14;
		public static final int MenuItem_actionViewClass = 15;
		public static final int MenuItem_alphabeticModifiers = 16;
		public static final int MenuItem_android_alphabeticShortcut = 9;
		public static final int MenuItem_android_checkable = 11;
		public static final int MenuItem_android_checked = 3;
		public static final int MenuItem_android_enabled = 1;
		public static final int MenuItem_android_icon = 0;
		public static final int MenuItem_android_id = 2;
		public static final int MenuItem_android_menuCategory = 5;
		public static final int MenuItem_android_numericShortcut = 10;
		public static final int MenuItem_android_onClick = 12;
		public static final int MenuItem_android_orderInCategory = 6;
		public static final int MenuItem_android_title = 7;
		public static final int MenuItem_android_titleCondensed = 8;
		public static final int MenuItem_android_visible = 4;
		public static final int MenuItem_contentDescription = 17;
		public static final int MenuItem_iconTint = 18;
		public static final int MenuItem_iconTintMode = 19;
		public static final int MenuItem_numericModifiers = 20;
		public static final int MenuItem_showAsAction = 21;
		public static final int MenuItem_tooltipText = 22;
		public static final int[] MenuView = new int[] { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f0303af, 0x7f030433 };
		public static final int MenuView_android_headerBackground = 4;
		public static final int MenuView_android_horizontalDivider = 2;
		public static final int MenuView_android_itemBackground = 5;
		public static final int MenuView_android_itemIconDisabledAlpha = 6;
		public static final int MenuView_android_itemTextAppearance = 1;
		public static final int MenuView_android_verticalDivider = 3;
		public static final int MenuView_android_windowAnimationStyle = 0;
		public static final int MenuView_preserveIconSpacing = 7;
		public static final int MenuView_subMenuArrow = 8;
		public static final int[] PopupWindow = new int[] { 0x01010176, 0x010102c9, 0x7f030382 };
		public static final int PopupWindow_android_popupAnimationStyle = 1;
		public static final int PopupWindow_android_popupBackground = 0;
		public static final int PopupWindow_overlapAnchor = 2;
		public static final int[] PopupWindowBackgroundState = new int[] { 0x7f030424 };
		public static final int PopupWindowBackgroundState_state_above_anchor = 0;
		public static final int[] RecycleListView = new int[] { 0x7f030384, 0x7f03038b };
		public static final int RecycleListView_paddingBottomNoButtons = 0;
		public static final int RecycleListView_paddingTopNoTitle = 1;
		public static final int[] SearchView = new int[] { 0x01010034, 0x010100da, 0x0101011f, 0x0101014f, 0x01010150, 0x01010220, 0x01010264, 0x7f030036, 0x7f030037, 0x7f030045, 0x7f03004c, 0x7f030056, 0x7f0300e7, 0x7f030138, 0x7f03017e, 0x7f030220, 0x7f030230, 0x7f030238, 0x7f03024c, 0x7f03028b, 0x7f0303b7, 0x7f0303b8, 0x7f0303d6, 0x7f0303d7, 0x7f0303d8, 0x7f030438, 0x7f030441, 0x7f0304ff, 0x7f03050a };
		public static final int SearchView_android_focusable = 1;
		public static final int SearchView_android_imeOptions = 6;
		public static final int SearchView_android_inputType = 5;
		public static final int SearchView_android_maxWidth = 2;
		public static final int SearchView_closeIcon = 12;
		public static final int SearchView_commitIcon = 13;
		public static final int SearchView_defaultQueryHint = 14;
		public static final int SearchView_goIcon = 15;
		public static final int SearchView_iconifiedByDefault = 18;
		public static final int SearchView_layout = 19;
		public static final int SearchView_queryBackground = 20;
		public static final int SearchView_queryHint = 21;
		public static final int SearchView_searchHintIcon = 22;
		public static final int SearchView_searchIcon = 23;
		public static final int SearchView_submitBackground = 25;
		public static final int SearchView_suggestionRowLayout = 26;
		public static final int SearchView_voiceIcon = 28;
		public static final int[] Spinner = new int[] { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f0303aa };
		public static final int Spinner_android_dropDownWidth = 3;
		public static final int Spinner_android_entries = 0;
		public static final int Spinner_android_popupBackground = 1;
		public static final int Spinner_android_prompt = 2;
		public static final int Spinner_popupTheme = 4;
		public static final int[] SwitchCompat = new int[] { 0x01010124, 0x01010125, 0x01010142, 0x7f0303f6, 0x7f030412, 0x7f030443, 0x7f030444, 0x7f030446, 0x7f0304b7, 0x7f0304b8, 0x7f0304b9, 0x7f0304e4, 0x7f0304f0, 0x7f0304f1 };
		public static final int SwitchCompat_android_textOff = 1;
		public static final int SwitchCompat_android_textOn = 0;
		public static final int SwitchCompat_android_thumb = 2;
		public static final int SwitchCompat_showText = 3;
		public static final int SwitchCompat_splitTrack = 4;
		public static final int SwitchCompat_switchMinWidth = 5;
		public static final int SwitchCompat_switchPadding = 6;
		public static final int SwitchCompat_switchTextAppearance = 7;
		public static final int SwitchCompat_thumbTextPadding = 8;
		public static final int SwitchCompat_thumbTint = 9;
		public static final int SwitchCompat_thumbTintMode = 10;
		public static final int SwitchCompat_track = 11;
		public static final int SwitchCompat_trackTint = 12;
		public static final int SwitchCompat_trackTintMode = 13;
		public static final int[] TextAppearance = new int[] { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x01010585, 0x7f03020e, 0x7f030218, 0x7f03046a, 0x7f0304a1 };
		public static final int TextAppearance_android_fontFamily = 10;
		public static final int TextAppearance_android_shadowColor = 6;
		public static final int TextAppearance_android_shadowDx = 7;
		public static final int TextAppearance_android_shadowDy = 8;
		public static final int TextAppearance_android_shadowRadius = 9;
		public static final int TextAppearance_android_textColor = 3;
		public static final int TextAppearance_android_textColorHint = 4;
		public static final int TextAppearance_android_textColorLink = 5;
		public static final int TextAppearance_android_textFontWeight = 11;
		public static final int TextAppearance_android_textSize = 0;
		public static final int TextAppearance_android_textStyle = 2;
		public static final int TextAppearance_android_typeface = 1;
		public static final int TextAppearance_fontFamily = 12;
		public static final int TextAppearance_fontVariationSettings = 13;
		public static final int TextAppearance_textAllCaps = 14;
		public static final int TextAppearance_textLocale = 15;
		public static final int[] Toolbar = new int[] { 0x010100af, 0x01010140, 0x7f030097, 0x7f0300ef, 0x7f0300f0, 0x7f030143, 0x7f030144, 0x7f030145, 0x7f030146, 0x7f030147, 0x7f030148, 0x7f0302ea, 0x7f0302ec, 0x7f030325, 0x7f03032e, 0x7f03036e, 0x7f03036f, 0x7f0303aa, 0x7f030439, 0x7f03043b, 0x7f03043c, 0x7f0304c8, 0x7f0304cc, 0x7f0304cd, 0x7f0304ce, 0x7f0304cf, 0x7f0304d0, 0x7f0304d1, 0x7f0304d3, 0x7f0304d4 };
		public static final int Toolbar_android_gravity = 0;
		public static final int Toolbar_android_minHeight = 1;
		public static final int Toolbar_buttonGravity = 2;
		public static final int Toolbar_collapseContentDescription = 3;
		public static final int Toolbar_collapseIcon = 4;
		public static final int Toolbar_contentInsetEnd = 5;
		public static final int Toolbar_contentInsetEndWithActions = 6;
		public static final int Toolbar_contentInsetLeft = 7;
		public static final int Toolbar_contentInsetRight = 8;
		public static final int Toolbar_contentInsetStart = 9;
		public static final int Toolbar_contentInsetStartWithNavigation = 10;
		public static final int Toolbar_logo = 11;
		public static final int Toolbar_logoDescription = 12;
		public static final int Toolbar_maxButtonHeight = 13;
		public static final int Toolbar_menu = 14;
		public static final int Toolbar_navigationContentDescription = 15;
		public static final int Toolbar_navigationIcon = 16;
		public static final int Toolbar_popupTheme = 17;
		public static final int Toolbar_subtitle = 18;
		public static final int Toolbar_subtitleTextAppearance = 19;
		public static final int Toolbar_subtitleTextColor = 20;
		public static final int Toolbar_title = 21;
		public static final int Toolbar_titleMargin = 22;
		public static final int Toolbar_titleMarginBottom = 23;
		public static final int Toolbar_titleMarginEnd = 24;
		public static final int Toolbar_titleMarginStart = 25;
		public static final int Toolbar_titleMarginTop = 26;
		public static final int Toolbar_titleMargins = 27;
		public static final int Toolbar_titleTextAppearance = 28;
		public static final int Toolbar_titleTextColor = 29;
		public static final int[] View = new int[] { 0x01010000, 0x010100da, 0x7f030386, 0x7f030389, 0x7f0304ab };
		public static final int View_android_focusable = 1;
		public static final int View_android_theme = 0;
		public static final int View_paddingEnd = 2;
		public static final int View_paddingStart = 3;
		public static final int View_theme = 4;
		public static final int[] ViewBackgroundHelper = new int[] { 0x010100d4, 0x7f030056, 0x7f030057 };
		public static final int ViewBackgroundHelper_android_background = 0;
		public static final int ViewBackgroundHelper_backgroundTint = 1;
		public static final int ViewBackgroundHelper_backgroundTintMode = 2;
		public static final int[] ViewStubCompat = new int[] { 0x010100d0, 0x010100f2, 0x010100f3 };
		public static final int ViewStubCompat_android_id = 0;
		public static final int ViewStubCompat_android_inflatedId = 2;
		public static final int ViewStubCompat_android_layout = 1;
	}
}
