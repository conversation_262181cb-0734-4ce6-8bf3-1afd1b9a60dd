@page "/checkinbox"
@inject NavigationManager Navigation

<div class="check-inbox-container">
    <!-- Email illustration -->
    <div class="illustration-section">
        <div class="email-illustration">
            <!-- Main blue email window -->
            <div class="main-email-window">
                <div class="window-header">
                    <div class="header-dot"></div>
                </div>
                <div class="window-content">
                    <div class="content-lines">
                        <div class="content-line line-1"></div>
                        <div class="content-line line-2"></div>
                        <div class="content-line line-3"></div>
                        <div class="content-line line-4"></div>
                    </div>
                </div>
            </div>

            <!-- Small teal email card -->
            <div class="small-email-card">
                <div class="small-card-lines">
                    <div class="small-line"></div>
                    <div class="small-line"></div>
                </div>
            </div>

            <!-- Bottom email icon -->
            <div class="bottom-email-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <rect x="2" y="4" width="20" height="16" rx="2" stroke="#666" stroke-width="2" fill="none"/>
                    <path d="M2 6L12 13L22 6" stroke="#666" stroke-width="2" stroke-linecap="round"/>
                </svg>
            </div>
        </div>
    </div>

    <!-- Content section -->
    <div class="content-section">
        <h1 class="main-title">Check Your Inbox</h1>
        <p class="description">
            We've sent you a verification email.<br>
            Please check your inbox and click the<br>
            link to verify your account.
        </p>
    </div>

    <!-- Button section -->
    <div class="button-section">
        <button class="email-client-button" @onclick="OpenEmailClient">
            Open Email Client
        </button>
    </div>

    <!-- Footer section -->
    <div class="footer-section">
        <p class="footer-text">
            Didn't receive the email? Check your spam folder
        </p>
    </div>
</div>

<style>
    .check-inbox-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 40px 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: #FFFFFF;
        min-height: 100vh;
        width: 375px;
        margin: 0 auto;
        justify-content: center;
    }

    .illustration-section {
        margin-bottom: 60px;
        position: relative;
        width: 200px;
        height: 160px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .email-illustration {
        position: relative;
        width: 100%;
        height: 100%;
    }

    /* Main blue email window */
    .main-email-window {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 120px;
        height: 80px;
        background: #4A90E2;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
        z-index: 2;
    }

    .window-header {
        height: 16px;
        background: rgba(255,255,255,0.2);
        border-radius: 8px 8px 0 0;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 0 8px;
    }

    .header-dot {
        width: 6px;
        height: 6px;
        background: white;
        border-radius: 50%;
    }

    .window-content {
        padding: 8px 12px;
        height: calc(100% - 16px);
    }

    .content-lines {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .content-line {
        height: 3px;
        background: rgba(255,255,255,0.8);
        border-radius: 2px;
    }

    .line-1 {
        width: 85%;
    }

    .line-2 {
        width: 70%;
    }

    .line-3 {
        width: 90%;
    }

    .line-4 {
        width: 60%;
    }

    /* Small teal email card */
    .small-email-card {
        position: absolute;
        top: 30px;
        left: 20px;
        width: 70px;
        height: 50px;
        background: #20D4C7;
        border-radius: 6px;
        box-shadow: 0 3px 8px rgba(32, 212, 199, 0.3);
        padding: 8px;
        z-index: 1;
    }

    .small-card-lines {
        display: flex;
        flex-direction: column;
        gap: 4px;
        margin-top: 8px;
    }

    .small-line {
        height: 2px;
        background: rgba(255,255,255,0.7);
        border-radius: 1px;
        width: 80%;
    }

    /* Bottom email icon */
    .bottom-email-icon {
        position: absolute;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 40px;
        background: #F5F5F5;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        z-index: 1;
    }

    .content-section {
        text-align: center;
        margin-bottom: 40px;
    }

    .main-title {
        font-size: 24px;
        font-weight: 800;
        color: #000000;
        margin: 0 0 20px 0;
        line-height: 1.2;
    }

    .description {
        font-size: 16px;
        color: #666666;
        line-height: 1.5;
        margin: 0;
    }

    .button-section {
        width: 100%;
        max-width: 320px;
        margin-bottom: 30px;
    }

    .email-client-button {
        width: 100%;
        height: 50px;
        background-color: #87CEEB;
        color: #333333;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(135, 206, 235, 0.3);
    }

    .email-client-button:hover {
        background-color: #7BC4E8;
    }

    .footer-section {
        margin-top: auto;
        padding-top: 20px;
    }

    .footer-text {
        font-size: 14px;
        color: #999999;
        text-align: center;
        margin: 0;
        line-height: 1.4;
    }
</style>

@code {
    private void OpenEmailClient()
    {
        // Handle opening email client
        // This could open the default email app or navigate to next step
        Navigation.NavigateTo("/main");
    }
}
