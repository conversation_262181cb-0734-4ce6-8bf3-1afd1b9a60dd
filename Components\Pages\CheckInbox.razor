@page "/checkinbox"
@inject NavigationManager Navigation

<div class="check-inbox-container">
    <!-- Email illustration -->
    <div class="illustration-section">
        <div class="email-illustration">
            <!-- Computer monitor -->
            <div class="monitor">
                <div class="screen">
                    <div class="email-window">
                        <div class="window-header">
                            <div class="window-controls">
                                <span class="control-dot"></span>
                                <span class="control-dot"></span>
                                <span class="control-dot"></span>
                            </div>
                        </div>
                        <div class="email-content">
                            <div class="email-lines">
                                <div class="email-line long"></div>
                                <div class="email-line medium"></div>
                                <div class="email-line short"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="monitor-stand"></div>
                <div class="monitor-base"></div>
            </div>
            
            <!-- Floating email cards -->
            <div class="email-card card-1">
                <div class="card-header"></div>
                <div class="card-lines">
                    <div class="card-line"></div>
                    <div class="card-line"></div>
                </div>
            </div>
            
            <div class="email-card card-2">
                <div class="card-header"></div>
                <div class="card-lines">
                    <div class="card-line"></div>
                    <div class="card-line"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content section -->
    <div class="content-section">
        <h1 class="main-title">Check Your Inbox</h1>
        <p class="description">
            We've sent you a verification email.<br>
            Please check your inbox and click the<br>
            link to verify your account.
        </p>
    </div>

    <!-- Button section -->
    <div class="button-section">
        <button class="email-client-button" @onclick="OpenEmailClient">
            Open Email Client
        </button>
    </div>

    <!-- Footer section -->
    <div class="footer-section">
        <p class="footer-text">
            Didn't receive the email? Check your spam folder
        </p>
    </div>
</div>

<style>
    .check-inbox-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 40px 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: #FFFFFF;
        min-height: 100vh;
        width: 375px;
        margin: 0 auto;
        justify-content: center;
    }

    .illustration-section {
        margin-bottom: 60px;
        position: relative;
        width: 280px;
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .email-illustration {
        position: relative;
        width: 100%;
        height: 100%;
    }

    /* Computer Monitor */
    .monitor {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        z-index: 2;
    }

    .screen {
        width: 120px;
        height: 80px;
        background: #4A90E2;
        border-radius: 8px;
        border: 3px solid #2C3E50;
        position: relative;
        overflow: hidden;
    }

    .email-window {
        width: 100%;
        height: 100%;
        background: white;
        margin: 8px 0 0 0;
        border-radius: 4px;
    }

    .window-header {
        height: 12px;
        background: #F5F5F5;
        border-bottom: 1px solid #E0E0E0;
        display: flex;
        align-items: center;
        padding: 0 6px;
    }

    .window-controls {
        display: flex;
        gap: 3px;
    }

    .control-dot {
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background: #DDD;
    }

    .email-content {
        padding: 8px 6px;
    }

    .email-lines {
        display: flex;
        flex-direction: column;
        gap: 3px;
    }

    .email-line {
        height: 2px;
        background: #E0E0E0;
        border-radius: 1px;
    }

    .email-line.long {
        width: 80%;
    }

    .email-line.medium {
        width: 60%;
    }

    .email-line.short {
        width: 40%;
    }

    .monitor-stand {
        width: 20px;
        height: 15px;
        background: #2C3E50;
        margin: 0 auto;
        border-radius: 0 0 4px 4px;
    }

    .monitor-base {
        width: 40px;
        height: 8px;
        background: #2C3E50;
        margin: 0 auto;
        border-radius: 20px;
    }

    /* Floating Email Cards */
    .email-card {
        position: absolute;
        width: 60px;
        height: 45px;
        background: white;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        padding: 6px;
        z-index: 1;
    }

    .card-1 {
        top: 20px;
        left: 20px;
        background: #20D4C7;
        transform: rotate(-15deg);
    }

    .card-2 {
        bottom: 30px;
        right: 30px;
        background: #4A90E2;
        transform: rotate(10deg);
    }

    .card-header {
        width: 100%;
        height: 8px;
        background: rgba(255,255,255,0.3);
        border-radius: 2px;
        margin-bottom: 4px;
    }

    .card-lines {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .card-line {
        height: 2px;
        background: rgba(255,255,255,0.6);
        border-radius: 1px;
        width: 80%;
    }

    .content-section {
        text-align: center;
        margin-bottom: 40px;
    }

    .main-title {
        font-size: 24px;
        font-weight: 800;
        color: #000000;
        margin: 0 0 20px 0;
        line-height: 1.2;
    }

    .description {
        font-size: 16px;
        color: #666666;
        line-height: 1.5;
        margin: 0;
    }

    .button-section {
        width: 100%;
        max-width: 320px;
        margin-bottom: 30px;
    }

    .email-client-button {
        width: 100%;
        height: 50px;
        background-color: #87CEEB;
        color: #333333;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(135, 206, 235, 0.3);
    }

    .email-client-button:hover {
        background-color: #7BC4E8;
    }

    .footer-section {
        margin-top: auto;
        padding-top: 20px;
    }

    .footer-text {
        font-size: 14px;
        color: #999999;
        text-align: center;
        margin: 0;
        line-height: 1.4;
    }
</style>

@code {
    private void OpenEmailClient()
    {
        // Handle opening email client
        // This could open the default email app or navigate to next step
        Navigation.NavigateTo("/main");
    }
}
