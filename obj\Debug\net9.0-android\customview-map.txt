@string/appbar_scrolling_view_behavior;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\layout\fragment_backstack.xml
@string/appbar_scrolling_view_behavior;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\layout\navigationlayout.xml
adaptive-icon;obj\Debug\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon.xml
adaptive-icon;obj\Debug\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon_round.xml
alpha;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\161\jl\res\anim\enterfromleft.xml
alpha;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\161\jl\res\anim\exittoleft.xml
alpha;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\anim\nav_default_enter_anim.xml
alpha;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\anim\nav_default_exit_anim.xml
alpha;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\anim\nav_default_pop_enter_anim.xml
alpha;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\anim\nav_default_pop_exit_anim.xml
androidx.coordinatorlayout.widget.CoordinatorLayout;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\161\jl\res\layout\shellcontent.xml
androidx.coordinatorlayout.widget.CoordinatorLayout;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\layout\navigationlayout.xml
androidx.drawerlayout.widget.DrawerLayout;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\layout\drawer_layout.xml
androidx.fragment.app.FragmentContainerView;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\layout\fragment_backstack.xml
androidx.fragment.app.FragmentContainerView;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\layout\navigationlayout.xml
attr;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\values\attr.xml
background;obj\Debug\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon.xml
background;obj\Debug\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon_round.xml
bitmap;obj\Debug\net9.0-android\resizetizer\sp\drawable\maui_splash_image.xml
bitmap;obj\Debug\net9.0-android\resizetizer\sp\drawable-v31\maui_splash_image.xml
cache-path;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\164\jl\res\xml\microsoft_maui_essentials_fileprovider_file_paths.xml
color;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\values\colors.xml
color;obj\Debug\net9.0-android\res\values\colors.xml
color;obj\Debug\net9.0-android\resizetizer\sp\values\maui_colors.xml
com.google.android.material.appbar.AppBarLayout;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\161\jl\res\layout\flyoutcontent.xml
com.google.android.material.appbar.AppBarLayout;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\161\jl\res\layout\shellcontent.xml
com.google.android.material.appbar.AppBarLayout;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\layout\navigationlayout.xml
declare-styleable;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\values\attr.xml
external-cache-path;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\164\jl\res\xml\microsoft_maui_essentials_fileprovider_file_paths.xml
external-path;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\164\jl\res\xml\microsoft_maui_essentials_fileprovider_file_paths.xml
foreground;obj\Debug\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon.xml
foreground;obj\Debug\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon_round.xml
item;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\162\jl\res\values\values.xml
item;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\drawable\maui_splash.xml
item;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\values\styles.xml
item;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\values-v35\styles.xml
item;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\167\jl\res\values\values.xml
item;obj\Debug\net9.0-android\resizetizer\sp\drawable\maui_splash_image.xml
item;obj\Debug\net9.0-android\resizetizer\sp\drawable-v31\maui_splash_image.xml
layer-list;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\drawable\maui_splash.xml
layer-list;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\drawable\maui_splash_image.xml
layer-list;obj\Debug\net9.0-android\resizetizer\sp\drawable\maui_splash_image.xml
layer-list;obj\Debug\net9.0-android\resizetizer\sp\drawable-v31\maui_splash_image.xml
microsoft.maui.controls.platform.compatibility.ShellFlyoutLayout;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\161\jl\res\layout\flyoutcontent.xml
monochrome;obj\Debug\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon.xml
monochrome;obj\Debug\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon_round.xml
paths;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\164\jl\res\xml\microsoft_maui_essentials_fileprovider_file_paths.xml
resources;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\161\jl\res\values\strings.xml
resources;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\162\jl\res\values\values.xml
resources;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\values\attr.xml
resources;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\values\colors.xml
resources;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\values\styles.xml
resources;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\values-v35\styles.xml
resources;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\167\jl\res\values\values.xml
resources;obj\Debug\net9.0-android\res\values\colors.xml
resources;obj\Debug\net9.0-android\resizetizer\sp\values\maui_colors.xml
set;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\161\jl\res\anim\enterfromleft.xml
set;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\161\jl\res\anim\enterfromright.xml
set;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\161\jl\res\anim\exittoleft.xml
set;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\161\jl\res\anim\exittoright.xml
set;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\anim\nav_default_enter_anim.xml
set;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\anim\nav_default_exit_anim.xml
set;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\anim\nav_default_pop_enter_anim.xml
set;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\anim\nav_default_pop_exit_anim.xml
set;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\anim\nav_modal_default_enter_anim.xml
set;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\anim\nav_modal_default_exit_anim.xml
string;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\161\jl\res\values\strings.xml
string;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\162\jl\res\values\values.xml
style;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\values\styles.xml
style;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\values-v35\styles.xml
translate;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\161\jl\res\anim\enterfromleft.xml
translate;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\161\jl\res\anim\enterfromright.xml
translate;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\161\jl\res\anim\exittoleft.xml
translate;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\161\jl\res\anim\exittoright.xml
translate;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\anim\nav_default_enter_anim.xml
translate;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\anim\nav_default_exit_anim.xml
translate;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\anim\nav_default_pop_enter_anim.xml
translate;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\anim\nav_default_pop_exit_anim.xml
translate;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\anim\nav_modal_default_enter_anim.xml
translate;D:\Projects\GateSale\obj\Debug\net9.0-android\lp\163\jl\res\anim\nav_modal_default_exit_anim.xml
