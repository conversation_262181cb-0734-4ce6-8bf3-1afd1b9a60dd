{"Version": 1, "Hash": "/bLNX4gCJTHlb53+UHSGlPrVQNfHT79D4cIb3q5fs8k=", "Source": "GateSale", "BasePath": "/", "Mode": "Root", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "GateSale\\wwwroot", "Source": "GateSale", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "Pattern": "**"}], "Assets": [{"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\0qse1rb7ey-onmyqsxya3.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\", "BasePath": "/", "RelativePath": "css/tailwind#[.{fingerprint=onmyqsxya3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cj75ss3qkm", "Integrity": "aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint=6gzpyzhau4}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7l51xal3e9", "Integrity": "dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\", "BasePath": "/", "RelativePath": "css/input#[.{fingerprint=4jtf2pvl4b}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oh1840y9tw", "Integrity": "dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\okv3f23xy4-e5tk7yf482.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint=e5tk7yf482}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qfor6zdeny", "Integrity": "ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\uv3ua5xd0n-55aay04al6.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint=55aay04al6}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t8inowlh2q", "Integrity": "HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\index.html"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\x0awlxkggx-8inm30yfxf.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint=8inm30yfxf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m532s4naxw", "Integrity": "epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "e5tk7yf482", "Integrity": "bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6gzpyzhau4", "Integrity": "SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css.map"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/input#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4jtf2pvl4b", "Integrity": "QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\input.css"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/tailwind#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "onmyqsxya3", "Integrity": "NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\tailwind.css"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\images\\bagpack.jpg", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "images/bagpack#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k0z6ov860a", "Integrity": "xOoLewrcV24WWrZig0WoK6pOM/8ibw0qDYlAxUz/8UI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\bagpack.jpg"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\images\\gatesale-logo.jpg", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "images/gatesale-logo#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1k4dxodk5q", "Integrity": "UUlEhGO2LZgJCDHGa0JRAUd5vATDUM8nIcSfT2tPsns=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\gatesale-logo.jpg"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\images\\IMG-20250718-WA0005.jpg", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "images/IMG-20250718-WA0005#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "59aobo96p5", "Integrity": "F1uLNJm3kGCEmNH8p8blXl4SOGkgja83lBRE9Lz0qfw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\IMG-20250718-WA0005.jpg"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\images\\IMG-20250718-WA0008.jpg", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "images/IMG-20250718-WA0008#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "yybck5txrc", "Integrity": "mp+E7uF1kcokT6UusbZH66mccFYN0n56q1WBn8aCvFA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\IMG-20250718-WA0008.jpg"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\images\\IMG-20250718-WA0009.jpg", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "images/IMG-20250718-WA0009#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "itqnu1e2xx", "Integrity": "hsmAzWzQ4/UmXqCo3jL1dqgoHy8bu4BUKQlAGOXGcTg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\IMG-20250718-WA0009.jpg"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\images\\onboarding-1.jpg", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "images/onboarding-1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rw05rgbojm", "Integrity": "234oSg737ZpiLcPlvxkrDR/NPxTPadosJYscwqMks0U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\onboarding-1.jpg"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\images\\school.jpg", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "images/school#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "n6ysue7hqv", "Integrity": "8d1zgQSp+5LQQkENCAwA7UQGngPAcMulmWfI5ZQtVfU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\school.jpg"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\index.html", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "55aay04al6", "Integrity": "2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html"}], "Endpoints": [{"Route": "css/app.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\okv3f23xy4-e5tk7yf482.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000556173526"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1797"}, {"Name": "ETag", "Value": "\"ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3376"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\okv3f23xy4-e5tk7yf482.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1797"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A="}]}, {"Route": "css/app.e5tk7yf482.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\okv3f23xy4-e5tk7yf482.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000556173526"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1797"}, {"Name": "ETag", "Value": "\"ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "label", "Value": "css/app.css"}, {"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.e5tk7yf482.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3376"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "label", "Value": "css/app.css"}, {"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.e5tk7yf482.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\okv3f23xy4-e5tk7yf482.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1797"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "label", "Value": "css/app.css.gz"}, {"Name": "integrity", "Value": "sha256-ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A="}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041912905"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23858"}, {"Name": "ETag", "Value": "\"dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162726"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23858"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.gz"}, {"Name": "integrity", "Value": "sha256-dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8="}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041912905"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23858"}, {"Name": "ETag", "Value": "\"dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162726"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\x0awlxkggx-8inm30yfxf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013397104"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\x0awlxkggx-8inm30yfxf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E="}]}, {"Route": "css/bootstrap/bootstrap.min.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23858"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\x0awlxkggx-8inm30yfxf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013397104"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\x0awlxkggx-8inm30yfxf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E="}]}, {"Route": "css/input.4jtf2pvl4b.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003891050584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "ETag", "Value": "\"dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "label", "Value": "css/input.css"}, {"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.4jtf2pvl4b.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "476"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "label", "Value": "css/input.css"}, {"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.4jtf2pvl4b.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "label", "Value": "css/input.css.gz"}, {"Name": "integrity", "Value": "sha256-dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs="}]}, {"Route": "css/input.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003891050584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "ETag", "Value": "\"dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "476"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs="}]}, {"Route": "css/tailwind.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\0qse1rb7ey-onmyqsxya3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000189286390"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5282"}, {"Name": "ETag", "Value": "\"aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23768"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\0qse1rb7ey-onmyqsxya3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5282"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c="}]}, {"Route": "css/tailwind.onmyqsxya3.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\0qse1rb7ey-onmyqsxya3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000189286390"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5282"}, {"Name": "ETag", "Value": "\"aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "onmyqsxya3"}, {"Name": "label", "Value": "css/tailwind.css"}, {"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.onmyqsxya3.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23768"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "onmyqsxya3"}, {"Name": "label", "Value": "css/tailwind.css"}, {"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.onmyqsxya3.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\0qse1rb7ey-onmyqsxya3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5282"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "onmyqsxya3"}, {"Name": "label", "Value": "css/tailwind.css.gz"}, {"Name": "integrity", "Value": "sha256-aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c="}]}, {"Route": "images/bagpack.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\bagpack.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9109"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"xOoLewrcV24WWrZig0WoK6pOM/8ibw0qDYlAxUz/8UI=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xOoLewrcV24WWrZig0WoK6pOM/8ibw0qDYlAxUz/8UI="}]}, {"Route": "images/bagpack.k0z6ov860a.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\bagpack.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9109"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"xOoLewrcV24WWrZig0WoK6pOM/8ibw0qDYlAxUz/8UI=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k0z6ov860a"}, {"Name": "label", "Value": "images/bagpack.jpg"}, {"Name": "integrity", "Value": "sha256-xOoLewrcV24WWrZig0WoK6pOM/8ibw0qDYlAxUz/8UI="}]}, {"Route": "images/gatesale-logo.1k4dxodk5q.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\gatesale-logo.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18583"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"UUlEhGO2LZgJCDHGa0JRAUd5vATDUM8nIcSfT2tPsns=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1k4dxodk5q"}, {"Name": "label", "Value": "images/gatesale-logo.jpg"}, {"Name": "integrity", "Value": "sha256-UUlEhGO2LZgJCDHGa0JRAUd5vATDUM8nIcSfT2tPsns="}]}, {"Route": "images/gatesale-logo.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\gatesale-logo.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18583"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"UUlEhGO2LZgJCDHGa0JRAUd5vATDUM8nIcSfT2tPsns=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UUlEhGO2LZgJCDHGa0JRAUd5vATDUM8nIcSfT2tPsns="}]}, {"Route": "images/IMG-20250718-WA0005.59aobo96p5.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\IMG-20250718-WA0005.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"F1uLNJm3kGCEmNH8p8blXl4SOGkgja83lBRE9Lz0qfw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "59aobo96p5"}, {"Name": "label", "Value": "images/IMG-20250718-WA0005.jpg"}, {"Name": "integrity", "Value": "sha256-F1uLNJm3kGCEmNH8p8blXl4SOGkgja83lBRE9Lz0qfw="}]}, {"Route": "images/IMG-20250718-WA0005.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\IMG-20250718-WA0005.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"F1uLNJm3kGCEmNH8p8blXl4SOGkgja83lBRE9Lz0qfw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-F1uLNJm3kGCEmNH8p8blXl4SOGkgja83lBRE9Lz0qfw="}]}, {"Route": "images/IMG-20250718-WA0008.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\IMG-20250718-WA0008.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24367"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"mp+E7uF1kcokT6UusbZH66mccFYN0n56q1WBn8aCvFA=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mp+E7uF1kcokT6UusbZH66mccFYN0n56q1WBn8aCvFA="}]}, {"Route": "images/IMG-20250718-WA0008.yybck5txrc.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\IMG-20250718-WA0008.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24367"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"mp+E7uF1kcokT6UusbZH66mccFYN0n56q1WBn8aCvFA=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yybck5txrc"}, {"Name": "label", "Value": "images/IMG-20250718-WA0008.jpg"}, {"Name": "integrity", "Value": "sha256-mp+E7uF1kcokT6UusbZH66mccFYN0n56q1WBn8aCvFA="}]}, {"Route": "images/IMG-20250718-WA0009.itqnu1e2xx.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\IMG-20250718-WA0009.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10423"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"hsmAzWzQ4/UmXqCo3jL1dqgoHy8bu4BUKQlAGOXGcTg=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "itqnu1e2xx"}, {"Name": "label", "Value": "images/IMG-20250718-WA0009.jpg"}, {"Name": "integrity", "Value": "sha256-hsmAzWzQ4/UmXqCo3jL1dqgoHy8bu4BUKQlAGOXGcTg="}]}, {"Route": "images/IMG-20250718-WA0009.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\IMG-20250718-WA0009.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10423"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"hsmAzWzQ4/UmXqCo3jL1dqgoHy8bu4BUKQlAGOXGcTg=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hsmAzWzQ4/UmXqCo3jL1dqgoHy8bu4BUKQlAGOXGcTg="}]}, {"Route": "images/onboarding-1.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\onboarding-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "41637"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"234oSg737ZpiLcPlvxkrDR/NPxTPadosJYscwqMks0U=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-234oSg737ZpiLcPlvxkrDR/NPxTPadosJYscwqMks0U="}]}, {"Route": "images/onboarding-1.rw05rgbojm.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\onboarding-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "41637"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"234oSg737ZpiLcPlvxkrDR/NPxTPadosJYscwqMks0U=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rw05rgbojm"}, {"Name": "label", "Value": "images/onboarding-1.jpg"}, {"Name": "integrity", "Value": "sha256-234oSg737ZpiLcPlvxkrDR/NPxTPadosJYscwqMks0U="}]}, {"Route": "images/school.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\school.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "46823"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"8d1zgQSp+5LQQkENCAwA7UQGngPAcMulmWfI5ZQtVfU=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8d1zgQSp+5LQQkENCAwA7UQGngPAcMulmWfI5ZQtVfU="}]}, {"Route": "images/school.n6ysue7hqv.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\school.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "46823"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"8d1zgQSp+5LQQkENCAwA7UQGngPAcMulmWfI5ZQtVfU=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n6ysue7hqv"}, {"Name": "label", "Value": "images/school.jpg"}, {"Name": "integrity", "Value": "sha256-8d1zgQSp+5LQQkENCAwA7UQGngPAcMulmWfI5ZQtVfU="}]}, {"Route": "index.55aay04al6.html", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\uv3ua5xd0n-55aay04al6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002309468822"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "432"}, {"Name": "ETag", "Value": "\"HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.55aay04al6.html", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.55aay04al6.html.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\uv3ua5xd0n-55aay04al6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "432"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4="}]}, {"Route": "index.html", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\uv3ua5xd0n-55aay04al6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002309468822"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "432"}, {"Name": "ETag", "Value": "\"HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.html", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.html.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\uv3ua5xd0n-55aay04al6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "432"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4="}]}]}