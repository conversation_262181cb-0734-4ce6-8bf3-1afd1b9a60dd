<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome Page - Exact Replica</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            background: linear-gradient(90deg, #FFFFFF 0%, #F8FCFF 40%, #F0F8FF 70%, #E8F4FD 100%);
            border-radius: 25px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
            overflow: hidden;
            position: relative;
        }

        .onboarding-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif;
            background: linear-gradient(90deg, #FFFFFF 0%, #F8FCFF 40%, #F0F8FF 70%, #E8F4FD 100%);
            height: 100%;
            justify-content: space-between;
            width: 100%;
            box-sizing: border-box;
            position: relative;
        }

        .content-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            justify-content: center;
            width: 100%;
        }

        .logo-container {
            margin-top: 40px;
            margin-bottom: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .logo-icon {
            margin-bottom: 12px;
        }

        .logo-image {
            width: 165px;
            height: 121px;
            object-fit: contain;
        }

        .tagline {
            margin: 16px 0 32px 0;
            font-size: 16px;
            color: #666;
            text-align: center;
            font-weight: 400;
        }

        .illustration-card {
            position: relative;
            margin: 0 0 40px 0;
            padding: 16px;
            border-radius: 20px;
            background: linear-gradient(135deg, #E8F4F8 0%, #F0F8FF 100%);
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            max-height: 400px;
            width: 100%;
            max-width: 320px;
        }

        .illustration-img {
            width: 280px;
            height: 240px;
            border-radius: 15px;
            object-fit: cover;
        }

        .icon-heart {
            position: absolute;
            top: 12px;
            right: 12px;
            background-color: #4CAF50;
            color: white;
            border-radius: 50%;
            padding: 8px;
            font-size: 18px;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 3px 8px rgba(76, 175, 80, 0.3);
        }

        .icon-shield {
            position: absolute;
            bottom: 12px;
            left: 12px;
            background-color: #2196F3;
            color: white;
            border-radius: 50%;
            padding: 8px;
            font-size: 18px;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 3px 8px rgba(33, 150, 243, 0.3);
        }

        .bottom-section {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
            margin-top: auto;
            padding-bottom: 20px;
        }

        .next-button {
            width: 100%;
            max-width: 320px;
            background-color: #00BFFF;
            color: white;
            padding: 14px 20px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            box-shadow: 0 3px 6px rgba(0, 191, 255, 0.3);
            cursor: pointer;
            margin-bottom: 12px;
        }

        .next-button:hover {
            background-color: #1C7ED6;
        }

        .skip-text {
            font-size: 14px;
            color: #999;
            margin-bottom: 16px;
            cursor: pointer;
        }

        .skip-text:hover {
            color: #666;
        }

        .progress-dots {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
        }

        .dot {
            width: 8px;
            height: 8px;
            background-color: #e0e0e0;
            border-radius: 50%;
        }

        .dot.active {
            width: 24px;
            border-radius: 4px;
            background-color: #00BFFF;
        }

        /* Placeholder for logo image */
        .logo-placeholder {
            width: 165px;
            height: 121px;
            background: linear-gradient(135deg, #4ECDC4 0%, #FF8A65 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: 800;
            text-align: center;
        }

        /* Placeholder for illustration */
        .illustration-placeholder {
            width: 280px;
            height: 240px;
            background: linear-gradient(45deg, #FFE0B2, #FFCC80);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #FF8A65;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="phone-frame">
        <div class="onboarding-container">
            <!-- Content Section -->
            <div class="content-section">
                <!-- Logo -->
                <div class="logo-container">
                    <div class="logo-icon">
                        <div class="logo-placeholder">
                            <span style="color:#4ECDC4;">GATE</span><span style="color:#FF8A65;">SALE</span>
                        </div>
                    </div>
                </div>

                <!-- Tagline -->
                <p class="tagline">A safe marketplace just for students.</p>

                <!-- Illustration Card -->
                <div class="illustration-card">
                    <!-- Onboarding illustration -->
                    <div class="illustration-placeholder">
                        Students connecting safely
                    </div>

                    <!-- Icons -->
                    <div class="icon-heart">♥</div>
                    <div class="icon-shield">🛡️</div>
                </div>
            </div>

            <!-- Bottom Section -->
            <div class="bottom-section">
                <!-- Button -->
                <button class="next-button">Next</button>

                <!-- Skip text -->
                <p class="skip-text">Skip for now</p>

                <!-- Progress dots -->
                <div class="progress-dots">
                    <span class="dot active"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
