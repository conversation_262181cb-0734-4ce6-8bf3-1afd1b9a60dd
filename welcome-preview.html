<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GateSale - Welcome Screen Preview (Clean)</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .welcome-container {
            width: 375px;
            height: 812px;
            background: #FFFFFF;
            margin: 0 auto;
            position: relative;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 20px;
        }

        /* Content */
        .content {
            padding: 80px 30px 30px 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100%;
        }

        /* Logo Section */
        .logo-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 15px;
        }

        .logo-icon {
            margin-bottom: 12px;
        }

        .app-title {
            font-size: 28px;
            font-weight: 700;
            margin: 0;
            letter-spacing: 0.5px;
        }

        .gate {
            color: #4ECDC4;
        }

        .sale {
            color: #FF6B6B;
        }

        /* Tagline */
        .tagline {
            font-size: 14px;
            color: #666666;
            text-align: center;
            margin: 0 0 50px 0;
            line-height: 1.4;
            font-weight: 400;
        }

        /* Illustration Container */
        .illustration-container {
            position: relative;
            width: 300px;
            height: 300px;
            margin-bottom: 50px;
        }

        .background-square {
            position: absolute;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #E8F4F8 0%, #F0F8FF 100%);
            border-radius: 24px;
            top: 0;
            left: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
        }

        .illustration {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2;
        }

        /* Students */
        .student {
            position: absolute;
            width: 80px;
            height: 160px;
        }

        .student-left {
            left: 40px;
            top: 60px;
        }

        .student-right {
            right: 40px;
            top: 60px;
        }

        /* Student Parts */
        .head {
            position: relative;
            width: 50px;
            height: 50px;
            margin: 0 auto 5px;
        }

        .hair {
            position: absolute;
            width: 50px;
            height: 40px;
            border-radius: 25px 25px 18px 18px;
            top: 0;
        }

        .hair-brown {
            background: #8B4513;
        }

        .hair-dark-curly {
            background: #2C1810;
            border-radius: 25px 25px 22px 22px;
        }

        .face {
            position: absolute;
            width: 42px;
            height: 42px;
            background: #FDBCB4;
            border-radius: 21px;
            top: 15px;
            left: 4px;
        }

        .face-dark {
            background: #8D5524;
        }

        .eyes {
            position: absolute;
            top: 22px;
            left: 10px;
            width: 32px;
            height: 8px;
        }

        .eye {
            position: absolute;
            width: 3px;
            height: 3px;
            background: #000;
            border-radius: 50%;
        }

        .eye-left {
            left: 8px;
        }

        .eye-right {
            right: 8px;
        }

        .mouth {
            position: absolute;
            top: 32px;
            left: 20px;
            width: 6px;
            height: 3px;
            background: #FF6B6B;
            border-radius: 0 0 6px 6px;
        }

        .body {
            position: relative;
            width: 55px;
            height: 70px;
            margin: 0 auto;
        }

        .jacket {
            width: 55px;
            height: 50px;
            border-radius: 12px 12px 0 0;
            position: relative;
            z-index: 2;
        }

        .jacket-teal {
            background: #20B2AA;
        }

        .jacket-orange {
            background: #FF8C00;
        }

        .inner-shirt {
            position: absolute;
            width: 35px;
            height: 25px;
            top: 20px;
            left: 10px;
            border-radius: 8px 8px 0 0;
            z-index: 1;
        }

        .inner-shirt-red {
            background: #FF6B6B;
        }

        .inner-shirt-yellow {
            background: #FFD700;
        }

        .arms {
            position: absolute;
            top: 15px;
            width: 100%;
            height: 35px;
        }

        .arm {
            position: absolute;
            width: 18px;
            height: 40px;
            background: #FDBCB4;
            border-radius: 9px;
        }

        .student-left .arm {
            background: #FDBCB4;
        }

        .student-right .arm {
            background: #8D5524;
        }

        .arm-left {
            left: -10px;
            transform: rotate(-15deg);
        }

        .arm-right {
            right: -10px;
            transform: rotate(15deg);
        }

        .extending {
            transform: rotate(0deg) !important;
        }

        .student-left .arm-right.extending {
            right: -25px;
            transform: rotate(-30deg);
        }

        .student-right .arm-left.extending {
            left: -25px;
            transform: rotate(30deg);
        }

        .legs {
            position: relative;
            width: 50px;
            height: 45px;
            margin: 0 auto;
        }

        .pants {
            width: 50px;
            height: 40px;
            border-radius: 0 0 12px 12px;
        }

        .pants-beige {
            background: #D2B48C;
        }

        .pants-blue {
            background: #4169E1;
        }

        .backpack {
            position: absolute;
            width: 28px;
            height: 35px;
            border-radius: 8px;
            top: 50px;
            right: -12px;
        }

        .backpack-red {
            background: #DC143C;
        }

        .handshake-area {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 20px;
            z-index: 3;
        }

        /* Floating Icons */
        .floating-icon {
            position: absolute;
            z-index: 4;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .heart-icon {
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
        }

        .shield-icon {
            bottom: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
        }

        /* Next Button */
        .next-button {
            width: 300px;
            height: 52px;
            background: #00BFFF;
            border: none;
            border-radius: 26px;
            color: #FFFFFF;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 16px;
            transition: background-color 0.2s;
            box-shadow: 0 4px 16px rgba(0, 191, 255, 0.3);
        }

        .next-button:hover {
            background: #0099CC;
        }

        /* Skip Text */
        .skip-text {
            font-size: 16px;
            color: #999999;
            margin: 0 0 40px 0;
            cursor: pointer;
            font-weight: 400;
        }

        /* Page Indicators */
        .page-indicators {
            display: flex;
            gap: 8px;
            margin-top: auto;
            padding-bottom: 20px;
        }

        .indicator {
            width: 8px;
            height: 8px;
            border-radius: 4px;
            background: #E0E0E0;
            transition: all 0.3s ease;
        }

        .indicator.active {
            background: #00BFFF;
            width: 24px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <!-- Main Content -->
        <div class="content">
            <!-- Logo Section -->
            <div class="logo-section">
                <img src="images/gatesale-logo.png" alt="GateSale Logo" style="width: 128px; height: 96px;" />
            </div>

            <!-- Tagline -->
            <p class="tagline">A safe marketplace just for students.</p>

            <!-- Illustration -->
            <div class="illustration-container">
                <div class="background-square"></div>
                <div class="illustration">
                    <!-- Left Student (Boy) -->
                    <div class="student student-left">
                        <div class="head">
                            <div class="hair hair-brown"></div>
                            <div class="face"></div>
                            <div class="eyes">
                                <div class="eye eye-left"></div>
                                <div class="eye eye-right"></div>
                            </div>
                            <div class="mouth"></div>
                        </div>
                        <div class="body">
                            <div class="jacket jacket-teal"></div>
                            <div class="inner-shirt inner-shirt-red"></div>
                            <div class="arms">
                                <div class="arm arm-left"></div>
                                <div class="arm arm-right extending"></div>
                            </div>
                        </div>
                        <div class="legs">
                            <div class="pants pants-beige"></div>
                        </div>
                        <div class="backpack backpack-red"></div>
                    </div>

                    <!-- Right Student (Girl) -->
                    <div class="student student-right">
                        <div class="head">
                            <div class="hair hair-dark-curly"></div>
                            <div class="face face-dark"></div>
                            <div class="eyes">
                                <div class="eye eye-left"></div>
                                <div class="eye eye-right"></div>
                            </div>
                            <div class="mouth"></div>
                        </div>
                        <div class="body">
                            <div class="jacket jacket-orange"></div>
                            <div class="inner-shirt inner-shirt-yellow"></div>
                            <div class="arms">
                                <div class="arm arm-left extending"></div>
                                <div class="arm arm-right"></div>
                            </div>
                        </div>
                        <div class="legs">
                            <div class="pants pants-blue"></div>
                        </div>
                    </div>

                    <!-- Handshake area -->
                    <div class="handshake-area"></div>
                </div>

                <!-- Floating Icons -->
                <div class="floating-icon heart-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" fill="#4CAF50"/>
                    </svg>
                </div>
                <div class="floating-icon shield-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.5C15.4,11.5 16,12.1 16,12.7V16.2C16,16.8 15.4,17.3 14.8,17.3H9.2C8.6,17.3 8,16.8 8,16.2V12.7C8,12.1 8.6,11.5 9.2,11.5V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.5,8.7 10.5,10V11.5H13.5V10C13.5,8.7 12.8,8.2 12,8.2Z" fill="#2196F3"/>
                    </svg>
                </div>
            </div>

            <!-- Next Button -->
            <button class="next-button">Next</button>

            <!-- Skip Text -->
            <p class="skip-text">Skip for now</p>

            <!-- Page Indicators -->
            <div class="page-indicators">
                <span class="indicator active"></span>
                <span class="indicator"></span>
                <span class="indicator"></span>
            </div>
        </div>
    </div>
</body>
</html>
