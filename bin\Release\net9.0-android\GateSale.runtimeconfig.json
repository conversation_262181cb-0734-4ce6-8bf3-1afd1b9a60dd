{"runtimeOptions": {"tfm": "net9.0", "frameworks": [{"name": "Microsoft.NETCore.App", "version": "9.0.0"}, {"name": "Microsoft.Android", "version": ""}], "configProperties": {"Microsoft.Extensions.DependencyInjection.VerifyOpenGenericServiceTrimmability": false, "System.ComponentModel.DefaultValueAttribute.IsSupported": true, "System.ComponentModel.Design.IDesignerHost.IsSupported": false, "System.ComponentModel.TypeConverter.EnableUnsafeBinaryFormatterInDesigntimeLicenseContextSerialization": false, "System.ComponentModel.TypeDescriptor.IsComObjectDescriptorSupported": false, "System.Diagnostics.Debugger.IsSupported": false, "System.Diagnostics.Metrics.Meter.IsSupported": false, "System.Diagnostics.Tracing.EventSource.IsSupported": false, "System.Globalization.Invariant": false, "System.Net.Http.EnableActivityPropagation": false, "System.Net.Http.UseNativeHttpHandler": true, "System.Reflection.Metadata.MetadataUpdater.IsSupported": false, "System.Resources.ResourceManager.AllowCustomResourceTypes": false, "System.Resources.UseSystemResourceKeys": true, "System.Runtime.CompilerServices.RuntimeFeature.IsDynamicCodeSupported": true, "System.Runtime.InteropServices.BuiltInComInterop.IsSupported": false, "System.Runtime.InteropServices.EnableConsumingManagedCodeFromNativeHosting": false, "System.Runtime.InteropServices.EnableCppCLIHostActivation": false, "System.Runtime.InteropServices.Marshalling.EnableGeneratedComInterfaceComImportInterop": false, "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false, "System.StartupHookProvider.IsSupported": false, "System.Text.Encoding.EnableUnsafeUTF7Encoding": false, "System.Text.Json.JsonSerializer.IsReflectionEnabledByDefault": true, "System.Threading.Thread.EnableAutoreleasePool": false, "Xamarin.Android.Net.UseNegotiateAuthentication": false, "Switch.System.Reflection.ForceInterpretedInvoke": true, "Microsoft.Extensions.DependencyInjection.DisableDynamicEngine": true, "Microsoft.Maui.RuntimeFeature.IsIVisualAssemblyScanningEnabled": false, "Microsoft.Maui.RuntimeFeature.AreBindingInterceptorsSupported": true}}}