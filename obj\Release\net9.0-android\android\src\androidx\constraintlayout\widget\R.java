/* AUTO-GENERATED FILE. DO NOT MODIFY.
 *
 * This class was automatically generated by
 * .NET for Android from the resource data it found.
 * It should not be modified by hand.
 */
package androidx.constraintlayout.widget;

public final class R {
	public static final class attr {
		public static final int SharedValue = 0x7f030000;
		public static final int SharedValueId = 0x7f030001;
		public static final int altSrc = 0x7f030033;
		public static final int animateCircleAngleTo = 0x7f030035;
		public static final int animateRelativeTo = 0x7f030038;
		public static final int applyMotionScene = 0x7f03003c;
		public static final int arcMode = 0x7f03003d;
		public static final int attributeName = 0x7f030041;
		public static final int autoCompleteMode = 0x7f030043;
		public static final int autoTransition = 0x7f03004b;
		public static final int barrierAllowsGoneWidgets = 0x7f03006a;
		public static final int barrierDirection = 0x7f03006b;
		public static final int barrierMargin = 0x7f03006c;
		public static final int blendSrc = 0x7f030079;
		public static final int borderRound = 0x7f03007a;
		public static final int borderRoundPercent = 0x7f03007b;
		public static final int brightness = 0x7f030090;
		public static final int carousel_backwardTransition = 0x7f0300aa;
		public static final int carousel_emptyViewsBehavior = 0x7f0300ab;
		public static final int carousel_firstView = 0x7f0300ac;
		public static final int carousel_forwardTransition = 0x7f0300ad;
		public static final int carousel_infinite = 0x7f0300ae;
		public static final int carousel_nextState = 0x7f0300af;
		public static final int carousel_previousState = 0x7f0300b0;
		public static final int carousel_touchUpMode = 0x7f0300b1;
		public static final int carousel_touchUp_dampeningFactor = 0x7f0300b2;
		public static final int carousel_touchUp_velocityThreshold = 0x7f0300b3;
		public static final int chainUseRtl = 0x7f0300b5;
		public static final int circleRadius = 0x7f0300d9;
		public static final int circularflow_angles = 0x7f0300db;
		public static final int circularflow_defaultAngle = 0x7f0300dc;
		public static final int circularflow_defaultRadius = 0x7f0300dd;
		public static final int circularflow_radiusInDP = 0x7f0300de;
		public static final int circularflow_viewCenter = 0x7f0300df;
		public static final int clearsTag = 0x7f0300e1;
		public static final int clickAction = 0x7f0300e2;
		public static final int constraintRotate = 0x7f03013a;
		public static final int constraintSet = 0x7f03013b;
		public static final int constraintSetEnd = 0x7f03013c;
		public static final int constraintSetStart = 0x7f03013d;
		public static final int constraint_referenced_ids = 0x7f03013e;
		public static final int constraint_referenced_tags = 0x7f03013f;
		public static final int constraints = 0x7f030140;
		public static final int content = 0x7f030141;
		public static final int contrast = 0x7f030151;
		public static final int crossfade = 0x7f030166;
		public static final int currentState = 0x7f030167;
		public static final int curveFit = 0x7f03016a;
		public static final int customBoolean = 0x7f03016b;
		public static final int customColorDrawableValue = 0x7f03016c;
		public static final int customColorValue = 0x7f03016d;
		public static final int customDimension = 0x7f03016e;
		public static final int customFloatValue = 0x7f03016f;
		public static final int customIntegerValue = 0x7f030170;
		public static final int customPixelDimension = 0x7f030172;
		public static final int customReference = 0x7f030173;
		public static final int customStringValue = 0x7f030174;
		public static final int defaultDuration = 0x7f03017b;
		public static final int defaultState = 0x7f030180;
		public static final int deltaPolarAngle = 0x7f030181;
		public static final int deltaPolarRadius = 0x7f030182;
		public static final int deriveConstraintsFrom = 0x7f030183;
		public static final int dragDirection = 0x7f030191;
		public static final int dragScale = 0x7f030192;
		public static final int dragThreshold = 0x7f030193;
		public static final int drawPath = 0x7f030194;
		public static final int duration = 0x7f0301a4;
		public static final int flow_firstHorizontalBias = 0x7f0301fa;
		public static final int flow_firstHorizontalStyle = 0x7f0301fb;
		public static final int flow_firstVerticalBias = 0x7f0301fc;
		public static final int flow_firstVerticalStyle = 0x7f0301fd;
		public static final int flow_horizontalAlign = 0x7f0301fe;
		public static final int flow_horizontalBias = 0x7f0301ff;
		public static final int flow_horizontalGap = 0x7f030200;
		public static final int flow_horizontalStyle = 0x7f030201;
		public static final int flow_lastHorizontalBias = 0x7f030202;
		public static final int flow_lastHorizontalStyle = 0x7f030203;
		public static final int flow_lastVerticalBias = 0x7f030204;
		public static final int flow_lastVerticalStyle = 0x7f030205;
		public static final int flow_maxElementsWrap = 0x7f030206;
		public static final int flow_padding = 0x7f030207;
		public static final int flow_verticalAlign = 0x7f030208;
		public static final int flow_verticalBias = 0x7f030209;
		public static final int flow_verticalGap = 0x7f03020a;
		public static final int flow_verticalStyle = 0x7f03020b;
		public static final int flow_wrapMode = 0x7f03020c;
		public static final int framePosition = 0x7f03021d;
		public static final int grid_columnWeights = 0x7f030222;
		public static final int grid_columns = 0x7f030223;
		public static final int grid_horizontalGaps = 0x7f030224;
		public static final int grid_orientation = 0x7f030225;
		public static final int grid_rowWeights = 0x7f030226;
		public static final int grid_rows = 0x7f030227;
		public static final int grid_skips = 0x7f030228;
		public static final int grid_spans = 0x7f030229;
		public static final int grid_useRtl = 0x7f03022a;
		public static final int grid_validateInputs = 0x7f03022b;
		public static final int grid_verticalGaps = 0x7f03022c;
		public static final int guidelineUseRtl = 0x7f03022d;
		public static final int ifTagNotSet = 0x7f03024d;
		public static final int ifTagSet = 0x7f03024e;
		public static final int imagePanX = 0x7f030250;
		public static final int imagePanY = 0x7f030251;
		public static final int imageRotate = 0x7f030252;
		public static final int imageZoom = 0x7f030253;
		public static final int keyPositionType = 0x7f030280;
		public static final int layoutDescription = 0x7f03028c;
		public static final int layoutDuringTransition = 0x7f03028d;
		public static final int layout_constrainedHeight = 0x7f030294;
		public static final int layout_constrainedWidth = 0x7f030295;
		public static final int layout_constraintBaseline_creator = 0x7f030296;
		public static final int layout_constraintBaseline_toBaselineOf = 0x7f030297;
		public static final int layout_constraintBaseline_toBottomOf = 0x7f030298;
		public static final int layout_constraintBaseline_toTopOf = 0x7f030299;
		public static final int layout_constraintBottom_creator = 0x7f03029a;
		public static final int layout_constraintBottom_toBottomOf = 0x7f03029b;
		public static final int layout_constraintBottom_toTopOf = 0x7f03029c;
		public static final int layout_constraintCircle = 0x7f03029d;
		public static final int layout_constraintCircleAngle = 0x7f03029e;
		public static final int layout_constraintCircleRadius = 0x7f03029f;
		public static final int layout_constraintDimensionRatio = 0x7f0302a0;
		public static final int layout_constraintEnd_toEndOf = 0x7f0302a1;
		public static final int layout_constraintEnd_toStartOf = 0x7f0302a2;
		public static final int layout_constraintGuide_begin = 0x7f0302a3;
		public static final int layout_constraintGuide_end = 0x7f0302a4;
		public static final int layout_constraintGuide_percent = 0x7f0302a5;
		public static final int layout_constraintHeight = 0x7f0302a6;
		public static final int layout_constraintHeight_default = 0x7f0302a7;
		public static final int layout_constraintHeight_max = 0x7f0302a8;
		public static final int layout_constraintHeight_min = 0x7f0302a9;
		public static final int layout_constraintHeight_percent = 0x7f0302aa;
		public static final int layout_constraintHorizontal_bias = 0x7f0302ab;
		public static final int layout_constraintHorizontal_chainStyle = 0x7f0302ac;
		public static final int layout_constraintHorizontal_weight = 0x7f0302ad;
		public static final int layout_constraintLeft_creator = 0x7f0302ae;
		public static final int layout_constraintLeft_toLeftOf = 0x7f0302af;
		public static final int layout_constraintLeft_toRightOf = 0x7f0302b0;
		public static final int layout_constraintRight_creator = 0x7f0302b1;
		public static final int layout_constraintRight_toLeftOf = 0x7f0302b2;
		public static final int layout_constraintRight_toRightOf = 0x7f0302b3;
		public static final int layout_constraintStart_toEndOf = 0x7f0302b4;
		public static final int layout_constraintStart_toStartOf = 0x7f0302b5;
		public static final int layout_constraintTag = 0x7f0302b6;
		public static final int layout_constraintTop_creator = 0x7f0302b7;
		public static final int layout_constraintTop_toBottomOf = 0x7f0302b8;
		public static final int layout_constraintTop_toTopOf = 0x7f0302b9;
		public static final int layout_constraintVertical_bias = 0x7f0302ba;
		public static final int layout_constraintVertical_chainStyle = 0x7f0302bb;
		public static final int layout_constraintVertical_weight = 0x7f0302bc;
		public static final int layout_constraintWidth = 0x7f0302bd;
		public static final int layout_constraintWidth_default = 0x7f0302be;
		public static final int layout_constraintWidth_max = 0x7f0302bf;
		public static final int layout_constraintWidth_min = 0x7f0302c0;
		public static final int layout_constraintWidth_percent = 0x7f0302c1;
		public static final int layout_editor_absoluteX = 0x7f0302c3;
		public static final int layout_editor_absoluteY = 0x7f0302c4;
		public static final int layout_goneMarginBaseline = 0x7f0302c5;
		public static final int layout_goneMarginBottom = 0x7f0302c6;
		public static final int layout_goneMarginEnd = 0x7f0302c7;
		public static final int layout_goneMarginLeft = 0x7f0302c8;
		public static final int layout_goneMarginRight = 0x7f0302c9;
		public static final int layout_goneMarginStart = 0x7f0302ca;
		public static final int layout_goneMarginTop = 0x7f0302cb;
		public static final int layout_marginBaseline = 0x7f0302ce;
		public static final int layout_optimizationLevel = 0x7f0302cf;
		public static final int layout_wrapBehaviorInParent = 0x7f0302d3;
		public static final int limitBoundsTo = 0x7f0302d7;
		public static final int maxAcceleration = 0x7f030323;
		public static final int maxHeight = 0x7f030327;
		public static final int maxVelocity = 0x7f03032b;
		public static final int maxWidth = 0x7f03032c;
		public static final int methodName = 0x7f030331;
		public static final int minHeight = 0x7f030333;
		public static final int minWidth = 0x7f030337;
		public static final int mock_diagonalsColor = 0x7f030338;
		public static final int mock_label = 0x7f030339;
		public static final int mock_labelBackgroundColor = 0x7f03033a;
		public static final int mock_labelColor = 0x7f03033b;
		public static final int mock_showDiagonals = 0x7f03033c;
		public static final int mock_showLabel = 0x7f03033d;
		public static final int motionDebug = 0x7f03033e;
		public static final int motionEffect_alpha = 0x7f03035b;
		public static final int motionEffect_end = 0x7f03035c;
		public static final int motionEffect_move = 0x7f03035d;
		public static final int motionEffect_start = 0x7f03035e;
		public static final int motionEffect_strict = 0x7f03035f;
		public static final int motionEffect_translationX = 0x7f030360;
		public static final int motionEffect_translationY = 0x7f030361;
		public static final int motionEffect_viewTransition = 0x7f030362;
		public static final int motionInterpolator = 0x7f030363;
		public static final int motionPathRotate = 0x7f030365;
		public static final int motionProgress = 0x7f030366;
		public static final int motionStagger = 0x7f030367;
		public static final int motionTarget = 0x7f030368;
		public static final int motion_postLayoutCollision = 0x7f030369;
		public static final int motion_triggerOnCollision = 0x7f03036a;
		public static final int moveWhenScrollAtTop = 0x7f03036b;
		public static final int nestedScrollFlags = 0x7f030374;
		public static final int onCross = 0x7f03037b;
		public static final int onHide = 0x7f03037c;
		public static final int onNegativeCross = 0x7f03037d;
		public static final int onPositiveCross = 0x7f03037e;
		public static final int onShow = 0x7f03037f;
		public static final int onStateTransition = 0x7f030380;
		public static final int onTouchUp = 0x7f030381;
		public static final int overlay = 0x7f030383;
		public static final int pathMotionArc = 0x7f030395;
		public static final int path_percent = 0x7f030396;
		public static final int percentHeight = 0x7f030397;
		public static final int percentWidth = 0x7f030398;
		public static final int percentX = 0x7f030399;
		public static final int percentY = 0x7f03039a;
		public static final int perpendicularPath_percent = 0x7f03039b;
		public static final int pivotAnchor = 0x7f03039c;
		public static final int placeholder_emptyVisibility = 0x7f0303a1;
		public static final int polarRelativeTo = 0x7f0303a2;
		public static final int quantizeMotionInterpolator = 0x7f0303b4;
		public static final int quantizeMotionPhase = 0x7f0303b5;
		public static final int quantizeMotionSteps = 0x7f0303b6;
		public static final int reactiveGuide_animateChange = 0x7f0303bf;
		public static final int reactiveGuide_applyToAllConstraintSets = 0x7f0303c0;
		public static final int reactiveGuide_applyToConstraintSet = 0x7f0303c1;
		public static final int reactiveGuide_valueId = 0x7f0303c2;
		public static final int region_heightLessThan = 0x7f0303c4;
		public static final int region_heightMoreThan = 0x7f0303c5;
		public static final int region_widthLessThan = 0x7f0303c6;
		public static final int region_widthMoreThan = 0x7f0303c7;
		public static final int rotationCenterId = 0x7f0303cc;
		public static final int round = 0x7f0303cd;
		public static final int roundPercent = 0x7f0303ce;
		public static final int saturation = 0x7f0303d0;
		public static final int scaleFromTextSize = 0x7f0303d1;
		public static final int setsTag = 0x7f0303e1;
		public static final int showPaths = 0x7f0303f5;
		public static final int sizePercent = 0x7f030402;
		public static final int springBoundary = 0x7f030413;
		public static final int springDamping = 0x7f030414;
		public static final int springMass = 0x7f030415;
		public static final int springStiffness = 0x7f030416;
		public static final int springStopThreshold = 0x7f030417;
		public static final int staggered = 0x7f03041a;
		public static final int stateLabels = 0x7f030423;
		public static final int targetId = 0x7f030465;
		public static final int telltales_tailColor = 0x7f030467;
		public static final int telltales_tailScale = 0x7f030468;
		public static final int telltales_velocityMode = 0x7f030469;
		public static final int textBackground = 0x7f030490;
		public static final int textBackgroundPanX = 0x7f030491;
		public static final int textBackgroundPanY = 0x7f030492;
		public static final int textBackgroundRotate = 0x7f030493;
		public static final int textBackgroundZoom = 0x7f030494;
		public static final int textFillColor = 0x7f030498;
		public static final int textOutlineColor = 0x7f0304a2;
		public static final int textOutlineThickness = 0x7f0304a3;
		public static final int textPanX = 0x7f0304a4;
		public static final int textPanY = 0x7f0304a5;
		public static final int textureBlurFactor = 0x7f0304a7;
		public static final int textureEffect = 0x7f0304a8;
		public static final int textureHeight = 0x7f0304a9;
		public static final int textureWidth = 0x7f0304aa;
		public static final int touchAnchorId = 0x7f0304e1;
		public static final int touchAnchorSide = 0x7f0304e2;
		public static final int touchRegionId = 0x7f0304e3;
		public static final int transformPivotTarget = 0x7f0304f2;
		public static final int transitionDisable = 0x7f0304f3;
		public static final int transitionEasing = 0x7f0304f4;
		public static final int transitionFlags = 0x7f0304f5;
		public static final int transitionPathRotate = 0x7f0304f6;
		public static final int triggerId = 0x7f0304f8;
		public static final int triggerReceiver = 0x7f0304f9;
		public static final int triggerSlack = 0x7f0304fa;
		public static final int upDuration = 0x7f0304fc;
		public static final int viewTransitionMode = 0x7f030505;
		public static final int viewTransitionOnCross = 0x7f030506;
		public static final int viewTransitionOnNegativeCross = 0x7f030507;
		public static final int viewTransitionOnPositiveCross = 0x7f030508;
		public static final int visibilityMode = 0x7f030509;
		public static final int warmth = 0x7f03050b;
		public static final int waveDecay = 0x7f03050c;
		public static final int waveOffset = 0x7f03050d;
		public static final int wavePeriod = 0x7f03050e;
		public static final int wavePhase = 0x7f03050f;
		public static final int waveShape = 0x7f030510;
		public static final int waveVariesBy = 0x7f030511;
	}
	public static final class id {
		public static final int NO_DEBUG = 0x7f080006;
		public static final int SHOW_ALL = 0x7f080008;
		public static final int SHOW_PATH = 0x7f080009;
		public static final int SHOW_PROGRESS = 0x7f08000a;
		public static final int above = 0x7f08000e;
		public static final int accelerate = 0x7f08000f;
		public static final int actionDown = 0x7f080031;
		public static final int actionDownUp = 0x7f080032;
		public static final int actionUp = 0x7f080033;
		public static final int aligned = 0x7f08004a;
		public static final int allStates = 0x7f08004c;
		public static final int animateToEnd = 0x7f080051;
		public static final int animateToStart = 0x7f080052;
		public static final int antiClockwise = 0x7f080053;
		public static final int anticipate = 0x7f080054;
		public static final int asConfigured = 0x7f080056;
		public static final int auto = 0x7f080058;
		public static final int autoComplete = 0x7f080059;
		public static final int autoCompleteToEnd = 0x7f08005a;
		public static final int autoCompleteToStart = 0x7f08005b;
		public static final int axisRelative = 0x7f08005c;
		public static final int baseline = 0x7f08005e;
		public static final int below = 0x7f080061;
		public static final int bestChoice = 0x7f080062;
		public static final int bottom = 0x7f080064;
		public static final int bounce = 0x7f080066;
		public static final int callMeasure = 0x7f080071;
		public static final int carryVelocity = 0x7f080073;
		public static final int center = 0x7f080074;
		public static final int chain = 0x7f080079;
		public static final int chain2 = 0x7f08007a;
		public static final int clockwise = 0x7f080083;
		public static final int closest = 0x7f080084;
		public static final int constraint = 0x7f080088;
		public static final int continuousVelocity = 0x7f08008d;
		public static final int cos = 0x7f08008f;
		public static final int currentState = 0x7f080092;
		public static final int decelerate = 0x7f080097;
		public static final int decelerateAndComplete = 0x7f080098;
		public static final int deltaRelative = 0x7f08009b;
		public static final int dragAnticlockwise = 0x7f0800aa;
		public static final int dragClockwise = 0x7f0800ab;
		public static final int dragDown = 0x7f0800ac;
		public static final int dragEnd = 0x7f0800ad;
		public static final int dragLeft = 0x7f0800ae;
		public static final int dragRight = 0x7f0800af;
		public static final int dragStart = 0x7f0800b0;
		public static final int dragUp = 0x7f0800b1;
		public static final int easeIn = 0x7f0800b3;
		public static final int easeInOut = 0x7f0800b4;
		public static final int easeOut = 0x7f0800b5;
		public static final int east = 0x7f0800b6;
		public static final int end = 0x7f0800bc;
		public static final int flip = 0x7f0800cf;
		public static final int frost = 0x7f0800d4;
		public static final int gone = 0x7f0800d9;
		public static final int honorRequest = 0x7f0800e4;
		public static final int horizontal = 0x7f0800e5;
		public static final int horizontal_only = 0x7f0800e6;
		public static final int ignore = 0x7f0800ea;
		public static final int ignoreRequest = 0x7f0800eb;
		public static final int immediateStop = 0x7f0800ed;
		public static final int included = 0x7f0800ee;
		public static final int invisible = 0x7f0800f1;
		public static final int jumpToEnd = 0x7f0800f6;
		public static final int jumpToStart = 0x7f0800f7;
		public static final int layout = 0x7f0800f9;
		public static final int left = 0x7f0800fa;
		public static final int linear = 0x7f0800ff;
		public static final int match_constraint = 0x7f080107;
		public static final int match_parent = 0x7f080108;
		public static final int middle = 0x7f080120;
		public static final int motion_base = 0x7f080128;
		public static final int neverCompleteToEnd = 0x7f080151;
		public static final int neverCompleteToStart = 0x7f080152;
		public static final int noState = 0x7f080154;
		public static final int none = 0x7f080155;
		public static final int normal = 0x7f080156;
		public static final int north = 0x7f080157;
		public static final int overshoot = 0x7f08016e;
		public static final int packed = 0x7f08016f;
		public static final int parent = 0x7f080171;
		public static final int parentRelative = 0x7f080173;
		public static final int path = 0x7f080176;
		public static final int pathRelative = 0x7f080177;
		public static final int percent = 0x7f080179;
		public static final int position = 0x7f08017c;
		public static final int postLayout = 0x7f08017d;
		public static final int rectangles = 0x7f080183;
		public static final int reverseSawtooth = 0x7f080185;
		public static final int right = 0x7f080186;
		public static final int sawtooth = 0x7f08018f;
		public static final int sharedValueSet = 0x7f0801a4;
		public static final int sharedValueUnset = 0x7f0801a5;
		public static final int sin = 0x7f0801ab;
		public static final int skipped = 0x7f0801ad;
		public static final int south = 0x7f0801b5;
		public static final int spline = 0x7f0801b8;
		public static final int spread = 0x7f0801ba;
		public static final int spread_inside = 0x7f0801bb;
		public static final int spring = 0x7f0801bc;
		public static final int square = 0x7f0801bd;
		public static final int standard = 0x7f0801c1;
		public static final int start = 0x7f0801c2;
		public static final int startHorizontal = 0x7f0801c3;
		public static final int startVertical = 0x7f0801c5;
		public static final int staticLayout = 0x7f0801c6;
		public static final int staticPostLayout = 0x7f0801c7;
		public static final int stop = 0x7f0801c8;
		public static final int top = 0x7f0801f0;
		public static final int triangle = 0x7f0801fe;
		public static final int vertical = 0x7f080204;
		public static final int vertical_only = 0x7f080205;
		public static final int view_transition = 0x7f080207;
		public static final int visible = 0x7f08020c;
		public static final int west = 0x7f08020e;
		public static final int wrap = 0x7f080212;
		public static final int wrap_content = 0x7f080213;
		public static final int wrap_content_constrained = 0x7f080214;
		public static final int x_left = 0x7f080215;
		public static final int x_right = 0x7f080216;
	}
	public static final class styleable {
		public static final int[] Carousel = new int[] { 0x7f0300a9, 0x7f0300aa, 0x7f0300ab, 0x7f0300ac, 0x7f0300ad, 0x7f0300ae, 0x7f0300af, 0x7f0300b0, 0x7f0300b1, 0x7f0300b2, 0x7f0300b3 };
		public static final int Carousel_carousel_backwardTransition = 1;
		public static final int Carousel_carousel_emptyViewsBehavior = 2;
		public static final int Carousel_carousel_firstView = 3;
		public static final int Carousel_carousel_forwardTransition = 4;
		public static final int Carousel_carousel_infinite = 5;
		public static final int Carousel_carousel_nextState = 6;
		public static final int Carousel_carousel_previousState = 7;
		public static final int Carousel_carousel_touchUpMode = 8;
		public static final int Carousel_carousel_touchUp_dampeningFactor = 9;
		public static final int Carousel_carousel_touchUp_velocityThreshold = 10;
		public static final int[] Constraint = new int[] { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f030035, 0x7f030038, 0x7f03006a, 0x7f03006b, 0x7f03006c, 0x7f0300b5, 0x7f03013e, 0x7f03013f, 0x7f030194, 0x7f0301fa, 0x7f0301fb, 0x7f0301fc, 0x7f0301fd, 0x7f0301fe, 0x7f0301ff, 0x7f030200, 0x7f030201, 0x7f030202, 0x7f030203, 0x7f030204, 0x7f030205, 0x7f030206, 0x7f030208, 0x7f030209, 0x7f03020a, 0x7f03020b, 0x7f03020c, 0x7f03022d, 0x7f030294, 0x7f030295, 0x7f030296, 0x7f030297, 0x7f030298, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029c, 0x7f03029d, 0x7f03029e, 0x7f03029f, 0x7f0302a0, 0x7f0302a1, 0x7f0302a2, 0x7f0302a3, 0x7f0302a4, 0x7f0302a5, 0x7f0302a6, 0x7f0302a7, 0x7f0302a8, 0x7f0302a9, 0x7f0302aa, 0x7f0302ab, 0x7f0302ac, 0x7f0302ad, 0x7f0302ae, 0x7f0302af, 0x7f0302b0, 0x7f0302b1, 0x7f0302b2, 0x7f0302b3, 0x7f0302b4, 0x7f0302b5, 0x7f0302b6, 0x7f0302b7, 0x7f0302b8, 0x7f0302b9, 0x7f0302ba, 0x7f0302bb, 0x7f0302bc, 0x7f0302bd, 0x7f0302be, 0x7f0302bf, 0x7f0302c0, 0x7f0302c1, 0x7f0302c3, 0x7f0302c4, 0x7f0302c5, 0x7f0302c6, 0x7f0302c7, 0x7f0302c8, 0x7f0302c9, 0x7f0302ca, 0x7f0302cb, 0x7f0302ce, 0x7f0302d3, 0x7f030366, 0x7f030367, 0x7f030395, 0x7f03039c, 0x7f0303a2, 0x7f0303b4, 0x7f0303b5, 0x7f0303b6, 0x7f0304f2, 0x7f0304f4, 0x7f0304f6, 0x7f030509 };
		public static final int Constraint_android_alpha = 13;
		public static final int Constraint_android_elevation = 26;
		public static final int Constraint_android_id = 1;
		public static final int Constraint_android_layout_height = 4;
		public static final int Constraint_android_layout_marginBottom = 8;
		public static final int Constraint_android_layout_marginEnd = 24;
		public static final int Constraint_android_layout_marginLeft = 5;
		public static final int Constraint_android_layout_marginRight = 7;
		public static final int Constraint_android_layout_marginStart = 23;
		public static final int Constraint_android_layout_marginTop = 6;
		public static final int Constraint_android_layout_width = 3;
		public static final int Constraint_android_maxHeight = 10;
		public static final int Constraint_android_maxWidth = 9;
		public static final int Constraint_android_minHeight = 12;
		public static final int Constraint_android_minWidth = 11;
		public static final int Constraint_android_orientation = 0;
		public static final int Constraint_android_rotation = 20;
		public static final int Constraint_android_rotationX = 21;
		public static final int Constraint_android_rotationY = 22;
		public static final int Constraint_android_scaleX = 18;
		public static final int Constraint_android_scaleY = 19;
		public static final int Constraint_android_transformPivotX = 14;
		public static final int Constraint_android_transformPivotY = 15;
		public static final int Constraint_android_translationX = 16;
		public static final int Constraint_android_translationY = 17;
		public static final int Constraint_android_translationZ = 25;
		public static final int Constraint_android_visibility = 2;
		public static final int Constraint_animateCircleAngleTo = 27;
		public static final int Constraint_animateRelativeTo = 28;
		public static final int Constraint_barrierAllowsGoneWidgets = 29;
		public static final int Constraint_barrierDirection = 30;
		public static final int Constraint_barrierMargin = 31;
		public static final int Constraint_chainUseRtl = 32;
		public static final int Constraint_constraint_referenced_ids = 33;
		public static final int Constraint_constraint_referenced_tags = 34;
		public static final int Constraint_drawPath = 35;
		public static final int Constraint_flow_firstHorizontalBias = 36;
		public static final int Constraint_flow_firstHorizontalStyle = 37;
		public static final int Constraint_flow_firstVerticalBias = 38;
		public static final int Constraint_flow_firstVerticalStyle = 39;
		public static final int Constraint_flow_horizontalAlign = 40;
		public static final int Constraint_flow_horizontalBias = 41;
		public static final int Constraint_flow_horizontalGap = 42;
		public static final int Constraint_flow_horizontalStyle = 43;
		public static final int Constraint_flow_lastHorizontalBias = 44;
		public static final int Constraint_flow_lastHorizontalStyle = 45;
		public static final int Constraint_flow_lastVerticalBias = 46;
		public static final int Constraint_flow_lastVerticalStyle = 47;
		public static final int Constraint_flow_maxElementsWrap = 48;
		public static final int Constraint_flow_verticalAlign = 49;
		public static final int Constraint_flow_verticalBias = 50;
		public static final int Constraint_flow_verticalGap = 51;
		public static final int Constraint_flow_verticalStyle = 52;
		public static final int Constraint_flow_wrapMode = 53;
		public static final int Constraint_guidelineUseRtl = 54;
		public static final int Constraint_layout_constrainedHeight = 55;
		public static final int Constraint_layout_constrainedWidth = 56;
		public static final int Constraint_layout_constraintBaseline_creator = 57;
		public static final int Constraint_layout_constraintBaseline_toBaselineOf = 58;
		public static final int Constraint_layout_constraintBaseline_toBottomOf = 59;
		public static final int Constraint_layout_constraintBaseline_toTopOf = 60;
		public static final int Constraint_layout_constraintBottom_creator = 61;
		public static final int Constraint_layout_constraintBottom_toBottomOf = 62;
		public static final int Constraint_layout_constraintBottom_toTopOf = 63;
		public static final int Constraint_layout_constraintCircle = 64;
		public static final int Constraint_layout_constraintCircleAngle = 65;
		public static final int Constraint_layout_constraintCircleRadius = 66;
		public static final int Constraint_layout_constraintDimensionRatio = 67;
		public static final int Constraint_layout_constraintEnd_toEndOf = 68;
		public static final int Constraint_layout_constraintEnd_toStartOf = 69;
		public static final int Constraint_layout_constraintGuide_begin = 70;
		public static final int Constraint_layout_constraintGuide_end = 71;
		public static final int Constraint_layout_constraintGuide_percent = 72;
		public static final int Constraint_layout_constraintHeight = 73;
		public static final int Constraint_layout_constraintHeight_default = 74;
		public static final int Constraint_layout_constraintHeight_max = 75;
		public static final int Constraint_layout_constraintHeight_min = 76;
		public static final int Constraint_layout_constraintHeight_percent = 77;
		public static final int Constraint_layout_constraintHorizontal_bias = 78;
		public static final int Constraint_layout_constraintHorizontal_chainStyle = 79;
		public static final int Constraint_layout_constraintHorizontal_weight = 80;
		public static final int Constraint_layout_constraintLeft_creator = 81;
		public static final int Constraint_layout_constraintLeft_toLeftOf = 82;
		public static final int Constraint_layout_constraintLeft_toRightOf = 83;
		public static final int Constraint_layout_constraintRight_creator = 84;
		public static final int Constraint_layout_constraintRight_toLeftOf = 85;
		public static final int Constraint_layout_constraintRight_toRightOf = 86;
		public static final int Constraint_layout_constraintStart_toEndOf = 87;
		public static final int Constraint_layout_constraintStart_toStartOf = 88;
		public static final int Constraint_layout_constraintTag = 89;
		public static final int Constraint_layout_constraintTop_creator = 90;
		public static final int Constraint_layout_constraintTop_toBottomOf = 91;
		public static final int Constraint_layout_constraintTop_toTopOf = 92;
		public static final int Constraint_layout_constraintVertical_bias = 93;
		public static final int Constraint_layout_constraintVertical_chainStyle = 94;
		public static final int Constraint_layout_constraintVertical_weight = 95;
		public static final int Constraint_layout_constraintWidth = 96;
		public static final int Constraint_layout_constraintWidth_default = 97;
		public static final int Constraint_layout_constraintWidth_max = 98;
		public static final int Constraint_layout_constraintWidth_min = 99;
		public static final int Constraint_layout_constraintWidth_percent = 100;
		public static final int Constraint_layout_editor_absoluteX = 101;
		public static final int Constraint_layout_editor_absoluteY = 102;
		public static final int Constraint_layout_goneMarginBaseline = 103;
		public static final int Constraint_layout_goneMarginBottom = 104;
		public static final int Constraint_layout_goneMarginEnd = 105;
		public static final int Constraint_layout_goneMarginLeft = 106;
		public static final int Constraint_layout_goneMarginRight = 107;
		public static final int Constraint_layout_goneMarginStart = 108;
		public static final int Constraint_layout_goneMarginTop = 109;
		public static final int Constraint_layout_marginBaseline = 110;
		public static final int Constraint_layout_wrapBehaviorInParent = 111;
		public static final int Constraint_motionProgress = 112;
		public static final int Constraint_motionStagger = 113;
		public static final int Constraint_pathMotionArc = 114;
		public static final int Constraint_pivotAnchor = 115;
		public static final int Constraint_polarRelativeTo = 116;
		public static final int Constraint_quantizeMotionInterpolator = 117;
		public static final int Constraint_quantizeMotionPhase = 118;
		public static final int Constraint_quantizeMotionSteps = 119;
		public static final int Constraint_transformPivotTarget = 120;
		public static final int Constraint_transitionEasing = 121;
		public static final int Constraint_transitionPathRotate = 122;
		public static final int Constraint_visibilityMode = 123;
		public static final int[] ConstraintLayout_Layout = new int[] { 0x010100c4, 0x010100d5, 0x010100d6, 0x010100d7, 0x010100d8, 0x010100d9, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f6, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010103b3, 0x010103b4, 0x010103b5, 0x010103b6, 0x01010440, 0x0101053b, 0x0101053c, 0x7f03006a, 0x7f03006b, 0x7f03006c, 0x7f0300b5, 0x7f0300db, 0x7f0300dc, 0x7f0300dd, 0x7f0300de, 0x7f0300df, 0x7f03013b, 0x7f03013e, 0x7f03013f, 0x7f0301fa, 0x7f0301fb, 0x7f0301fc, 0x7f0301fd, 0x7f0301fe, 0x7f0301ff, 0x7f030200, 0x7f030201, 0x7f030202, 0x7f030203, 0x7f030204, 0x7f030205, 0x7f030206, 0x7f030208, 0x7f030209, 0x7f03020a, 0x7f03020b, 0x7f03020c, 0x7f03022d, 0x7f03028c, 0x7f030294, 0x7f030295, 0x7f030296, 0x7f030297, 0x7f030298, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029c, 0x7f03029d, 0x7f03029e, 0x7f03029f, 0x7f0302a0, 0x7f0302a1, 0x7f0302a2, 0x7f0302a3, 0x7f0302a4, 0x7f0302a5, 0x7f0302a6, 0x7f0302a7, 0x7f0302a8, 0x7f0302a9, 0x7f0302aa, 0x7f0302ab, 0x7f0302ac, 0x7f0302ad, 0x7f0302ae, 0x7f0302af, 0x7f0302b0, 0x7f0302b1, 0x7f0302b2, 0x7f0302b3, 0x7f0302b4, 0x7f0302b5, 0x7f0302b6, 0x7f0302b7, 0x7f0302b8, 0x7f0302b9, 0x7f0302ba, 0x7f0302bb, 0x7f0302bc, 0x7f0302bd, 0x7f0302be, 0x7f0302bf, 0x7f0302c0, 0x7f0302c1, 0x7f0302c3, 0x7f0302c4, 0x7f0302c5, 0x7f0302c6, 0x7f0302c7, 0x7f0302c8, 0x7f0302c9, 0x7f0302ca, 0x7f0302cb, 0x7f0302ce, 0x7f0302cf, 0x7f0302d3 };
		public static final int ConstraintLayout_Layout_android_elevation = 22;
		public static final int ConstraintLayout_Layout_android_layout_height = 8;
		public static final int ConstraintLayout_Layout_android_layout_margin = 9;
		public static final int ConstraintLayout_Layout_android_layout_marginBottom = 13;
		public static final int ConstraintLayout_Layout_android_layout_marginEnd = 21;
		public static final int ConstraintLayout_Layout_android_layout_marginHorizontal = 23;
		public static final int ConstraintLayout_Layout_android_layout_marginLeft = 10;
		public static final int ConstraintLayout_Layout_android_layout_marginRight = 12;
		public static final int ConstraintLayout_Layout_android_layout_marginStart = 20;
		public static final int ConstraintLayout_Layout_android_layout_marginTop = 11;
		public static final int ConstraintLayout_Layout_android_layout_marginVertical = 24;
		public static final int ConstraintLayout_Layout_android_layout_width = 7;
		public static final int ConstraintLayout_Layout_android_maxHeight = 15;
		public static final int ConstraintLayout_Layout_android_maxWidth = 14;
		public static final int ConstraintLayout_Layout_android_minHeight = 17;
		public static final int ConstraintLayout_Layout_android_minWidth = 16;
		public static final int ConstraintLayout_Layout_android_orientation = 0;
		public static final int ConstraintLayout_Layout_android_padding = 1;
		public static final int ConstraintLayout_Layout_android_paddingBottom = 5;
		public static final int ConstraintLayout_Layout_android_paddingEnd = 19;
		public static final int ConstraintLayout_Layout_android_paddingLeft = 2;
		public static final int ConstraintLayout_Layout_android_paddingRight = 4;
		public static final int ConstraintLayout_Layout_android_paddingStart = 18;
		public static final int ConstraintLayout_Layout_android_paddingTop = 3;
		public static final int ConstraintLayout_Layout_android_visibility = 6;
		public static final int ConstraintLayout_Layout_barrierAllowsGoneWidgets = 25;
		public static final int ConstraintLayout_Layout_barrierDirection = 26;
		public static final int ConstraintLayout_Layout_barrierMargin = 27;
		public static final int ConstraintLayout_Layout_chainUseRtl = 28;
		public static final int ConstraintLayout_Layout_circularflow_angles = 29;
		public static final int ConstraintLayout_Layout_circularflow_defaultAngle = 30;
		public static final int ConstraintLayout_Layout_circularflow_defaultRadius = 31;
		public static final int ConstraintLayout_Layout_circularflow_radiusInDP = 32;
		public static final int ConstraintLayout_Layout_circularflow_viewCenter = 33;
		public static final int ConstraintLayout_Layout_constraintSet = 34;
		public static final int ConstraintLayout_Layout_constraint_referenced_ids = 35;
		public static final int ConstraintLayout_Layout_constraint_referenced_tags = 36;
		public static final int ConstraintLayout_Layout_flow_firstHorizontalBias = 37;
		public static final int ConstraintLayout_Layout_flow_firstHorizontalStyle = 38;
		public static final int ConstraintLayout_Layout_flow_firstVerticalBias = 39;
		public static final int ConstraintLayout_Layout_flow_firstVerticalStyle = 40;
		public static final int ConstraintLayout_Layout_flow_horizontalAlign = 41;
		public static final int ConstraintLayout_Layout_flow_horizontalBias = 42;
		public static final int ConstraintLayout_Layout_flow_horizontalGap = 43;
		public static final int ConstraintLayout_Layout_flow_horizontalStyle = 44;
		public static final int ConstraintLayout_Layout_flow_lastHorizontalBias = 45;
		public static final int ConstraintLayout_Layout_flow_lastHorizontalStyle = 46;
		public static final int ConstraintLayout_Layout_flow_lastVerticalBias = 47;
		public static final int ConstraintLayout_Layout_flow_lastVerticalStyle = 48;
		public static final int ConstraintLayout_Layout_flow_maxElementsWrap = 49;
		public static final int ConstraintLayout_Layout_flow_verticalAlign = 50;
		public static final int ConstraintLayout_Layout_flow_verticalBias = 51;
		public static final int ConstraintLayout_Layout_flow_verticalGap = 52;
		public static final int ConstraintLayout_Layout_flow_verticalStyle = 53;
		public static final int ConstraintLayout_Layout_flow_wrapMode = 54;
		public static final int ConstraintLayout_Layout_guidelineUseRtl = 55;
		public static final int ConstraintLayout_Layout_layoutDescription = 56;
		public static final int ConstraintLayout_Layout_layout_constrainedHeight = 57;
		public static final int ConstraintLayout_Layout_layout_constrainedWidth = 58;
		public static final int ConstraintLayout_Layout_layout_constraintBaseline_creator = 59;
		public static final int ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf = 60;
		public static final int ConstraintLayout_Layout_layout_constraintBaseline_toBottomOf = 61;
		public static final int ConstraintLayout_Layout_layout_constraintBaseline_toTopOf = 62;
		public static final int ConstraintLayout_Layout_layout_constraintBottom_creator = 63;
		public static final int ConstraintLayout_Layout_layout_constraintBottom_toBottomOf = 64;
		public static final int ConstraintLayout_Layout_layout_constraintBottom_toTopOf = 65;
		public static final int ConstraintLayout_Layout_layout_constraintCircle = 66;
		public static final int ConstraintLayout_Layout_layout_constraintCircleAngle = 67;
		public static final int ConstraintLayout_Layout_layout_constraintCircleRadius = 68;
		public static final int ConstraintLayout_Layout_layout_constraintDimensionRatio = 69;
		public static final int ConstraintLayout_Layout_layout_constraintEnd_toEndOf = 70;
		public static final int ConstraintLayout_Layout_layout_constraintEnd_toStartOf = 71;
		public static final int ConstraintLayout_Layout_layout_constraintGuide_begin = 72;
		public static final int ConstraintLayout_Layout_layout_constraintGuide_end = 73;
		public static final int ConstraintLayout_Layout_layout_constraintGuide_percent = 74;
		public static final int ConstraintLayout_Layout_layout_constraintHeight = 75;
		public static final int ConstraintLayout_Layout_layout_constraintHeight_default = 76;
		public static final int ConstraintLayout_Layout_layout_constraintHeight_max = 77;
		public static final int ConstraintLayout_Layout_layout_constraintHeight_min = 78;
		public static final int ConstraintLayout_Layout_layout_constraintHeight_percent = 79;
		public static final int ConstraintLayout_Layout_layout_constraintHorizontal_bias = 80;
		public static final int ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle = 81;
		public static final int ConstraintLayout_Layout_layout_constraintHorizontal_weight = 82;
		public static final int ConstraintLayout_Layout_layout_constraintLeft_creator = 83;
		public static final int ConstraintLayout_Layout_layout_constraintLeft_toLeftOf = 84;
		public static final int ConstraintLayout_Layout_layout_constraintLeft_toRightOf = 85;
		public static final int ConstraintLayout_Layout_layout_constraintRight_creator = 86;
		public static final int ConstraintLayout_Layout_layout_constraintRight_toLeftOf = 87;
		public static final int ConstraintLayout_Layout_layout_constraintRight_toRightOf = 88;
		public static final int ConstraintLayout_Layout_layout_constraintStart_toEndOf = 89;
		public static final int ConstraintLayout_Layout_layout_constraintStart_toStartOf = 90;
		public static final int ConstraintLayout_Layout_layout_constraintTag = 91;
		public static final int ConstraintLayout_Layout_layout_constraintTop_creator = 92;
		public static final int ConstraintLayout_Layout_layout_constraintTop_toBottomOf = 93;
		public static final int ConstraintLayout_Layout_layout_constraintTop_toTopOf = 94;
		public static final int ConstraintLayout_Layout_layout_constraintVertical_bias = 95;
		public static final int ConstraintLayout_Layout_layout_constraintVertical_chainStyle = 96;
		public static final int ConstraintLayout_Layout_layout_constraintVertical_weight = 97;
		public static final int ConstraintLayout_Layout_layout_constraintWidth = 98;
		public static final int ConstraintLayout_Layout_layout_constraintWidth_default = 99;
		public static final int ConstraintLayout_Layout_layout_constraintWidth_max = 100;
		public static final int ConstraintLayout_Layout_layout_constraintWidth_min = 101;
		public static final int ConstraintLayout_Layout_layout_constraintWidth_percent = 102;
		public static final int ConstraintLayout_Layout_layout_editor_absoluteX = 103;
		public static final int ConstraintLayout_Layout_layout_editor_absoluteY = 104;
		public static final int ConstraintLayout_Layout_layout_goneMarginBaseline = 105;
		public static final int ConstraintLayout_Layout_layout_goneMarginBottom = 106;
		public static final int ConstraintLayout_Layout_layout_goneMarginEnd = 107;
		public static final int ConstraintLayout_Layout_layout_goneMarginLeft = 108;
		public static final int ConstraintLayout_Layout_layout_goneMarginRight = 109;
		public static final int ConstraintLayout_Layout_layout_goneMarginStart = 110;
		public static final int ConstraintLayout_Layout_layout_goneMarginTop = 111;
		public static final int ConstraintLayout_Layout_layout_marginBaseline = 112;
		public static final int ConstraintLayout_Layout_layout_optimizationLevel = 113;
		public static final int ConstraintLayout_Layout_layout_wrapBehaviorInParent = 114;
		public static final int[] ConstraintLayout_ReactiveGuide = new int[] { 0x7f0303bf, 0x7f0303c0, 0x7f0303c1, 0x7f0303c2 };
		public static final int ConstraintLayout_ReactiveGuide_reactiveGuide_animateChange = 0;
		public static final int ConstraintLayout_ReactiveGuide_reactiveGuide_applyToAllConstraintSets = 1;
		public static final int ConstraintLayout_ReactiveGuide_reactiveGuide_applyToConstraintSet = 2;
		public static final int ConstraintLayout_ReactiveGuide_reactiveGuide_valueId = 3;
		public static final int[] ConstraintLayout_placeholder = new int[] { 0x7f030141, 0x7f0303a1 };
		public static final int ConstraintLayout_placeholder_content = 0;
		public static final int ConstraintLayout_placeholder_placeholder_emptyVisibility = 1;
		public static final int[] ConstraintOverride = new int[] { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f030035, 0x7f030038, 0x7f03006a, 0x7f03006b, 0x7f03006c, 0x7f0300b5, 0x7f03013e, 0x7f030194, 0x7f0301fa, 0x7f0301fb, 0x7f0301fc, 0x7f0301fd, 0x7f0301fe, 0x7f0301ff, 0x7f030200, 0x7f030201, 0x7f030202, 0x7f030203, 0x7f030204, 0x7f030205, 0x7f030206, 0x7f030208, 0x7f030209, 0x7f03020a, 0x7f03020b, 0x7f03020c, 0x7f03022d, 0x7f030294, 0x7f030295, 0x7f030296, 0x7f03029a, 0x7f03029e, 0x7f03029f, 0x7f0302a0, 0x7f0302a3, 0x7f0302a4, 0x7f0302a5, 0x7f0302a6, 0x7f0302a7, 0x7f0302a8, 0x7f0302a9, 0x7f0302aa, 0x7f0302ab, 0x7f0302ac, 0x7f0302ad, 0x7f0302ae, 0x7f0302b1, 0x7f0302b6, 0x7f0302b7, 0x7f0302ba, 0x7f0302bb, 0x7f0302bc, 0x7f0302bd, 0x7f0302be, 0x7f0302bf, 0x7f0302c0, 0x7f0302c1, 0x7f0302c3, 0x7f0302c4, 0x7f0302c5, 0x7f0302c6, 0x7f0302c7, 0x7f0302c8, 0x7f0302c9, 0x7f0302ca, 0x7f0302cb, 0x7f0302ce, 0x7f0302d3, 0x7f030366, 0x7f030367, 0x7f030368, 0x7f030395, 0x7f03039c, 0x7f0303a2, 0x7f0303b4, 0x7f0303b5, 0x7f0303b6, 0x7f0304f2, 0x7f0304f4, 0x7f0304f6, 0x7f030509 };
		public static final int ConstraintOverride_android_alpha = 13;
		public static final int ConstraintOverride_android_elevation = 26;
		public static final int ConstraintOverride_android_id = 1;
		public static final int ConstraintOverride_android_layout_height = 4;
		public static final int ConstraintOverride_android_layout_marginBottom = 8;
		public static final int ConstraintOverride_android_layout_marginEnd = 24;
		public static final int ConstraintOverride_android_layout_marginLeft = 5;
		public static final int ConstraintOverride_android_layout_marginRight = 7;
		public static final int ConstraintOverride_android_layout_marginStart = 23;
		public static final int ConstraintOverride_android_layout_marginTop = 6;
		public static final int ConstraintOverride_android_layout_width = 3;
		public static final int ConstraintOverride_android_maxHeight = 10;
		public static final int ConstraintOverride_android_maxWidth = 9;
		public static final int ConstraintOverride_android_minHeight = 12;
		public static final int ConstraintOverride_android_minWidth = 11;
		public static final int ConstraintOverride_android_orientation = 0;
		public static final int ConstraintOverride_android_rotation = 20;
		public static final int ConstraintOverride_android_rotationX = 21;
		public static final int ConstraintOverride_android_rotationY = 22;
		public static final int ConstraintOverride_android_scaleX = 18;
		public static final int ConstraintOverride_android_scaleY = 19;
		public static final int ConstraintOverride_android_transformPivotX = 14;
		public static final int ConstraintOverride_android_transformPivotY = 15;
		public static final int ConstraintOverride_android_translationX = 16;
		public static final int ConstraintOverride_android_translationY = 17;
		public static final int ConstraintOverride_android_translationZ = 25;
		public static final int ConstraintOverride_android_visibility = 2;
		public static final int ConstraintOverride_animateCircleAngleTo = 27;
		public static final int ConstraintOverride_animateRelativeTo = 28;
		public static final int ConstraintOverride_barrierAllowsGoneWidgets = 29;
		public static final int ConstraintOverride_barrierDirection = 30;
		public static final int ConstraintOverride_barrierMargin = 31;
		public static final int ConstraintOverride_chainUseRtl = 32;
		public static final int ConstraintOverride_constraint_referenced_ids = 33;
		public static final int ConstraintOverride_drawPath = 34;
		public static final int ConstraintOverride_flow_firstHorizontalBias = 35;
		public static final int ConstraintOverride_flow_firstHorizontalStyle = 36;
		public static final int ConstraintOverride_flow_firstVerticalBias = 37;
		public static final int ConstraintOverride_flow_firstVerticalStyle = 38;
		public static final int ConstraintOverride_flow_horizontalAlign = 39;
		public static final int ConstraintOverride_flow_horizontalBias = 40;
		public static final int ConstraintOverride_flow_horizontalGap = 41;
		public static final int ConstraintOverride_flow_horizontalStyle = 42;
		public static final int ConstraintOverride_flow_lastHorizontalBias = 43;
		public static final int ConstraintOverride_flow_lastHorizontalStyle = 44;
		public static final int ConstraintOverride_flow_lastVerticalBias = 45;
		public static final int ConstraintOverride_flow_lastVerticalStyle = 46;
		public static final int ConstraintOverride_flow_maxElementsWrap = 47;
		public static final int ConstraintOverride_flow_verticalAlign = 48;
		public static final int ConstraintOverride_flow_verticalBias = 49;
		public static final int ConstraintOverride_flow_verticalGap = 50;
		public static final int ConstraintOverride_flow_verticalStyle = 51;
		public static final int ConstraintOverride_flow_wrapMode = 52;
		public static final int ConstraintOverride_guidelineUseRtl = 53;
		public static final int ConstraintOverride_layout_constrainedHeight = 54;
		public static final int ConstraintOverride_layout_constrainedWidth = 55;
		public static final int ConstraintOverride_layout_constraintBaseline_creator = 56;
		public static final int ConstraintOverride_layout_constraintBottom_creator = 57;
		public static final int ConstraintOverride_layout_constraintCircleAngle = 58;
		public static final int ConstraintOverride_layout_constraintCircleRadius = 59;
		public static final int ConstraintOverride_layout_constraintDimensionRatio = 60;
		public static final int ConstraintOverride_layout_constraintGuide_begin = 61;
		public static final int ConstraintOverride_layout_constraintGuide_end = 62;
		public static final int ConstraintOverride_layout_constraintGuide_percent = 63;
		public static final int ConstraintOverride_layout_constraintHeight = 64;
		public static final int ConstraintOverride_layout_constraintHeight_default = 65;
		public static final int ConstraintOverride_layout_constraintHeight_max = 66;
		public static final int ConstraintOverride_layout_constraintHeight_min = 67;
		public static final int ConstraintOverride_layout_constraintHeight_percent = 68;
		public static final int ConstraintOverride_layout_constraintHorizontal_bias = 69;
		public static final int ConstraintOverride_layout_constraintHorizontal_chainStyle = 70;
		public static final int ConstraintOverride_layout_constraintHorizontal_weight = 71;
		public static final int ConstraintOverride_layout_constraintLeft_creator = 72;
		public static final int ConstraintOverride_layout_constraintRight_creator = 73;
		public static final int ConstraintOverride_layout_constraintTag = 74;
		public static final int ConstraintOverride_layout_constraintTop_creator = 75;
		public static final int ConstraintOverride_layout_constraintVertical_bias = 76;
		public static final int ConstraintOverride_layout_constraintVertical_chainStyle = 77;
		public static final int ConstraintOverride_layout_constraintVertical_weight = 78;
		public static final int ConstraintOverride_layout_constraintWidth = 79;
		public static final int ConstraintOverride_layout_constraintWidth_default = 80;
		public static final int ConstraintOverride_layout_constraintWidth_max = 81;
		public static final int ConstraintOverride_layout_constraintWidth_min = 82;
		public static final int ConstraintOverride_layout_constraintWidth_percent = 83;
		public static final int ConstraintOverride_layout_editor_absoluteX = 84;
		public static final int ConstraintOverride_layout_editor_absoluteY = 85;
		public static final int ConstraintOverride_layout_goneMarginBaseline = 86;
		public static final int ConstraintOverride_layout_goneMarginBottom = 87;
		public static final int ConstraintOverride_layout_goneMarginEnd = 88;
		public static final int ConstraintOverride_layout_goneMarginLeft = 89;
		public static final int ConstraintOverride_layout_goneMarginRight = 90;
		public static final int ConstraintOverride_layout_goneMarginStart = 91;
		public static final int ConstraintOverride_layout_goneMarginTop = 92;
		public static final int ConstraintOverride_layout_marginBaseline = 93;
		public static final int ConstraintOverride_layout_wrapBehaviorInParent = 94;
		public static final int ConstraintOverride_motionProgress = 95;
		public static final int ConstraintOverride_motionStagger = 96;
		public static final int ConstraintOverride_motionTarget = 97;
		public static final int ConstraintOverride_pathMotionArc = 98;
		public static final int ConstraintOverride_pivotAnchor = 99;
		public static final int ConstraintOverride_polarRelativeTo = 100;
		public static final int ConstraintOverride_quantizeMotionInterpolator = 101;
		public static final int ConstraintOverride_quantizeMotionPhase = 102;
		public static final int ConstraintOverride_quantizeMotionSteps = 103;
		public static final int ConstraintOverride_transformPivotTarget = 104;
		public static final int ConstraintOverride_transitionEasing = 105;
		public static final int ConstraintOverride_transitionPathRotate = 106;
		public static final int ConstraintOverride_visibilityMode = 107;
		public static final int[] ConstraintSet = new int[] { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010101b5, 0x010101b6, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f030035, 0x7f030038, 0x7f03006a, 0x7f03006b, 0x7f03006c, 0x7f0300b5, 0x7f03013a, 0x7f03013e, 0x7f03013f, 0x7f030183, 0x7f030194, 0x7f0301fa, 0x7f0301fb, 0x7f0301fc, 0x7f0301fd, 0x7f0301fe, 0x7f0301ff, 0x7f030200, 0x7f030201, 0x7f030202, 0x7f030203, 0x7f030204, 0x7f030205, 0x7f030206, 0x7f030208, 0x7f030209, 0x7f03020a, 0x7f03020b, 0x7f03020c, 0x7f03022d, 0x7f030294, 0x7f030295, 0x7f030296, 0x7f030297, 0x7f030298, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029c, 0x7f03029d, 0x7f03029e, 0x7f03029f, 0x7f0302a0, 0x7f0302a1, 0x7f0302a2, 0x7f0302a3, 0x7f0302a4, 0x7f0302a5, 0x7f0302a7, 0x7f0302a8, 0x7f0302a9, 0x7f0302aa, 0x7f0302ab, 0x7f0302ac, 0x7f0302ad, 0x7f0302ae, 0x7f0302af, 0x7f0302b0, 0x7f0302b1, 0x7f0302b2, 0x7f0302b3, 0x7f0302b4, 0x7f0302b5, 0x7f0302b6, 0x7f0302b7, 0x7f0302b8, 0x7f0302b9, 0x7f0302ba, 0x7f0302bb, 0x7f0302bc, 0x7f0302be, 0x7f0302bf, 0x7f0302c0, 0x7f0302c1, 0x7f0302c3, 0x7f0302c4, 0x7f0302c5, 0x7f0302c6, 0x7f0302c7, 0x7f0302c8, 0x7f0302c9, 0x7f0302ca, 0x7f0302cb, 0x7f0302ce, 0x7f0302d3, 0x7f030366, 0x7f030367, 0x7f030395, 0x7f03039c, 0x7f0303a2, 0x7f0303b6, 0x7f030423, 0x7f0304f4, 0x7f0304f6 };
		public static final int ConstraintSet_android_alpha = 15;
		public static final int ConstraintSet_android_elevation = 28;
		public static final int ConstraintSet_android_id = 1;
		public static final int ConstraintSet_android_layout_height = 4;
		public static final int ConstraintSet_android_layout_marginBottom = 8;
		public static final int ConstraintSet_android_layout_marginEnd = 26;
		public static final int ConstraintSet_android_layout_marginLeft = 5;
		public static final int ConstraintSet_android_layout_marginRight = 7;
		public static final int ConstraintSet_android_layout_marginStart = 25;
		public static final int ConstraintSet_android_layout_marginTop = 6;
		public static final int ConstraintSet_android_layout_width = 3;
		public static final int ConstraintSet_android_maxHeight = 10;
		public static final int ConstraintSet_android_maxWidth = 9;
		public static final int ConstraintSet_android_minHeight = 12;
		public static final int ConstraintSet_android_minWidth = 11;
		public static final int ConstraintSet_android_orientation = 0;
		public static final int ConstraintSet_android_pivotX = 13;
		public static final int ConstraintSet_android_pivotY = 14;
		public static final int ConstraintSet_android_rotation = 22;
		public static final int ConstraintSet_android_rotationX = 23;
		public static final int ConstraintSet_android_rotationY = 24;
		public static final int ConstraintSet_android_scaleX = 20;
		public static final int ConstraintSet_android_scaleY = 21;
		public static final int ConstraintSet_android_transformPivotX = 16;
		public static final int ConstraintSet_android_transformPivotY = 17;
		public static final int ConstraintSet_android_translationX = 18;
		public static final int ConstraintSet_android_translationY = 19;
		public static final int ConstraintSet_android_translationZ = 27;
		public static final int ConstraintSet_android_visibility = 2;
		public static final int ConstraintSet_animateCircleAngleTo = 29;
		public static final int ConstraintSet_animateRelativeTo = 30;
		public static final int ConstraintSet_barrierAllowsGoneWidgets = 31;
		public static final int ConstraintSet_barrierDirection = 32;
		public static final int ConstraintSet_barrierMargin = 33;
		public static final int ConstraintSet_chainUseRtl = 34;
		public static final int ConstraintSet_constraintRotate = 35;
		public static final int ConstraintSet_constraint_referenced_ids = 36;
		public static final int ConstraintSet_constraint_referenced_tags = 37;
		public static final int ConstraintSet_deriveConstraintsFrom = 38;
		public static final int ConstraintSet_drawPath = 39;
		public static final int ConstraintSet_flow_firstHorizontalBias = 40;
		public static final int ConstraintSet_flow_firstHorizontalStyle = 41;
		public static final int ConstraintSet_flow_firstVerticalBias = 42;
		public static final int ConstraintSet_flow_firstVerticalStyle = 43;
		public static final int ConstraintSet_flow_horizontalAlign = 44;
		public static final int ConstraintSet_flow_horizontalBias = 45;
		public static final int ConstraintSet_flow_horizontalGap = 46;
		public static final int ConstraintSet_flow_horizontalStyle = 47;
		public static final int ConstraintSet_flow_lastHorizontalBias = 48;
		public static final int ConstraintSet_flow_lastHorizontalStyle = 49;
		public static final int ConstraintSet_flow_lastVerticalBias = 50;
		public static final int ConstraintSet_flow_lastVerticalStyle = 51;
		public static final int ConstraintSet_flow_maxElementsWrap = 52;
		public static final int ConstraintSet_flow_verticalAlign = 53;
		public static final int ConstraintSet_flow_verticalBias = 54;
		public static final int ConstraintSet_flow_verticalGap = 55;
		public static final int ConstraintSet_flow_verticalStyle = 56;
		public static final int ConstraintSet_flow_wrapMode = 57;
		public static final int ConstraintSet_guidelineUseRtl = 58;
		public static final int ConstraintSet_layout_constrainedHeight = 59;
		public static final int ConstraintSet_layout_constrainedWidth = 60;
		public static final int ConstraintSet_layout_constraintBaseline_creator = 61;
		public static final int ConstraintSet_layout_constraintBaseline_toBaselineOf = 62;
		public static final int ConstraintSet_layout_constraintBaseline_toBottomOf = 63;
		public static final int ConstraintSet_layout_constraintBaseline_toTopOf = 64;
		public static final int ConstraintSet_layout_constraintBottom_creator = 65;
		public static final int ConstraintSet_layout_constraintBottom_toBottomOf = 66;
		public static final int ConstraintSet_layout_constraintBottom_toTopOf = 67;
		public static final int ConstraintSet_layout_constraintCircle = 68;
		public static final int ConstraintSet_layout_constraintCircleAngle = 69;
		public static final int ConstraintSet_layout_constraintCircleRadius = 70;
		public static final int ConstraintSet_layout_constraintDimensionRatio = 71;
		public static final int ConstraintSet_layout_constraintEnd_toEndOf = 72;
		public static final int ConstraintSet_layout_constraintEnd_toStartOf = 73;
		public static final int ConstraintSet_layout_constraintGuide_begin = 74;
		public static final int ConstraintSet_layout_constraintGuide_end = 75;
		public static final int ConstraintSet_layout_constraintGuide_percent = 76;
		public static final int ConstraintSet_layout_constraintHeight_default = 77;
		public static final int ConstraintSet_layout_constraintHeight_max = 78;
		public static final int ConstraintSet_layout_constraintHeight_min = 79;
		public static final int ConstraintSet_layout_constraintHeight_percent = 80;
		public static final int ConstraintSet_layout_constraintHorizontal_bias = 81;
		public static final int ConstraintSet_layout_constraintHorizontal_chainStyle = 82;
		public static final int ConstraintSet_layout_constraintHorizontal_weight = 83;
		public static final int ConstraintSet_layout_constraintLeft_creator = 84;
		public static final int ConstraintSet_layout_constraintLeft_toLeftOf = 85;
		public static final int ConstraintSet_layout_constraintLeft_toRightOf = 86;
		public static final int ConstraintSet_layout_constraintRight_creator = 87;
		public static final int ConstraintSet_layout_constraintRight_toLeftOf = 88;
		public static final int ConstraintSet_layout_constraintRight_toRightOf = 89;
		public static final int ConstraintSet_layout_constraintStart_toEndOf = 90;
		public static final int ConstraintSet_layout_constraintStart_toStartOf = 91;
		public static final int ConstraintSet_layout_constraintTag = 92;
		public static final int ConstraintSet_layout_constraintTop_creator = 93;
		public static final int ConstraintSet_layout_constraintTop_toBottomOf = 94;
		public static final int ConstraintSet_layout_constraintTop_toTopOf = 95;
		public static final int ConstraintSet_layout_constraintVertical_bias = 96;
		public static final int ConstraintSet_layout_constraintVertical_chainStyle = 97;
		public static final int ConstraintSet_layout_constraintVertical_weight = 98;
		public static final int ConstraintSet_layout_constraintWidth_default = 99;
		public static final int ConstraintSet_layout_constraintWidth_max = 100;
		public static final int ConstraintSet_layout_constraintWidth_min = 101;
		public static final int ConstraintSet_layout_constraintWidth_percent = 102;
		public static final int ConstraintSet_layout_editor_absoluteX = 103;
		public static final int ConstraintSet_layout_editor_absoluteY = 104;
		public static final int ConstraintSet_layout_goneMarginBaseline = 105;
		public static final int ConstraintSet_layout_goneMarginBottom = 106;
		public static final int ConstraintSet_layout_goneMarginEnd = 107;
		public static final int ConstraintSet_layout_goneMarginLeft = 108;
		public static final int ConstraintSet_layout_goneMarginRight = 109;
		public static final int ConstraintSet_layout_goneMarginStart = 110;
		public static final int ConstraintSet_layout_goneMarginTop = 111;
		public static final int ConstraintSet_layout_marginBaseline = 112;
		public static final int ConstraintSet_layout_wrapBehaviorInParent = 113;
		public static final int ConstraintSet_motionProgress = 114;
		public static final int ConstraintSet_motionStagger = 115;
		public static final int ConstraintSet_pathMotionArc = 116;
		public static final int ConstraintSet_pivotAnchor = 117;
		public static final int ConstraintSet_polarRelativeTo = 118;
		public static final int ConstraintSet_quantizeMotionSteps = 119;
		public static final int ConstraintSet_stateLabels = 120;
		public static final int ConstraintSet_transitionEasing = 121;
		public static final int ConstraintSet_transitionPathRotate = 122;
		public static final int[] CustomAttribute = new int[] { 0x7f030041, 0x7f03016b, 0x7f03016c, 0x7f03016d, 0x7f03016e, 0x7f03016f, 0x7f030170, 0x7f030172, 0x7f030173, 0x7f030174, 0x7f030331 };
		public static final int CustomAttribute_attributeName = 0;
		public static final int CustomAttribute_customBoolean = 1;
		public static final int CustomAttribute_customColorDrawableValue = 2;
		public static final int CustomAttribute_customColorValue = 3;
		public static final int CustomAttribute_customDimension = 4;
		public static final int CustomAttribute_customFloatValue = 5;
		public static final int CustomAttribute_customIntegerValue = 6;
		public static final int CustomAttribute_customPixelDimension = 7;
		public static final int CustomAttribute_customReference = 8;
		public static final int CustomAttribute_customStringValue = 9;
		public static final int CustomAttribute_methodName = 10;
		public static final int[] Grid = new int[] { 0x7f030222, 0x7f030223, 0x7f030224, 0x7f030225, 0x7f030226, 0x7f030227, 0x7f030228, 0x7f030229, 0x7f03022a, 0x7f03022b, 0x7f03022c };
		public static final int Grid_grid_columnWeights = 0;
		public static final int Grid_grid_columns = 1;
		public static final int Grid_grid_horizontalGaps = 2;
		public static final int Grid_grid_orientation = 3;
		public static final int Grid_grid_rowWeights = 4;
		public static final int Grid_grid_rows = 5;
		public static final int Grid_grid_skips = 6;
		public static final int Grid_grid_spans = 7;
		public static final int Grid_grid_useRtl = 8;
		public static final int Grid_grid_validateInputs = 9;
		public static final int Grid_grid_verticalGaps = 10;
		public static final int[] ImageFilterView = new int[] { 0x7f030033, 0x7f030079, 0x7f030090, 0x7f030151, 0x7f030166, 0x7f030250, 0x7f030251, 0x7f030252, 0x7f030253, 0x7f030383, 0x7f0303cd, 0x7f0303ce, 0x7f0303d0, 0x7f03050b };
		public static final int ImageFilterView_altSrc = 0;
		public static final int ImageFilterView_blendSrc = 1;
		public static final int ImageFilterView_brightness = 2;
		public static final int ImageFilterView_contrast = 3;
		public static final int ImageFilterView_crossfade = 4;
		public static final int ImageFilterView_imagePanX = 5;
		public static final int ImageFilterView_imagePanY = 6;
		public static final int ImageFilterView_imageRotate = 7;
		public static final int ImageFilterView_imageZoom = 8;
		public static final int ImageFilterView_overlay = 9;
		public static final int ImageFilterView_round = 10;
		public static final int ImageFilterView_roundPercent = 11;
		public static final int ImageFilterView_saturation = 12;
		public static final int ImageFilterView_warmth = 13;
		public static final int[] KeyAttribute = new int[] { 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f03016a, 0x7f03021d, 0x7f030366, 0x7f030368, 0x7f0304f2, 0x7f0304f4, 0x7f0304f6 };
		public static final int KeyAttribute_android_alpha = 0;
		public static final int KeyAttribute_android_elevation = 11;
		public static final int KeyAttribute_android_rotation = 7;
		public static final int KeyAttribute_android_rotationX = 8;
		public static final int KeyAttribute_android_rotationY = 9;
		public static final int KeyAttribute_android_scaleX = 5;
		public static final int KeyAttribute_android_scaleY = 6;
		public static final int KeyAttribute_android_transformPivotX = 1;
		public static final int KeyAttribute_android_transformPivotY = 2;
		public static final int KeyAttribute_android_translationX = 3;
		public static final int KeyAttribute_android_translationY = 4;
		public static final int KeyAttribute_android_translationZ = 10;
		public static final int KeyAttribute_curveFit = 12;
		public static final int KeyAttribute_framePosition = 13;
		public static final int KeyAttribute_motionProgress = 14;
		public static final int KeyAttribute_motionTarget = 15;
		public static final int KeyAttribute_transformPivotTarget = 16;
		public static final int KeyAttribute_transitionEasing = 17;
		public static final int KeyAttribute_transitionPathRotate = 18;
		public static final int[] KeyCycle = new int[] { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f03016a, 0x7f03021d, 0x7f030366, 0x7f030368, 0x7f0304f4, 0x7f0304f6, 0x7f03050d, 0x7f03050e, 0x7f03050f, 0x7f030510, 0x7f030511 };
		public static final int KeyCycle_android_alpha = 0;
		public static final int KeyCycle_android_elevation = 9;
		public static final int KeyCycle_android_rotation = 5;
		public static final int KeyCycle_android_rotationX = 6;
		public static final int KeyCycle_android_rotationY = 7;
		public static final int KeyCycle_android_scaleX = 3;
		public static final int KeyCycle_android_scaleY = 4;
		public static final int KeyCycle_android_translationX = 1;
		public static final int KeyCycle_android_translationY = 2;
		public static final int KeyCycle_android_translationZ = 8;
		public static final int KeyCycle_curveFit = 10;
		public static final int KeyCycle_framePosition = 11;
		public static final int KeyCycle_motionProgress = 12;
		public static final int KeyCycle_motionTarget = 13;
		public static final int KeyCycle_transitionEasing = 14;
		public static final int KeyCycle_transitionPathRotate = 15;
		public static final int KeyCycle_waveOffset = 16;
		public static final int KeyCycle_wavePeriod = 17;
		public static final int KeyCycle_wavePhase = 18;
		public static final int KeyCycle_waveShape = 19;
		public static final int KeyCycle_waveVariesBy = 20;
		public static final int[] KeyFrame = new int[] { };
		public static final int[] KeyFramesAcceleration = new int[] { };
		public static final int[] KeyFramesVelocity = new int[] { };
		public static final int[] KeyPosition = new int[] { 0x7f03016a, 0x7f030194, 0x7f03021d, 0x7f030280, 0x7f030368, 0x7f030395, 0x7f030397, 0x7f030398, 0x7f030399, 0x7f03039a, 0x7f030402, 0x7f0304f4 };
		public static final int KeyPosition_curveFit = 0;
		public static final int KeyPosition_drawPath = 1;
		public static final int KeyPosition_framePosition = 2;
		public static final int KeyPosition_keyPositionType = 3;
		public static final int KeyPosition_motionTarget = 4;
		public static final int KeyPosition_pathMotionArc = 5;
		public static final int KeyPosition_percentHeight = 6;
		public static final int KeyPosition_percentWidth = 7;
		public static final int KeyPosition_percentX = 8;
		public static final int KeyPosition_percentY = 9;
		public static final int KeyPosition_sizePercent = 10;
		public static final int KeyPosition_transitionEasing = 11;
		public static final int[] KeyTimeCycle = new int[] { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f03016a, 0x7f03021d, 0x7f030366, 0x7f030368, 0x7f0304f4, 0x7f0304f6, 0x7f03050c, 0x7f03050d, 0x7f03050e, 0x7f03050f, 0x7f030510 };
		public static final int KeyTimeCycle_android_alpha = 0;
		public static final int KeyTimeCycle_android_elevation = 9;
		public static final int KeyTimeCycle_android_rotation = 5;
		public static final int KeyTimeCycle_android_rotationX = 6;
		public static final int KeyTimeCycle_android_rotationY = 7;
		public static final int KeyTimeCycle_android_scaleX = 3;
		public static final int KeyTimeCycle_android_scaleY = 4;
		public static final int KeyTimeCycle_android_translationX = 1;
		public static final int KeyTimeCycle_android_translationY = 2;
		public static final int KeyTimeCycle_android_translationZ = 8;
		public static final int KeyTimeCycle_curveFit = 10;
		public static final int KeyTimeCycle_framePosition = 11;
		public static final int KeyTimeCycle_motionProgress = 12;
		public static final int KeyTimeCycle_motionTarget = 13;
		public static final int KeyTimeCycle_transitionEasing = 14;
		public static final int KeyTimeCycle_transitionPathRotate = 15;
		public static final int KeyTimeCycle_waveDecay = 16;
		public static final int KeyTimeCycle_waveOffset = 17;
		public static final int KeyTimeCycle_wavePeriod = 18;
		public static final int KeyTimeCycle_wavePhase = 19;
		public static final int KeyTimeCycle_waveShape = 20;
		public static final int[] KeyTrigger = new int[] { 0x7f03021d, 0x7f030368, 0x7f030369, 0x7f03036a, 0x7f03037b, 0x7f03037d, 0x7f03037e, 0x7f0304f8, 0x7f0304f9, 0x7f0304fa, 0x7f030506, 0x7f030507, 0x7f030508 };
		public static final int KeyTrigger_framePosition = 0;
		public static final int KeyTrigger_motionTarget = 1;
		public static final int KeyTrigger_motion_postLayoutCollision = 2;
		public static final int KeyTrigger_motion_triggerOnCollision = 3;
		public static final int KeyTrigger_onCross = 4;
		public static final int KeyTrigger_onNegativeCross = 5;
		public static final int KeyTrigger_onPositiveCross = 6;
		public static final int KeyTrigger_triggerId = 7;
		public static final int KeyTrigger_triggerReceiver = 8;
		public static final int KeyTrigger_triggerSlack = 9;
		public static final int KeyTrigger_viewTransitionOnCross = 10;
		public static final int KeyTrigger_viewTransitionOnNegativeCross = 11;
		public static final int KeyTrigger_viewTransitionOnPositiveCross = 12;
		public static final int[] Layout = new int[] { 0x010100c4, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x010103b5, 0x010103b6, 0x7f03006a, 0x7f03006b, 0x7f03006c, 0x7f0300b5, 0x7f03013e, 0x7f03013f, 0x7f03022d, 0x7f030294, 0x7f030295, 0x7f030296, 0x7f030297, 0x7f030298, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029c, 0x7f03029d, 0x7f03029e, 0x7f03029f, 0x7f0302a0, 0x7f0302a1, 0x7f0302a2, 0x7f0302a3, 0x7f0302a4, 0x7f0302a5, 0x7f0302a6, 0x7f0302a7, 0x7f0302a8, 0x7f0302a9, 0x7f0302aa, 0x7f0302ab, 0x7f0302ac, 0x7f0302ad, 0x7f0302ae, 0x7f0302af, 0x7f0302b0, 0x7f0302b1, 0x7f0302b2, 0x7f0302b3, 0x7f0302b4, 0x7f0302b5, 0x7f0302b7, 0x7f0302b8, 0x7f0302b9, 0x7f0302ba, 0x7f0302bb, 0x7f0302bc, 0x7f0302bd, 0x7f0302be, 0x7f0302bf, 0x7f0302c0, 0x7f0302c1, 0x7f0302c3, 0x7f0302c4, 0x7f0302c5, 0x7f0302c6, 0x7f0302c7, 0x7f0302c8, 0x7f0302c9, 0x7f0302ca, 0x7f0302cb, 0x7f0302ce, 0x7f0302d3, 0x7f030327, 0x7f03032c, 0x7f030333, 0x7f030337 };
		public static final int Layout_android_layout_height = 2;
		public static final int Layout_android_layout_marginBottom = 6;
		public static final int Layout_android_layout_marginEnd = 8;
		public static final int Layout_android_layout_marginLeft = 3;
		public static final int Layout_android_layout_marginRight = 5;
		public static final int Layout_android_layout_marginStart = 7;
		public static final int Layout_android_layout_marginTop = 4;
		public static final int Layout_android_layout_width = 1;
		public static final int Layout_android_orientation = 0;
		public static final int Layout_barrierAllowsGoneWidgets = 9;
		public static final int Layout_barrierDirection = 10;
		public static final int Layout_barrierMargin = 11;
		public static final int Layout_chainUseRtl = 12;
		public static final int Layout_constraint_referenced_ids = 13;
		public static final int Layout_constraint_referenced_tags = 14;
		public static final int Layout_guidelineUseRtl = 15;
		public static final int Layout_layout_constrainedHeight = 16;
		public static final int Layout_layout_constrainedWidth = 17;
		public static final int Layout_layout_constraintBaseline_creator = 18;
		public static final int Layout_layout_constraintBaseline_toBaselineOf = 19;
		public static final int Layout_layout_constraintBaseline_toBottomOf = 20;
		public static final int Layout_layout_constraintBaseline_toTopOf = 21;
		public static final int Layout_layout_constraintBottom_creator = 22;
		public static final int Layout_layout_constraintBottom_toBottomOf = 23;
		public static final int Layout_layout_constraintBottom_toTopOf = 24;
		public static final int Layout_layout_constraintCircle = 25;
		public static final int Layout_layout_constraintCircleAngle = 26;
		public static final int Layout_layout_constraintCircleRadius = 27;
		public static final int Layout_layout_constraintDimensionRatio = 28;
		public static final int Layout_layout_constraintEnd_toEndOf = 29;
		public static final int Layout_layout_constraintEnd_toStartOf = 30;
		public static final int Layout_layout_constraintGuide_begin = 31;
		public static final int Layout_layout_constraintGuide_end = 32;
		public static final int Layout_layout_constraintGuide_percent = 33;
		public static final int Layout_layout_constraintHeight = 34;
		public static final int Layout_layout_constraintHeight_default = 35;
		public static final int Layout_layout_constraintHeight_max = 36;
		public static final int Layout_layout_constraintHeight_min = 37;
		public static final int Layout_layout_constraintHeight_percent = 38;
		public static final int Layout_layout_constraintHorizontal_bias = 39;
		public static final int Layout_layout_constraintHorizontal_chainStyle = 40;
		public static final int Layout_layout_constraintHorizontal_weight = 41;
		public static final int Layout_layout_constraintLeft_creator = 42;
		public static final int Layout_layout_constraintLeft_toLeftOf = 43;
		public static final int Layout_layout_constraintLeft_toRightOf = 44;
		public static final int Layout_layout_constraintRight_creator = 45;
		public static final int Layout_layout_constraintRight_toLeftOf = 46;
		public static final int Layout_layout_constraintRight_toRightOf = 47;
		public static final int Layout_layout_constraintStart_toEndOf = 48;
		public static final int Layout_layout_constraintStart_toStartOf = 49;
		public static final int Layout_layout_constraintTop_creator = 50;
		public static final int Layout_layout_constraintTop_toBottomOf = 51;
		public static final int Layout_layout_constraintTop_toTopOf = 52;
		public static final int Layout_layout_constraintVertical_bias = 53;
		public static final int Layout_layout_constraintVertical_chainStyle = 54;
		public static final int Layout_layout_constraintVertical_weight = 55;
		public static final int Layout_layout_constraintWidth = 56;
		public static final int Layout_layout_constraintWidth_default = 57;
		public static final int Layout_layout_constraintWidth_max = 58;
		public static final int Layout_layout_constraintWidth_min = 59;
		public static final int Layout_layout_constraintWidth_percent = 60;
		public static final int Layout_layout_editor_absoluteX = 61;
		public static final int Layout_layout_editor_absoluteY = 62;
		public static final int Layout_layout_goneMarginBaseline = 63;
		public static final int Layout_layout_goneMarginBottom = 64;
		public static final int Layout_layout_goneMarginEnd = 65;
		public static final int Layout_layout_goneMarginLeft = 66;
		public static final int Layout_layout_goneMarginRight = 67;
		public static final int Layout_layout_goneMarginStart = 68;
		public static final int Layout_layout_goneMarginTop = 69;
		public static final int Layout_layout_marginBaseline = 70;
		public static final int Layout_layout_wrapBehaviorInParent = 71;
		public static final int Layout_maxHeight = 72;
		public static final int Layout_maxWidth = 73;
		public static final int Layout_minHeight = 74;
		public static final int Layout_minWidth = 75;
		public static final int[] MockView = new int[] { 0x7f030338, 0x7f030339, 0x7f03033a, 0x7f03033b, 0x7f03033c, 0x7f03033d };
		public static final int MockView_mock_diagonalsColor = 0;
		public static final int MockView_mock_label = 1;
		public static final int MockView_mock_labelBackgroundColor = 2;
		public static final int MockView_mock_labelColor = 3;
		public static final int MockView_mock_showDiagonals = 4;
		public static final int MockView_mock_showLabel = 5;
		public static final int[] Motion = new int[] { 0x7f030035, 0x7f030038, 0x7f030194, 0x7f030365, 0x7f030367, 0x7f030395, 0x7f0303b4, 0x7f0303b5, 0x7f0303b6, 0x7f0304f4 };
		public static final int Motion_animateCircleAngleTo = 0;
		public static final int Motion_animateRelativeTo = 1;
		public static final int Motion_drawPath = 2;
		public static final int Motion_motionPathRotate = 3;
		public static final int Motion_motionStagger = 4;
		public static final int Motion_pathMotionArc = 5;
		public static final int Motion_quantizeMotionInterpolator = 6;
		public static final int Motion_quantizeMotionPhase = 7;
		public static final int Motion_quantizeMotionSteps = 8;
		public static final int Motion_transitionEasing = 9;
		public static final int[] MotionEffect = new int[] { 0x7f03035b, 0x7f03035c, 0x7f03035d, 0x7f03035e, 0x7f03035f, 0x7f030360, 0x7f030361, 0x7f030362 };
		public static final int MotionEffect_motionEffect_alpha = 0;
		public static final int MotionEffect_motionEffect_end = 1;
		public static final int MotionEffect_motionEffect_move = 2;
		public static final int MotionEffect_motionEffect_start = 3;
		public static final int MotionEffect_motionEffect_strict = 4;
		public static final int MotionEffect_motionEffect_translationX = 5;
		public static final int MotionEffect_motionEffect_translationY = 6;
		public static final int MotionEffect_motionEffect_viewTransition = 7;
		public static final int[] MotionHelper = new int[] { 0x7f03037c, 0x7f03037f };
		public static final int MotionHelper_onHide = 0;
		public static final int MotionHelper_onShow = 1;
		public static final int[] MotionLabel = new int[] { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x010100af, 0x0101014f, 0x01010164, 0x010103ac, 0x01010535, 0x7f03007a, 0x7f03007b, 0x7f0303d1, 0x7f030490, 0x7f030491, 0x7f030492, 0x7f030493, 0x7f030494, 0x7f0304a2, 0x7f0304a3, 0x7f0304a4, 0x7f0304a5, 0x7f0304a7, 0x7f0304a8, 0x7f0304a9, 0x7f0304aa };
		public static final int MotionLabel_android_autoSizeTextType = 8;
		public static final int MotionLabel_android_fontFamily = 7;
		public static final int MotionLabel_android_gravity = 4;
		public static final int MotionLabel_android_shadowRadius = 6;
		public static final int MotionLabel_android_text = 5;
		public static final int MotionLabel_android_textColor = 3;
		public static final int MotionLabel_android_textSize = 0;
		public static final int MotionLabel_android_textStyle = 2;
		public static final int MotionLabel_android_typeface = 1;
		public static final int MotionLabel_borderRound = 9;
		public static final int MotionLabel_borderRoundPercent = 10;
		public static final int MotionLabel_scaleFromTextSize = 11;
		public static final int MotionLabel_textBackground = 12;
		public static final int MotionLabel_textBackgroundPanX = 13;
		public static final int MotionLabel_textBackgroundPanY = 14;
		public static final int MotionLabel_textBackgroundRotate = 15;
		public static final int MotionLabel_textBackgroundZoom = 16;
		public static final int MotionLabel_textOutlineColor = 17;
		public static final int MotionLabel_textOutlineThickness = 18;
		public static final int MotionLabel_textPanX = 19;
		public static final int MotionLabel_textPanY = 20;
		public static final int MotionLabel_textureBlurFactor = 21;
		public static final int MotionLabel_textureEffect = 22;
		public static final int MotionLabel_textureHeight = 23;
		public static final int MotionLabel_textureWidth = 24;
		public static final int[] MotionLayout = new int[] { 0x7f03003c, 0x7f030167, 0x7f03028c, 0x7f03033e, 0x7f030366, 0x7f0303f5 };
		public static final int MotionLayout_applyMotionScene = 0;
		public static final int MotionLayout_currentState = 1;
		public static final int MotionLayout_layoutDescription = 2;
		public static final int MotionLayout_motionDebug = 3;
		public static final int MotionLayout_motionProgress = 4;
		public static final int MotionLayout_showPaths = 5;
		public static final int[] MotionScene = new int[] { 0x7f03017b, 0x7f03028d };
		public static final int MotionScene_defaultDuration = 0;
		public static final int MotionScene_layoutDuringTransition = 1;
		public static final int[] MotionTelltales = new int[] { 0x7f030467, 0x7f030468, 0x7f030469 };
		public static final int MotionTelltales_telltales_tailColor = 0;
		public static final int MotionTelltales_telltales_tailScale = 1;
		public static final int MotionTelltales_telltales_velocityMode = 2;
		public static final int[] OnClick = new int[] { 0x7f0300e2, 0x7f030465 };
		public static final int OnClick_clickAction = 0;
		public static final int OnClick_targetId = 1;
		public static final int[] OnSwipe = new int[] { 0x7f030043, 0x7f030191, 0x7f030192, 0x7f030193, 0x7f0302d7, 0x7f030323, 0x7f03032b, 0x7f03036b, 0x7f030374, 0x7f030381, 0x7f0303cc, 0x7f030413, 0x7f030414, 0x7f030415, 0x7f030416, 0x7f030417, 0x7f0304e1, 0x7f0304e2, 0x7f0304e3 };
		public static final int OnSwipe_autoCompleteMode = 0;
		public static final int OnSwipe_dragDirection = 1;
		public static final int OnSwipe_dragScale = 2;
		public static final int OnSwipe_dragThreshold = 3;
		public static final int OnSwipe_limitBoundsTo = 4;
		public static final int OnSwipe_maxAcceleration = 5;
		public static final int OnSwipe_maxVelocity = 6;
		public static final int OnSwipe_moveWhenScrollAtTop = 7;
		public static final int OnSwipe_nestedScrollFlags = 8;
		public static final int OnSwipe_onTouchUp = 9;
		public static final int OnSwipe_rotationCenterId = 10;
		public static final int OnSwipe_springBoundary = 11;
		public static final int OnSwipe_springDamping = 12;
		public static final int OnSwipe_springMass = 13;
		public static final int OnSwipe_springStiffness = 14;
		public static final int OnSwipe_springStopThreshold = 15;
		public static final int OnSwipe_touchAnchorId = 16;
		public static final int OnSwipe_touchAnchorSide = 17;
		public static final int OnSwipe_touchRegionId = 18;
		public static final int[] PropertySet = new int[] { 0x010100dc, 0x0101031f, 0x7f0302b6, 0x7f030366, 0x7f030509 };
		public static final int PropertySet_android_alpha = 1;
		public static final int PropertySet_android_visibility = 0;
		public static final int PropertySet_layout_constraintTag = 2;
		public static final int PropertySet_motionProgress = 3;
		public static final int PropertySet_visibilityMode = 4;
		public static final int[] State = new int[] { 0x010100d0, 0x7f030140 };
		public static final int State_android_id = 0;
		public static final int State_constraints = 1;
		public static final int[] StateSet = new int[] { 0x7f030180 };
		public static final int StateSet_defaultState = 0;
		public static final int[] TextEffects = new int[] { 0x01010095, 0x01010096, 0x01010097, 0x0101014f, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x7f03007a, 0x7f03007b, 0x7f030498, 0x7f0304a2, 0x7f0304a3 };
		public static final int TextEffects_android_fontFamily = 8;
		public static final int TextEffects_android_shadowColor = 4;
		public static final int TextEffects_android_shadowDx = 5;
		public static final int TextEffects_android_shadowDy = 6;
		public static final int TextEffects_android_shadowRadius = 7;
		public static final int TextEffects_android_text = 3;
		public static final int TextEffects_android_textSize = 0;
		public static final int TextEffects_android_textStyle = 2;
		public static final int TextEffects_android_typeface = 1;
		public static final int TextEffects_borderRound = 9;
		public static final int TextEffects_borderRoundPercent = 10;
		public static final int TextEffects_textFillColor = 11;
		public static final int TextEffects_textOutlineColor = 12;
		public static final int TextEffects_textOutlineThickness = 13;
		public static final int[] Transform = new int[] { 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f0304f2 };
		public static final int Transform_android_elevation = 10;
		public static final int Transform_android_rotation = 6;
		public static final int Transform_android_rotationX = 7;
		public static final int Transform_android_rotationY = 8;
		public static final int Transform_android_scaleX = 4;
		public static final int Transform_android_scaleY = 5;
		public static final int Transform_android_transformPivotX = 0;
		public static final int Transform_android_transformPivotY = 1;
		public static final int Transform_android_translationX = 2;
		public static final int Transform_android_translationY = 3;
		public static final int Transform_android_translationZ = 9;
		public static final int Transform_transformPivotTarget = 11;
		public static final int[] Transition = new int[] { 0x010100d0, 0x7f03004b, 0x7f03013c, 0x7f03013d, 0x7f0301a4, 0x7f03028d, 0x7f030363, 0x7f030395, 0x7f03041a, 0x7f0304f3, 0x7f0304f5 };
		public static final int Transition_android_id = 0;
		public static final int Transition_autoTransition = 1;
		public static final int Transition_constraintSetEnd = 2;
		public static final int Transition_constraintSetStart = 3;
		public static final int Transition_duration = 4;
		public static final int Transition_layoutDuringTransition = 5;
		public static final int Transition_motionInterpolator = 6;
		public static final int Transition_pathMotionArc = 7;
		public static final int Transition_staggered = 8;
		public static final int Transition_transitionDisable = 9;
		public static final int Transition_transitionFlags = 10;
		public static final int[] Variant = new int[] { 0x7f030140, 0x7f0303c4, 0x7f0303c5, 0x7f0303c6, 0x7f0303c7 };
		public static final int Variant_constraints = 0;
		public static final int Variant_region_heightLessThan = 1;
		public static final int Variant_region_heightMoreThan = 2;
		public static final int Variant_region_widthLessThan = 3;
		public static final int Variant_region_widthMoreThan = 4;
		public static final int[] ViewTransition = new int[] { 0x010100d0, 0x7f030000, 0x7f030001, 0x7f0300e1, 0x7f0301a4, 0x7f03024d, 0x7f03024e, 0x7f030363, 0x7f030368, 0x7f030380, 0x7f030395, 0x7f0303e1, 0x7f0304f3, 0x7f0304fc, 0x7f030505 };
		public static final int ViewTransition_SharedValue = 1;
		public static final int ViewTransition_SharedValueId = 2;
		public static final int ViewTransition_android_id = 0;
		public static final int ViewTransition_clearsTag = 3;
		public static final int ViewTransition_duration = 4;
		public static final int ViewTransition_ifTagNotSet = 5;
		public static final int ViewTransition_ifTagSet = 6;
		public static final int ViewTransition_motionInterpolator = 7;
		public static final int ViewTransition_motionTarget = 8;
		public static final int ViewTransition_onStateTransition = 9;
		public static final int ViewTransition_pathMotionArc = 10;
		public static final int ViewTransition_setsTag = 11;
		public static final int ViewTransition_transitionDisable = 12;
		public static final int ViewTransition_upDuration = 13;
		public static final int ViewTransition_viewTransitionMode = 14;
		public static final int[] include = new int[] { 0x7f03013b };
		public static final int include_constraintSet = 0;
	}
}
