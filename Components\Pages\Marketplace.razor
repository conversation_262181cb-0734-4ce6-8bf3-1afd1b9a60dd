@page "/marketplace"
@inject NavigationManager Navigation

<div class="marketplace-container">
    <div class="header-section">
        <div class="school-info">
            <div class="school-avatar">
                <img src="images/lin.png" alt="Lincoln Logo" class="school-logo-img" />
            </div>
            <div class="school-details">
                <h1 class="school-name">Lincoln High School</h1>
                <p class="student-count">2,847 active students</p>
            </div>
            <div class="verified-badge">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M20 6L9 17L4 12" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <span>Verified</span>
            </div>
        </div>

        <div class="search-section">
            <div class="search-bar">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" class="search-icon">
                    <circle cx="11" cy="11" r="8" stroke="#9CA3AF" stroke-width="2" />
                    <path d="M21 21L16.65 16.65" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" />
                </svg>
                <input type="text" placeholder="Search textbooks, supplies, electronics..." class="search-input" />
                <button class="menu-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="#9CA3AF">
                        <circle cx="12" cy="5" r="2"/>
                        <circle cx="12" cy="12" r="2"/>
                        <circle cx="12" cy="19" r="2"/>
                    </svg>
                </button>
            </div>
        </div>

        <div class="filter-tabs">
            <div class="tab-scroll">
                <button class="filter-tab active">Textbooks</button>
                <button class="filter-tab">Electronics</button>
                <button class="filter-tab">Supplies</button>
                <button class="filter-tab">Dorm Items</button>
            </div>
            <div class="location-info">
                <svg width="16" height="16" fill="#3B82F6" viewBox="0 0 24 24">
                    <path d="M12 2C8.13 2 5 5.13 5 9C5 13.25 12 22 12 22C12 22 19 13.25 19 9C19 5.13 15.87 2 12 2ZM12 11.5C10.62 11.5 9.5 10.38 9.5 9C9.5 7.62 10.62 6.5 12 6.5C13.38 6.5 14.5 7.62 14.5 9C14.5 10.38 13.38 11.5 12 11.5Z" />
                </svg>
                <span>Building A - Room 205</span>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <div class="main-content">
        <!-- Trending section -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">Trending in Your Grade</h2>
                <button class="see-all-btn">See all</button>
            </div>
            <div class="trending-items">
                <div class="trending-item">
                    <div class="item-image">
                        <img src="/images/book.jpg" alt="Calculus AP" class="product-image" />
                        <div class="trending-badge hot">Hot Deal</div>
                        
                    </div>
                    <div class="item-info">
                        <h3 class="item-title">Calculus AP</h3>
                        <p class="item-details">Grade 12 • Building B</p>
                        <p class="item-price">$45</p>
                    </div>
                </div>
                <div class="trending-item">
                    <div class="item-image">
                        <img src="/images/phone.jpg" alt="TI-84 Calculator" class="product-image" />
                        
                        <div class="trending-badge left">2 Left</div>
                    </div>
                    <div class="item-info">
                        <h3 class="item-title">TI-84 Calculator</h3>
                        <p class="item-details">Grade 11 • Building A</p>
                        <p class="item-price">$89</p>
                    </div>
                </div>
            </div>
        </div>
        </div>
        <!-- Categories -->
        <div class="section">
            <h2 class="section-title">Categories</h2>
            <div class="categories-grid">
                <div class="category-item">
                    <div class="category-icon blue">📚</div>
                    <h3 class="category-title">Your Course Books</h3>
                    <p class="category-count">24 items</p>
                </div>
                <div class="category-item">
                    <div class="category-icon green">🧪</div>
                    <h3 class="category-title">Lab Equipment</h3>
                    <p class="category-count">12 items</p>
                </div>
                <div class="category-item">
                    <div class="category-icon orange">💻</div>
                    <h3 class="category-title">Tech & Gadgets</h3>
                    <p class="category-count">18 items</p>
                </div>
                <div class="category-item">
                    <div class="category-icon purple">📝</div>
                    <h3 class="category-title">Study Materials</h3>
                    <p class="category-count">31 items</p>
                </div>
            </div>
        </div>

        <!-- Recent Listings -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">Recent Listings</h2>
                <div class="filter-sort">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M3 6H21" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round"/>
                        <path d="M7 12H17" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round"/>
                        <path d="M10 18H14" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round"/>
                    </svg>
                </div>
            </div>
            <div class="recent-listings">
                <div class="listing-item">
                    <div class="listing-image">
                        <img src="/images/light.png" alt="Physics Book" class="listing-photo" />
                    </div>
                    <div class="listing-details">
                        <div class="listing-header">
                            <h3 class="listing-title">Physics: Principles & Problems</h3>
                            <button class="favorite-btn">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                    <path d="M20.84 4.61C19.32 3.09 17.16 3.09 15.64 4.61L12 8.25L8.36 4.61C6.84 3.09 4.68 3.09 3.16 4.61C1.64 6.13 1.64 8.29 3.16 9.81L12 18.65L20.84 9.81C22.36 8.29 22.36 6.13 20.84 4.61Z" stroke="#EF4444" stroke-width="2"/>
                                </svg>
                            </button>
                        </div>
                        <div class="listing-tags">
                            <span class="tag verified">Verified</span>
                            <span class="course-tag">Same Course</span>
                        </div>
                        <p class="listing-price">$35</p>
                        <p class="listing-condition">Grade 11 Physics • Excellent condition</p>
                        <button class="message-btn">Message</button>
                    </div>
                </div>

                <div class="listing-item">
                    <div class="listing-image">
                        <img src="/images/headphone.png" alt="Sony Headphones" class="listing-photo" />
                    </div>
                    <div class="listing-details">
                        <div class="listing-header">
                            <h3 class="listing-title">Sony WH-1000XM4</h3>
                            <button class="favorite-btn">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                    <path d="M20.84 4.61C19.32 3.09 17.16 3.09 15.64 4.61L12 8.25L8.36 4.61C6.84 3.09 4.68 3.09 3.16 4.61C1.64 6.13 1.64 8.29 3.16 9.81L12 18.65L20.84 9.81C22.36 8.29 22.36 6.13 20.84 4.61Z" stroke="#EF4444" stroke-width="2" fill="#EF4444"/>
                                </svg>
                            </button>
                        </div>
                        <div class="listing-tags">
                            <span class="tag verified">Verified</span>
                        </div>
                        <p class="listing-price">$220</p>
                        <p class="listing-condition">Like new • Building A pickup</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom navigation -->
<div class="bottom-nav">
    <button class="nav-item active">
        <div class="icon-wrap">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="#3B82F6" stroke-width="2" fill="#3B82F6" />
            </svg>
            <span class="nav-label">Home</span>
        </div>
    </button>
    <button class="nav-item">
        <div class="icon-wrap">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <circle cx="11" cy="11" r="8" stroke="#9CA3AF" stroke-width="2" />
                <path d="M21 21L16.65 16.65" stroke="#9CA3AF" stroke-width="2" />
            </svg>
            <span class="nav-label">Search</span>
        </div>
    </button>
    <button class="nav-item">
        <div class="icon-wrap">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" stroke="#9CA3AF" stroke-width="2" />
                <path d="M12 6V12L16 14" stroke="#9CA3AF" stroke-width="2" />
            </svg>
            <span class="nav-label">Sell</span>
        </div>
    </button>
    <button class="nav-item">
        <div class="icon-wrap">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M20.84 4.61C19.32 3.09 17.16 3.09 15.64 4.61L12 8.25L8.36 4.61C6.84 3.09 4.68 3.09 3.16 4.61C1.64 6.13 1.64 8.29 3.16 9.81L12 18.65L20.84 9.81C22.36 8.29 22.36 6.13 20.84 4.61Z" stroke="#9CA3AF" stroke-width="2" />
            </svg>
            <span class="nav-label">Saved</span>
        </div>
    </button>
    <button class="nav-item">
        <div class="icon-wrap">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="#9CA3AF" stroke-width="2" />
                <circle cx="12" cy="7" r="4" stroke="#9CA3AF" stroke-width="2" />
            </svg>
            <span class="nav-label">Profile</span>
        </div>
    </button>
</div>


<style>
    .marketplace-container {
        display: flex;
        flex-direction: column;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: #FFFFFF;
        min-height: 100vh;
        width: 375px;
        margin: 0 auto;
        position: relative;
        padding-bottom: 80px;
    }

    .header-section {
        padding: 16px 20px;
        background: white;
        border-bottom: 1px solid #F3F4F6;
    }

    .school-info {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 16px;
    }

    .school-avatar {
        width: 40px;
        height: 40px;
        background: #3B82F6;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 18px;
    }

    .school-details {
        flex: 1;
    }

    .school-name {
        font-size: 18px;
        font-weight: 700;
        color: #000000;
        margin: 0 0 4px 0;
    }

    .student-count {
        font-size: 14px;
        color: #6B7280;
        margin: 0;
    }

    .verified-badge {
        background: #10B981;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .search-section {
        margin-bottom: 8px;
    }

    .search-bar {
        position: relative;
        display: flex;
        align-items: center;
    }

    .search-icon {
        position: absolute;
        left: 12px;
        z-index: 1;
    }

    .search-input {
        width: 100%;
        height: 44px;
        padding: 0 16px 0 44px;
        border: 1px solid #E5E7EB;
        border-radius: 22px;
        font-size: 10px;
        background: #F9FAFB;
        box-sizing: border-box;
    }

    .search-input:focus {
        outline: none;
        border-color: #3B82F6;
        background: white;
    }

    .search-input::placeholder {
        color: #9CA3AF;
    }

    .filter-tabs {
        padding: 0 20px 16px 20px;
        background: white;
        border-bottom: 1px solid #F3F4F6;
    }

    .tab-scroll {
        display: flex;
        gap: 8px;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .tab-scroll::-webkit-scrollbar {
        display: none;
    }

    .menu-icon {
        position: absolute;
        right: 12px;
        background: none;
        border: none;
        cursor: pointer;
    }

    .trending-item .item-image {
        position: relative;
        
    }


    .trending-badge {
        position: absolute;
        top: 8px;
        right: 8px;
        background-color: #EF4444;
        color: white;
        font-size: 10px;
        font-weight: 600;
        padding: 2px 6px;
        border-radius: 8px;
    }

        .trending-badge.left {
            left: auto;
            right: 8px;
            background-color: #F59E0B;
        }

    .tag.verified {
        background: #D1FAE5;
        color: #059669;
    }

    .filter-tab {
        background: #F3F4F6;
        border: none;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        color: #6B7280;
        cursor: pointer;
        white-space: nowrap;
        flex-shrink: 0;
    }

    .filter-tab.active {
        background: #3B82F6;
        color: white;
    }

    .main-content {
        flex: 1;
        padding: 20px;
    }

    .section {
        margin-bottom: 32px;
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
    }

    .section-title {
        font-size: 20px;
        font-weight: 700;
        color: #000000;
        margin: 0;
    }

    .see-all-btn {
        background: none;
        border: none;
        color: #3B82F6;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
    }

    .trending-items {
        display: flex;
        gap: 16px;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .trending-items::-webkit-scrollbar {
        display: none;
    }

    .trending-item {
        flex-shrink: 0;
        width: 140px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .item-image {
        width: 100%; /* Keep inside parent */
        height: 150px;
        background: #F9FAFB;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden; /* Hide image overflow if enlarged */
    }

        .item-image img {
            max-width: 120%; /* Increase image size */
            max-height: 120%;
            object-fit: contain;
        }

    .product-image {
        width: 60px;
        height: 60px;
        object-fit: contain;
    }

    .item-info {
        padding: 12px;
    }

    .item-title {
        font-size: 14px;
        font-weight: 600;
        color: #000000;
        margin: 0 0 4px 0;
    }

    .item-details {
        font-size: 12px;
        color: #6B7280;
        margin: 0 0 8px 0;
    }

    .item-price {
        font-size: 16px;
        font-weight: 700;
        color: #000000;
        margin: 0;
    }

    .categories-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
    }

    .category-item {
        background: white;
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        text-align: center;
    }

    .category-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 12px auto;
        font-size: 24px;
    }

    .category-icon.blue {
        background: #DBEAFE;
    }

    .category-icon.green {
        background: #D1FAE5;
    }

    .category-icon.orange {
        background: #FED7AA;
    }

    .category-icon.purple {
        background: #E9D5FF;
    }

    .category-title {
        font-size: 14px;
        font-weight: 600;
        color: #000000;
        margin: 0 0 4px 0;
    }

    .category-count {
        font-size: 12px;
        color: #6B7280;
        margin: 0;
    }

    .filter-sort {
        display: flex;
        align-items: center;
        cursor: pointer;
    }

    .recent-listings {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .listing-item {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        overflow: hidden;
        display: flex;
    }

    .listing-image {
        width: 100px;
        height: 100px;
        background: #F9FAFB;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .listing-photo {
        width: 50px;
        height: 50px;
        object-fit: contain;
    }

    .listing-details {
        flex: 1;
        padding: 12px 16px 12px 12px;
    }

    .listing-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 8px;
    }

    .listing-title {
        font-size: 16px;
        font-weight: 600;
        color: #000000;
        margin: 0;
        flex: 1;
    }

    .favorite-btn {
        background: none;
        border: none;
        cursor: pointer;
        padding: 4px;
        margin-left: 8px;
    }

    .listing-tags {
        display: flex;
        gap: 8px;
        margin-bottom: 8px;
    }

    .tag {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .tag.new {
        background: #10B981;
        color: white;
    }

    .course-tag {
        background: #DBEAFE;
        color: #1D4ED8;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .listing-price {
        font-size: 18px;
        font-weight: 700;
        color: #000000;
        margin: 0 0 4px 0;
    }

    .listing-condition {
        font-size: 14px;
        color: #6B7280;
        margin: 0 0 12px 0;
    }

    .message-btn {
        background: #3B82F6;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
    }

    .bottom-nav {
        position: fixed;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 375px;
        height: 80px;
        background: white;
        border-top: 1px solid #F3F4F6;
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding: 0 12px;
        box-sizing: border-box;
        z-index: 1000;
    }

    .nav-item {
        background: none;
        border: none;
        cursor: pointer;
        padding: 4px;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        flex: 1;
    }

        .nav-item .icon-wrap {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

    .nav-label {
        font-size: 10px;
        font-weight: 500;
        color: #9CA3AF;
        margin-top: 4px;
    }

    .nav-item.active svg path,
    .nav-item.active svg circle {
        stroke: #3B82F6;
        fill: #3B82F6;
    }

    .nav-item.active .nav-label {
        color: #3B82F6;
    }
</style>

@code {
    // Component logic here
}
