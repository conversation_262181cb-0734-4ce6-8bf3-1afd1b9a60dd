/* AUTO-GENERATED FILE. DO NOT MODIFY.
 *
 * This class was automatically generated by
 * .NET for Android from the resource data it found.
 * It should not be modified by hand.
 */
package androidx.cardview;

public final class R {
	public static final class attr {
		public static final int cardBackgroundColor = 0x7f0300a1;
		public static final int cardCornerRadius = 0x7f0300a2;
		public static final int cardElevation = 0x7f0300a3;
		public static final int cardMaxElevation = 0x7f0300a5;
		public static final int cardPreventCornerOverlap = 0x7f0300a6;
		public static final int cardUseCompatPadding = 0x7f0300a7;
		public static final int cardViewStyle = 0x7f0300a8;
		public static final int contentPadding = 0x7f030149;
		public static final int contentPaddingBottom = 0x7f03014a;
		public static final int contentPaddingLeft = 0x7f03014c;
		public static final int contentPaddingRight = 0x7f03014d;
		public static final int contentPaddingTop = 0x7f03014f;
	}
	public static final class color {
		public static final int cardview_dark_background = 0x7f05002f;
		public static final int cardview_light_background = 0x7f050030;
		public static final int cardview_shadow_end_color = 0x7f050031;
		public static final int cardview_shadow_start_color = 0x7f050032;
	}
	public static final class dimen {
		public static final int cardview_compat_inset_shadow = 0x7f060054;
		public static final int cardview_default_elevation = 0x7f060055;
		public static final int cardview_default_radius = 0x7f060056;
	}
	public static final class style {
		public static final int Base_CardView = 0x7f100013;
		public static final int CardView = 0x7f100122;
		public static final int CardView_Dark = 0x7f100123;
		public static final int CardView_Light = 0x7f100124;
	}
	public static final class styleable {
		public static final int[] CardView = new int[] { 0x0101013f, 0x01010140, 0x7f0300a1, 0x7f0300a2, 0x7f0300a3, 0x7f0300a5, 0x7f0300a6, 0x7f0300a7, 0x7f030149, 0x7f03014a, 0x7f03014c, 0x7f03014d, 0x7f03014f };
		public static final int CardView_android_minHeight = 1;
		public static final int CardView_android_minWidth = 0;
		public static final int CardView_cardBackgroundColor = 2;
		public static final int CardView_cardCornerRadius = 3;
		public static final int CardView_cardElevation = 4;
		public static final int CardView_cardMaxElevation = 5;
		public static final int CardView_cardPreventCornerOverlap = 6;
		public static final int CardView_cardUseCompatPadding = 7;
		public static final int CardView_contentPadding = 8;
		public static final int CardView_contentPaddingBottom = 9;
		public static final int CardView_contentPaddingLeft = 10;
		public static final int CardView_contentPaddingRight = 11;
		public static final int CardView_contentPaddingTop = 12;
	}
}
