<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Page - Exact Replica</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            background: linear-gradient(90deg, #FFFFFF 0%, #F8FCFF 40%, #F0F8FF 70%, #E8F4FD 100%);
            border-radius: 25px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
            overflow: hidden;
            position: relative;
        }

        .login-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif;
            background: linear-gradient(90deg, #FFFFFF 0%, #F8FCFF 40%, #F0F8FF 70%, #E8F4FD 100%);
            min-height: 100%;
            width: 100%;
            box-sizing: border-box;
            position: relative;
        }

        .logo-section {
            margin-top: 80px;
            margin-bottom: 120px;
            display: flex;
            justify-content: center;
        }

        .logo-placeholder {
            width: 165px;
            height: 121px;
            background: linear-gradient(135deg, #4ECDC4 0%, #FF8A65 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: 800;
            text-align: center;
        }

        .form-section {
            width: 100%;
            max-width: 320px;
            display: flex;
            flex-direction: column;
            gap: 24px;
            margin-bottom: 40px;
        }

        .input-group {
            display: flex;
            flex-direction: column;
        }

        .input-label {
            font-size: 14px;
            font-weight: 600;
            color: #333333;
            margin-bottom: 8px;
        }

        .input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-icon {
            position: absolute;
            left: 16px;
            z-index: 2;
        }

        .form-input {
            width: 100%;
            height: 48px;
            padding: 12px 16px 12px 48px;
            border: 1px solid #E0E0E0;
            border-radius: 8px;
            font-size: 16px;
            color: #333333;
            background-color: #FAFAFA;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: #00BFFF;
            box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.1);
            background-color: white;
        }

        .form-input::placeholder {
            color: #AAAAAA;
        }

        .password-toggle {
            position: absolute;
            right: 16px;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
        }

        .password-toggle:hover {
            opacity: 0.7;
        }

        .login-button {
            width: 100%;
            max-width: 320px;
            height: 50px;
            background-color: #00BFFF;
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 24px;
            box-shadow: 0 3px 8px rgba(0, 191, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .login-button:hover {
            background-color: #1C7ED6;
        }

        .signup-text {
            font-size: 14px;
            color: #666666;
            text-align: center;
            margin: 0;
        }

        .signup-link {
            color: #00BFFF;
            text-decoration: none;
            font-weight: 600;
        }

        .signup-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="phone-frame">
        <div class="login-container">
            <!-- Logo Section -->
            <div class="logo-section">
                <div class="logo-placeholder">
                    <span style="color:#4ECDC4;">GATE</span><span style="color:#FF8A65;">SALE</span>
                </div>
            </div>

            <!-- Form Section -->
            <div class="form-section">
                <!-- School Email Address -->
                <div class="input-group">
                    <label class="input-label">School Email Address</label>
                    <div class="input-wrapper">
                        <svg class="input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="#AAAAAA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M22 6L12 13L2 6" stroke="#AAAAAA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <input type="email" class="form-input" placeholder="<EMAIL>" />
                    </div>
                </div>

                <!-- Password -->
                <div class="input-group">
                    <label class="input-label">Password</label>
                    <div class="input-wrapper">
                        <svg class="input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="#AAAAAA" stroke-width="2"/>
                            <circle cx="12" cy="16" r="1" fill="#AAAAAA"/>
                            <path d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11" stroke="#AAAAAA" stroke-width="2"/>
                        </svg>
                        <input type="password" class="form-input" placeholder="Enter password" />
                        <button type="button" class="password-toggle">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="#AAAAAA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="12" cy="12" r="3" stroke="#AAAAAA" stroke-width="2"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Login Button -->
            <button class="login-button">
                Login →
            </button>

            <!-- Sign Up Link -->
            <p class="signup-text">
                Don't have an account? <a href="#" class="signup-link">Sign Up</a>
            </p>
        </div>
    </div>
</body>
</html>
