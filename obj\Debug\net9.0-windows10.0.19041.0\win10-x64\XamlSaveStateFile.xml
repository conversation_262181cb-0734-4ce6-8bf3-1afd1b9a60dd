﻿<?xml version="1.0" encoding="utf-8"?><XamlCompilerSaveState><XamlFeatureControlFlags>EnableXBindDiagnostics, EnableDefaultValidationContextGeneration, EnableWin32Codegen, UsingCSWinRT</XamlFeatureControlFlags><ReferenceAssemblyList><LocalAssembly PathName="d:\projects\gatesale\obj\debug\net9.0-windows10.0.19041.0\win10-x64\intermediatexaml\gatesale.dll" HashGuid="bc5caf52-656b-7afe-2097-ed74eaadc2a6" /><ReferenceAssembly PathName="c:\program files\microsoft visual studio\2022\community\common7\ide\commonextensions\microsoft\xamldiagnostics\mobile\microsoft.maui.controls.hotreload.forms.dll" HashGuid="5314f39f-c98a-8831-e7c9-fd2e9ab21201" /><ReferenceAssembly PathName="c:\program files\microsoft visual studio\2022\community\common7\ide\commonextensions\microsoft\xamldiagnostics\mobile\microsoft.visualstudio.designtools.mobiletapcontracts.dll" HashGuid="5c296ef8-8151-da7a-16b2-5d070c4702f2" /><ReferenceAssembly PathName="c:\program files\microsoft visual studio\2022\community\common7\ide\commonextensions\microsoft\xamldiagnostics\mobile\microsoft.visualstudio.designtools.tapcontract.dll" HashGuid="3a4b2c5b-572a-bdd5-3cbe-175999284448" /><ReferenceAssembly PathName="c:\users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.2903.40\lib_manual\net8.0-windows10.0.17763.0\microsoft.web.webview2.core.projection.dll" HashGuid="10206ce0-5656-b06f-f85b-0c1b4799072a" /></ReferenceAssemblyList><XamlSourceFileDataList><XamlSourceFileData XamlFileName="Platforms\Windows\App.xaml" ClassFullName="GateSale.WinUI.App" GeneratedCodePathPrefix="D:\Projects\GateSale\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\Platforms\Windows\App" XamlFileTimeAtLastCompileInTicks="638883859749605236" HasBoundEventAssignments="False" /></XamlSourceFileDataList></XamlCompilerSaveState>