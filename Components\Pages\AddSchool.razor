@page "/addschool"
@inject NavigationManager Navigation

<div class="add-school-container">
    <!-- Header with back button -->
    <div class="header-section">
        <button class="back-button" @onclick="GoBack" aria-label="Go back">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" role="img" aria-hidden="true">
                <path d="M15 18L9 12L15 6" stroke="#333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </button>
    </div>

    <!-- School illustration -->
    <div class="illustration-section">
        <div class="school-illustration">
            <div class="image-wrapper">
                <img src="/images/school.jpg" alt="School Building" class="school-image" />
            </div>            <!-- Green location pin badge -->
            <div class="location-badge" title="Add your school">
                <svg width="16" height="16" viewBox="0 0 24 24" role="img" aria-hidden="true">
                    <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7Zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5S10.62 6.5 12 6.5s2.5 1.12 2.5 2.5S13.38 11.5 12 11.5Z" fill="white"/>
                </svg>
            </div>
        </div>
    </div>

    <!-- Title and description -->
    <div class="content-section">
        <h1 class="main-title">Oops — We Don't Support Your School Yet</h1>
        <p class="description">
            Tell us which school you're from, and we'll add it soon. We're working hard to support new schools everywhere!
        </p>
    </div>

    <!-- Form section -->
    <div class="form-section">
        <!-- School Name -->
        <div class="input-group">
            <label class="input-label">School Name</label>
            <input type="text" class="form-input" placeholder="Enter your school name" @bind="SchoolName" />
        </div>

        <!-- City -->
        <div class="input-group">
            <label class="input-label">City</label>
            <input type="text" class="form-input" placeholder="City where your school is located" @bind="City" />
        </div>

        <!-- Student Email -->
        <div class="input-group">
            <label class="input-label">Student Email</label>
            <input type="email" class="form-input" placeholder="<EMAIL>" @bind="StudentEmail" />
        </div>

        <!-- School Contact Person -->
        <div class="input-group">
            <label class="input-label">School Contact Person <span class="optional">(Optional)</span></label>
            <input type="text" class="form-input" placeholder="Name of teacher or administrator" @bind="ContactPerson" />
        </div>
    </div>

    <!-- Request button -->
    <div class="button-section">
        <button class="request-button" @onclick="RequestSchoolAccess" >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" style="margin-right:8px;" aria-hidden="true">
                <path d="M2 21l21-9L2 3v7l15 2-15 2v7Z" fill="white"/>
            </svg>
            Request School Access
        </button>
    </div>

    <!-- Footer note -->
    <div class="footer-section">
        <div class="footer-card">
            <div class="footer-pill">
                <svg class="clock-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" aria-hidden="true">
                    <path d="M12 1.75A10.25 10.25 0 1 0 22.25 12 10.262 10.262 0 0 0 12 1.75Zm0 18.5A8.25 8.25 0 1 1 20.25 12 8.26 8.26 0 0 1 12 20.25Zm.75-13.5h-1.5v5.25L16 14.19l.75-1.23-4-2.41Z" fill="#1C7ED6" />
                </svg>
                <span class="footer-pill-text">Usually takes 1–2 business days</span>
            </div>
            <p class="footer-text" style="color:black">
                We'll notify you as soon as your school is added to our platform.
            </p>
        </div>
    </div>

</div>

<style>
    :root {
        --primary: #3B82F6;          /* refined blue (screenshot-ish) */
        --primary-hover: #1C7ED6;
        --success: #27C46D;          /* green badge */
        --input-border: #E5E7EB;
        --input-border-focus: var(--primary);
        --input-bg: #FFFFFF;
        --input-bg-focus: #FFFFFF;
        --text-main: #000000;
        --text-secondary: #666666;
        --text-label: #333333;
        --text-placeholder: #AAAAAA;
        --radius-input: 12px;
        --radius-pill: 16px;
        --page-width: 375px;
    }

    .add-school-container {
        display: flex;
        flex-direction: column;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: #FFFFFF;
        min-height: 100vh;
        width: var(--page-width);
        margin: 0 auto;
        position: relative;
    }

    .header-section {
        padding: 16px 20px 0;
        margin-top: 16px; /* tighter, closer to screenshot */
        display: flex;
        align-items: center;
    }

    .back-button {
        background: none;
        border: none;
        padding: 8px;
        cursor: pointer;
        border-radius: 8px;
        line-height: 0;
    }

    .back-button:hover {
        background-color: #f5f5f5;
    }

    .illustration-section {
        display: flex;
        justify-content: center;
        margin: 16px 0 32px;
        padding: 0 20px;
    }

    /* Outer pastel halo box */
   .school-illustration {
    --illus-size: 160px;
    position: relative;
    width: var(--illus-size);
    height: var(--illus-size);
    border-radius: 24px;
    background: white; 
    padding: 0;       /* Remove extra padding */
    box-shadow: none; /* Remove shadow */
    display: block;
    align-items: center;
    justify-content: center;
}

    .image-wrapper {
        width: 100%;
        height: 100%;
        overflow: hidden;
        border-radius: 16px;
        display: ;
        align-items: center;
        justify-content: center;
    }

    .school-image {
        width: 100%;
        height: auto;
        object-fit: cover;
        border-radius: 16px;
    }


    /* Floating green badge offset slightly outside corner like screenshot */
    .location-badge {
        position: absolute;
        top: -10px;
        right: -10px;
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background: var(--success);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow:
            0 2px 4px rgba(0,0,0,0.08),
            0 0 0 2px rgba(255,255,255,0.9);
    }

    .content-section {
        padding: 0 24px;
        margin-bottom: 32px;
        text-align: center;
    }

    .main-title {
        font-size: 22px;
        font-weight: 800;
        color: var(--text-main);
        line-height: 1.3;
        margin: 0 0 16px;
    }

    .description {
        font-size: 15px;
        color: var(--text-secondary);
        line-height: 1.5;
        margin: 0;
    }

    .form-section {
        padding: 0 24px;
        display: flex;
        flex-direction: column;
        gap: 20px;
        margin-bottom: 32px;
    }

    .input-group {
        display: flex;
        flex-direction: column;
    }

    .input-label {
        font-size: 14px;
        font-weight: 600;
        color: var(--text-label);
        margin-bottom: 8px;
    }

    .optional {
        font-weight: 400;
        color: #999999;
    }

    .form-input {
        width: 100%;
        height: 52px;
        padding: 12px 16px;
        border: 1px solid var(--input-border);
        border-radius: var(--radius-input);
        font-size: 16px;
        color: var(--text-label);
        background-color: var(--input-bg);
        box-sizing: border-box;
        transition: border-color 0.15s ease, box-shadow 0.15s ease;
    }

    .form-input:focus {
        outline: none;
        border-color: var(--input-border-focus);
        box-shadow: 0 0 0 3px rgba(59,130,246,0.25);
        background-color: var(--input-bg-focus);
    }

    .form-input::placeholder {
        color: var(--text-placeholder);
    }

    .button-section {
        padding: 0 24px;
        margin-bottom: 32px;
    }

    .request-button {
        width: 100%;
        height: 52px;
        background-color: var(--primary);
        color: white;
        border: none;
        border-radius: 16px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 3px 8px rgba(59,130,246,0.35);
        transition: background-color 0.15s ease, box-shadow 0.15s ease;
    }

    

    .footer-section {
        padding: 0 24px 48px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
        margin-top: auto;
        text-align: center;
    }

    /* Pill styled like screenshot callout */
    .footer-card {
        background: #E0F3FF;
        padding: 16px;
        border-radius: 20px;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
    }

    .footer-pill {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 600;
        color: #1C7ED6;
        background: transparent;
    }

    .clock-icon {
        display: inline-block;
    }

    .footer-text {
        font-size: 14px;
        color: #1C7ED6;
        line-height: 1.4;
        margin: 0;
        max-width: 280px;
        text-align: center;
    }
</style>

@code {
    private string SchoolName = string.Empty;
    private string City = string.Empty;
    private string StudentEmail = string.Empty;
    private string ContactPerson = string.Empty;

    private bool IsSubmitDisabled =>
        string.IsNullOrWhiteSpace(SchoolName) ||
        string.IsNullOrWhiteSpace(City) ||
        string.IsNullOrWhiteSpace(StudentEmail);

    private void GoBack()
    {
        Navigation.NavigateTo("/login");
    }

    private void RequestSchoolAccess()
    {
        if (IsSubmitDisabled)
            return;

        // TODO: submit to backend
        Navigation.NavigateTo("/main");
    }
}
