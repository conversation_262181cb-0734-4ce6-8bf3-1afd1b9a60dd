{"Version": 1, "Hash": "zh3aJCifFedvBQ0uQHxxFPhlpZcKmL66WuxgqQz06xA=", "Source": "GateSale", "BasePath": "/", "Mode": "Root", "ManifestType": "Publish", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "GateSale\\wwwroot", "Source": "GateSale", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "Pattern": "**"}], "Assets": [{"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\0qse1rb7ey-onmyqsxya3.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\", "BasePath": "/", "RelativePath": "css/tailwind#[.{fingerprint=onmyqsxya3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sl35a95axq", "Integrity": "gIApGFSMjV7e0ktFpMgV49KaXV1XPi1hbYZndhnOSIw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint=6gzpyzhau4}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "joqzyw7ssu", "Integrity": "LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\", "BasePath": "/", "RelativePath": "css/input#[.{fingerprint=4jtf2pvl4b}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lwxvo5jupf", "Integrity": "ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\okv3f23xy4-e5tk7yf482.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint=e5tk7yf482}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9sb0w9jnhl", "Integrity": "qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\0qse1rb7ey-onmyqsxya3.br", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\", "BasePath": "/", "RelativePath": "css/tailwind#[.{fingerprint=onmyqsxya3}]?.css.br", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "br", "Fingerprint": "0wzs34tfpy", "Integrity": "UFg418IGrv/KwC3toXdChBJ60LQaKz87RvgZgEP5+C4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\c4yfl1a1cx-6gzpyzhau4.br", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint=6gzpyzhau4}]?.css.br", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "br", "Fingerprint": "2o883p7o84", "Integrity": "4jR0OWxmfzUr920AK8Yzn3GjSgWqtt7AtQDXrJ9OWmA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\l2n983ggca-4jtf2pvl4b.br", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\", "BasePath": "/", "RelativePath": "css/input#[.{fingerprint=4jtf2pvl4b}]?.css.br", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "br", "Fingerprint": "o5j5gkiy5u", "Integrity": "xI9MSXDnobj0u7fIoymKbCgpgoCDe8qcBP++ID5L/P0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\okv3f23xy4-e5tk7yf482.br", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint=e5tk7yf482}]?.css.br", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "br", "Fingerprint": "ix9166x7gh", "Integrity": "eqlsvw/N8zKnAKc05YDjHT7YIDSUfr5wmQ20CD/lmXo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\uv3ua5xd0n-55aay04al6.br", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint=55aay04al6}]?.html.br", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "br", "Fingerprint": "kfjl8mnaqz", "Integrity": "oGX1qxDFLGQek32k19UeG2LtrtRQB7n2Rpc9vvuhkWs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\index.html"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\x0awlxkggx-8inm30yfxf.br", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint=8inm30yfxf}]?.map.br", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "br", "Fingerprint": "b4a1wrn6r6", "Integrity": "e0W3cltN7F1+gW+ilWd1J4N1PyvYvfyn8Js6/fJEqb0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\uv3ua5xd0n-55aay04al6.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint=55aay04al6}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sciskma03l", "Integrity": "7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\index.html"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\x0awlxkggx-8inm30yfxf.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint=8inm30yfxf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fbl5ivwo6k", "Integrity": "Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "e5tk7yf482", "Integrity": "bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6gzpyzhau4", "Integrity": "SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css.map"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/input#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4jtf2pvl4b", "Integrity": "QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\input.css"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/tailwind#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "onmyqsxya3", "Integrity": "NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\tailwind.css"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\images\\bagpack.jpg", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "images/bagpack#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k0z6ov860a", "Integrity": "xOoLewrcV24WWrZig0WoK6pOM/8ibw0qDYlAxUz/8UI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\bagpack.jpg"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\images\\book.jpg", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "images/book#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "59aobo96p5", "Integrity": "F1uLNJm3kGCEmNH8p8blXl4SOGkgja83lBRE9Lz0qfw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\book.jpg"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\images\\gatesale-logo.png", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "images/gatesale-logo#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2ndi59cmif", "Integrity": "jj6JMxc5paIu5Hijgah8/q3lGtPZDfS6U//EC75tSK4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\gatesale-logo.png"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\images\\onboarding-1.jpg", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "images/onboarding-1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rw05rgbojm", "Integrity": "234oSg737ZpiLcPlvxkrDR/NPxTPadosJYscwqMks0U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\onboarding-1.jpg"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\images\\parental.jpg", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "images/parental#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "yybck5txrc", "Integrity": "mp+E7uF1kcokT6UusbZH66mccFYN0n56q1WBn8aCvFA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\parental.jpg"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\images\\phone.jpg", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "images/phone#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "itqnu1e2xx", "Integrity": "hsmAzWzQ4/UmXqCo3jL1dqgoHy8bu4BUKQlAGOXGcTg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\phone.jpg"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\images\\school.jpg", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "images/school#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "f7vz61kynb", "Integrity": "u7KCsrGks9t8ZHrP62EGsvjyOidEqMaQ/S0/Cf5qlNw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\school.jpg"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\index.html", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "55aay04al6", "Integrity": "2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html"}], "Endpoints": [{"Route": "css/app.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\okv3f23xy4-e5tk7yf482.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000571102227"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1750"}, {"Name": "ETag", "Value": "\"qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\okv3f23xy4-e5tk7yf482.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.000648508431"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1541"}, {"Name": "ETag", "Value": "\"eqlsvw/N8zKnAKc05YDjHT7YIDSUfr5wmQ20CD/lmXo=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3376"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.css.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\okv3f23xy4-e5tk7yf482.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1541"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"eqlsvw/N8zKnAKc05YDjHT7YIDSUfr5wmQ20CD/lmXo=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eqlsvw/N8zKnAKc05YDjHT7YIDSUfr5wmQ20CD/lmXo="}]}, {"Route": "css/app.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\okv3f23xy4-e5tk7yf482.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1750"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw="}]}, {"Route": "css/app.e5tk7yf482.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\okv3f23xy4-e5tk7yf482.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000571102227"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1750"}, {"Name": "ETag", "Value": "\"qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "label", "Value": "css/app.css"}, {"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.e5tk7yf482.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\okv3f23xy4-e5tk7yf482.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.000648508431"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1541"}, {"Name": "ETag", "Value": "\"eqlsvw/N8zKnAKc05YDjHT7YIDSUfr5wmQ20CD/lmXo=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "label", "Value": "css/app.css"}, {"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.e5tk7yf482.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3376"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "label", "Value": "css/app.css"}, {"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.e5tk7yf482.css.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\okv3f23xy4-e5tk7yf482.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1541"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"eqlsvw/N8zKnAKc05YDjHT7YIDSUfr5wmQ20CD/lmXo=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "label", "Value": "css/app.css.br"}, {"Name": "integrity", "Value": "sha256-eqlsvw/N8zKnAKc05YDjHT7YIDSUfr5wmQ20CD/lmXo="}]}, {"Route": "css/app.e5tk7yf482.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\okv3f23xy4-e5tk7yf482.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1750"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "label", "Value": "css/app.css.gz"}, {"Name": "integrity", "Value": "sha256-qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw="}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041844506"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\c4yfl1a1cx-6gzpyzhau4.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.000057250816"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17466"}, {"Name": "ETag", "Value": "\"4jR0OWxmfzUr920AK8Yzn3GjSgWqtt7AtQDXrJ9OWmA=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162726"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\c4yfl1a1cx-6gzpyzhau4.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17466"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4jR0OWxmfzUr920AK8Yzn3GjSgWqtt7AtQDXrJ9OWmA=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.br"}, {"Name": "integrity", "Value": "sha256-4jR0OWxmfzUr920AK8Yzn3GjSgWqtt7AtQDXrJ9OWmA="}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.gz"}, {"Name": "integrity", "Value": "sha256-LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c="}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041844506"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\c4yfl1a1cx-6gzpyzhau4.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.000057250816"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17466"}, {"Name": "ETag", "Value": "\"4jR0OWxmfzUr920AK8Yzn3GjSgWqtt7AtQDXrJ9OWmA=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162726"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\x0awlxkggx-8inm30yfxf.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.000017661604"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56619"}, {"Name": "ETag", "Value": "\"e0W3cltN7F1+gW+ilWd1J4N1PyvYvfyn8Js6/fJEqb0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\x0awlxkggx-8inm30yfxf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013350065"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\x0awlxkggx-8inm30yfxf.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56619"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"e0W3cltN7F1+gW+ilWd1J4N1PyvYvfyn8Js6/fJEqb0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map.br"}, {"Name": "integrity", "Value": "sha256-e0W3cltN7F1+gW+ilWd1J4N1PyvYvfyn8Js6/fJEqb0="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\x0awlxkggx-8inm30yfxf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU="}]}, {"Route": "css/bootstrap/bootstrap.min.css.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\c4yfl1a1cx-6gzpyzhau4.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17466"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4jR0OWxmfzUr920AK8Yzn3GjSgWqtt7AtQDXrJ9OWmA=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4jR0OWxmfzUr920AK8Yzn3GjSgWqtt7AtQDXrJ9OWmA="}]}, {"Route": "css/bootstrap/bootstrap.min.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\x0awlxkggx-8inm30yfxf.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.000017661604"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56619"}, {"Name": "ETag", "Value": "\"e0W3cltN7F1+gW+ilWd1J4N1PyvYvfyn8Js6/fJEqb0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\x0awlxkggx-8inm30yfxf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013350065"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\x0awlxkggx-8inm30yfxf.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56619"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"e0W3cltN7F1+gW+ilWd1J4N1PyvYvfyn8Js6/fJEqb0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-e0W3cltN7F1+gW+ilWd1J4N1PyvYvfyn8Js6/fJEqb0="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\x0awlxkggx-8inm30yfxf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU="}]}, {"Route": "css/input.4jtf2pvl4b.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003891050584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "ETag", "Value": "\"ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "label", "Value": "css/input.css"}, {"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.4jtf2pvl4b.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\l2n983ggca-4jtf2pvl4b.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.005154639175"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "193"}, {"Name": "ETag", "Value": "\"xI9MSXDnobj0u7fIoymKbCgpgoCDe8qcBP++ID5L/P0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "label", "Value": "css/input.css"}, {"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.4jtf2pvl4b.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "476"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "label", "Value": "css/input.css"}, {"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.4jtf2pvl4b.css.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\l2n983ggca-4jtf2pvl4b.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "193"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xI9MSXDnobj0u7fIoymKbCgpgoCDe8qcBP++ID5L/P0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "label", "Value": "css/input.css.br"}, {"Name": "integrity", "Value": "sha256-xI9MSXDnobj0u7fIoymKbCgpgoCDe8qcBP++ID5L/P0="}]}, {"Route": "css/input.4jtf2pvl4b.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "label", "Value": "css/input.css.gz"}, {"Name": "integrity", "Value": "sha256-ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU="}]}, {"Route": "css/input.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003891050584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "ETag", "Value": "\"ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\l2n983ggca-4jtf2pvl4b.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.005154639175"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "193"}, {"Name": "ETag", "Value": "\"xI9MSXDnobj0u7fIoymKbCgpgoCDe8qcBP++ID5L/P0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "476"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.css.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\l2n983ggca-4jtf2pvl4b.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "193"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xI9MSXDnobj0u7fIoymKbCgpgoCDe8qcBP++ID5L/P0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xI9MSXDnobj0u7fIoymKbCgpgoCDe8qcBP++ID5L/P0="}]}, {"Route": "css/input.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU="}]}, {"Route": "css/tailwind.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\0qse1rb7ey-onmyqsxya3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000191607588"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5218"}, {"Name": "ETag", "Value": "\"gIApGFSMjV7e0ktFpMgV49KaXV1XPi1hbYZndhnOSIw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\0qse1rb7ey-onmyqsxya3.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.000226500566"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4414"}, {"Name": "ETag", "Value": "\"UFg418IGrv/KwC3toXdChBJ60LQaKz87RvgZgEP5+C4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23768"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.css.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\0qse1rb7ey-onmyqsxya3.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4414"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UFg418IGrv/KwC3toXdChBJ60LQaKz87RvgZgEP5+C4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UFg418IGrv/KwC3toXdChBJ60LQaKz87RvgZgEP5+C4="}]}, {"Route": "css/tailwind.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\0qse1rb7ey-onmyqsxya3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5218"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gIApGFSMjV7e0ktFpMgV49KaXV1XPi1hbYZndhnOSIw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gIApGFSMjV7e0ktFpMgV49KaXV1XPi1hbYZndhnOSIw="}]}, {"Route": "css/tailwind.onmyqsxya3.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\0qse1rb7ey-onmyqsxya3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000191607588"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5218"}, {"Name": "ETag", "Value": "\"gIApGFSMjV7e0ktFpMgV49KaXV1XPi1hbYZndhnOSIw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "onmyqsxya3"}, {"Name": "label", "Value": "css/tailwind.css"}, {"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.onmyqsxya3.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\0qse1rb7ey-onmyqsxya3.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.000226500566"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4414"}, {"Name": "ETag", "Value": "\"UFg418IGrv/KwC3toXdChBJ60LQaKz87RvgZgEP5+C4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "onmyqsxya3"}, {"Name": "label", "Value": "css/tailwind.css"}, {"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.onmyqsxya3.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23768"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "onmyqsxya3"}, {"Name": "label", "Value": "css/tailwind.css"}, {"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.onmyqsxya3.css.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\0qse1rb7ey-onmyqsxya3.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4414"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UFg418IGrv/KwC3toXdChBJ60LQaKz87RvgZgEP5+C4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "onmyqsxya3"}, {"Name": "label", "Value": "css/tailwind.css.br"}, {"Name": "integrity", "Value": "sha256-UFg418IGrv/KwC3toXdChBJ60LQaKz87RvgZgEP5+C4="}]}, {"Route": "css/tailwind.onmyqsxya3.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\0qse1rb7ey-onmyqsxya3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5218"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gIApGFSMjV7e0ktFpMgV49KaXV1XPi1hbYZndhnOSIw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "onmyqsxya3"}, {"Name": "label", "Value": "css/tailwind.css.gz"}, {"Name": "integrity", "Value": "sha256-gIApGFSMjV7e0ktFpMgV49KaXV1XPi1hbYZndhnOSIw="}]}, {"Route": "images/bagpack.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\bagpack.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9109"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"xOoLewrcV24WWrZig0WoK6pOM/8ibw0qDYlAxUz/8UI=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xOoLewrcV24WWrZig0WoK6pOM/8ibw0qDYlAxUz/8UI="}]}, {"Route": "images/bagpack.k0z6ov860a.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\bagpack.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9109"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"xOoLewrcV24WWrZig0WoK6pOM/8ibw0qDYlAxUz/8UI=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k0z6ov860a"}, {"Name": "label", "Value": "images/bagpack.jpg"}, {"Name": "integrity", "Value": "sha256-xOoLewrcV24WWrZig0WoK6pOM/8ibw0qDYlAxUz/8UI="}]}, {"Route": "images/book.59aobo96p5.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\book.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"F1uLNJm3kGCEmNH8p8blXl4SOGkgja83lBRE9Lz0qfw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "59aobo96p5"}, {"Name": "label", "Value": "images/book.jpg"}, {"Name": "integrity", "Value": "sha256-F1uLNJm3kGCEmNH8p8blXl4SOGkgja83lBRE9Lz0qfw="}]}, {"Route": "images/book.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\book.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"F1uLNJm3kGCEmNH8p8blXl4SOGkgja83lBRE9Lz0qfw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-F1uLNJm3kGCEmNH8p8blXl4SOGkgja83lBRE9Lz0qfw="}]}, {"Route": "images/gatesale-logo.2ndi59cmif.png", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\gatesale-logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63944"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"jj6JMxc5paIu5Hijgah8/q3lGtPZDfS6U//EC75tSK4=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 05:12:59 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2ndi59cmif"}, {"Name": "label", "Value": "images/gatesale-logo.png"}, {"Name": "integrity", "Value": "sha256-jj6JMxc5paIu5Hijgah8/q3lGtPZDfS6U//EC75tSK4="}]}, {"Route": "images/gatesale-logo.png", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\gatesale-logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63944"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"jj6JMxc5paIu5Hijgah8/q3lGtPZDfS6U//EC75tSK4=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 05:12:59 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jj6JMxc5paIu5Hijgah8/q3lGtPZDfS6U//EC75tSK4="}]}, {"Route": "images/onboarding-1.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\onboarding-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "41637"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"234oSg737ZpiLcPlvxkrDR/NPxTPadosJYscwqMks0U=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-234oSg737ZpiLcPlvxkrDR/NPxTPadosJYscwqMks0U="}]}, {"Route": "images/onboarding-1.rw05rgbojm.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\onboarding-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "41637"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"234oSg737ZpiLcPlvxkrDR/NPxTPadosJYscwqMks0U=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rw05rgbojm"}, {"Name": "label", "Value": "images/onboarding-1.jpg"}, {"Name": "integrity", "Value": "sha256-234oSg737ZpiLcPlvxkrDR/NPxTPadosJYscwqMks0U="}]}, {"Route": "images/parental.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\parental.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24367"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"mp+E7uF1kcokT6UusbZH66mccFYN0n56q1WBn8aCvFA=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mp+E7uF1kcokT6UusbZH66mccFYN0n56q1WBn8aCvFA="}]}, {"Route": "images/parental.yybck5txrc.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\parental.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24367"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"mp+E7uF1kcokT6UusbZH66mccFYN0n56q1WBn8aCvFA=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yybck5txrc"}, {"Name": "label", "Value": "images/parental.jpg"}, {"Name": "integrity", "Value": "sha256-mp+E7uF1kcokT6UusbZH66mccFYN0n56q1WBn8aCvFA="}]}, {"Route": "images/phone.itqnu1e2xx.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\phone.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10423"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"hsmAzWzQ4/UmXqCo3jL1dqgoHy8bu4BUKQlAGOXGcTg=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "itqnu1e2xx"}, {"Name": "label", "Value": "images/phone.jpg"}, {"Name": "integrity", "Value": "sha256-hsmAzWzQ4/UmXqCo3jL1dqgoHy8bu4BUKQlAGOXGcTg="}]}, {"Route": "images/phone.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\phone.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10423"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"hsmAzWzQ4/UmXqCo3jL1dqgoHy8bu4BUKQlAGOXGcTg=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hsmAzWzQ4/UmXqCo3jL1dqgoHy8bu4BUKQlAGOXGcTg="}]}, {"Route": "images/school.f7vz61kynb.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\school.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "36526"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"u7KCsrGks9t8ZHrP62EGsvjyOidEqMaQ/S0/Cf5qlNw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 15:05:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f7vz61kynb"}, {"Name": "label", "Value": "images/school.jpg"}, {"Name": "integrity", "Value": "sha256-u7KCsrGks9t8ZHrP62EGsvjyOidEqMaQ/S0/Cf5qlNw="}]}, {"Route": "images/school.jpg", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\school.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "36526"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"u7KCsrGks9t8ZHrP62EGsvjyOidEqMaQ/S0/Cf5qlNw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 15:05:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u7KCsrGks9t8ZHrP62EGsvjyOidEqMaQ/S0/Cf5qlNw="}]}, {"Route": "index.55aay04al6.html", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\uv3ua5xd0n-55aay04al6.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.003246753247"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "307"}, {"Name": "ETag", "Value": "\"oGX1qxDFLGQek32k19UeG2LtrtRQB7n2Rpc9vvuhkWs=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.55aay04al6.html", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\uv3ua5xd0n-55aay04al6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002314814815"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "431"}, {"Name": "ETag", "Value": "\"7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.55aay04al6.html", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.55aay04al6.html.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\uv3ua5xd0n-55aay04al6.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "307"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"oGX1qxDFLGQek32k19UeG2LtrtRQB7n2Rpc9vvuhkWs=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "label", "Value": "index.html.br"}, {"Name": "integrity", "Value": "sha256-oGX1qxDFLGQek32k19UeG2LtrtRQB7n2Rpc9vvuhkWs="}]}, {"Route": "index.55aay04al6.html.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\uv3ua5xd0n-55aay04al6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "431"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0="}]}, {"Route": "index.html", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\uv3ua5xd0n-55aay04al6.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.003246753247"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "307"}, {"Name": "ETag", "Value": "\"oGX1qxDFLGQek32k19UeG2LtrtRQB7n2Rpc9vvuhkWs=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.html", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\uv3ua5xd0n-55aay04al6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002314814815"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "431"}, {"Name": "ETag", "Value": "\"7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.html", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.html.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\uv3ua5xd0n-55aay04al6.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "307"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"oGX1qxDFLGQek32k19UeG2LtrtRQB7n2Rpc9vvuhkWs=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oGX1qxDFLGQek32k19UeG2LtrtRQB7n2Rpc9vvuhkWs="}]}, {"Route": "index.html.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\uv3ua5xd0n-55aay04al6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "431"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0="}]}]}