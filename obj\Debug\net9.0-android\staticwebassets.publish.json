{"Version": 1, "Hash": "aEnXa4FPwk8qIakvqMKuOnWHszhvUqP7BvCx/sH7krA=", "Source": "GateSale", "BasePath": "/", "Mode": "Root", "ManifestType": "Publish", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "GateSale\\wwwroot", "Source": "GateSale", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "Pattern": "**"}], "Assets": [{"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\0qse1rb7ey-onmyqsxya3.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\", "BasePath": "/", "RelativePath": "css/tailwind#[.{fingerprint=onmyqsxya3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cj75ss3qkm", "Integrity": "aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint=6gzpyzhau4}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7l51xal3e9", "Integrity": "dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\", "BasePath": "/", "RelativePath": "css/input#[.{fingerprint=4jtf2pvl4b}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oh1840y9tw", "Integrity": "dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\okv3f23xy4-e5tk7yf482.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint=e5tk7yf482}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qfor6zdeny", "Integrity": "ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\0qse1rb7ey-onmyqsxya3.br", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\", "BasePath": "/", "RelativePath": "css/tailwind#[.{fingerprint=onmyqsxya3}]?.css.br", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "br", "Fingerprint": "0wzs34tfpy", "Integrity": "UFg418IGrv/KwC3toXdChBJ60LQaKz87RvgZgEP5+C4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\c4yfl1a1cx-6gzpyzhau4.br", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint=6gzpyzhau4}]?.css.br", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "br", "Fingerprint": "2o883p7o84", "Integrity": "4jR0OWxmfzUr920AK8Yzn3GjSgWqtt7AtQDXrJ9OWmA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\l2n983ggca-4jtf2pvl4b.br", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\", "BasePath": "/", "RelativePath": "css/input#[.{fingerprint=4jtf2pvl4b}]?.css.br", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "br", "Fingerprint": "o5j5gkiy5u", "Integrity": "xI9MSXDnobj0u7fIoymKbCgpgoCDe8qcBP++ID5L/P0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\okv3f23xy4-e5tk7yf482.br", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint=e5tk7yf482}]?.css.br", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "br", "Fingerprint": "ix9166x7gh", "Integrity": "eqlsvw/N8zKnAKc05YDjHT7YIDSUfr5wmQ20CD/lmXo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\uv3ua5xd0n-55aay04al6.br", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint=55aay04al6}]?.html.br", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "br", "Fingerprint": "kfjl8mnaqz", "Integrity": "oGX1qxDFLGQek32k19UeG2LtrtRQB7n2Rpc9vvuhkWs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\index.html"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\x0awlxkggx-8inm30yfxf.br", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint=8inm30yfxf}]?.map.br", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "br", "Fingerprint": "b4a1wrn6r6", "Integrity": "e0W3cltN7F1+gW+ilWd1J4N1PyvYvfyn8Js6/fJEqb0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\uv3ua5xd0n-55aay04al6.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint=55aay04al6}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t8inowlh2q", "Integrity": "HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\index.html"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\x0awlxkggx-8inm30yfxf.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint=8inm30yfxf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m532s4naxw", "Integrity": "epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "e5tk7yf482", "Integrity": "bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6gzpyzhau4", "Integrity": "SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css.map"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/input#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4jtf2pvl4b", "Integrity": "QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\input.css"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/tailwind#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "onmyqsxya3", "Integrity": "NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\tailwind.css"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\images\\gatesale-logo.png", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "images/gatesale-logo#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5vb0ielf3y", "Integrity": "72kAsODfe9r7PSZ2qJSR/ElSeWDOU7WI5lPEao3S7ks=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\gatesale-logo.png"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\images\\onboarding-1.png", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "images/onboarding-1#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5hljwozhwy", "Integrity": "eeO6PzBdkOZFtTE8xdC+5jvPlvtrXmemdzw0S7aCSZQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\onboarding-1.png"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\index.html", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "55aay04al6", "Integrity": "2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html"}], "Endpoints": [{"Route": "css/app.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\okv3f23xy4-e5tk7yf482.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000556173526"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1797"}, {"Name": "ETag", "Value": "\"ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\okv3f23xy4-e5tk7yf482.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.000648508431"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1541"}, {"Name": "ETag", "Value": "\"eqlsvw/N8zKnAKc05YDjHT7YIDSUfr5wmQ20CD/lmXo=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3376"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.css.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\okv3f23xy4-e5tk7yf482.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1541"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"eqlsvw/N8zKnAKc05YDjHT7YIDSUfr5wmQ20CD/lmXo=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eqlsvw/N8zKnAKc05YDjHT7YIDSUfr5wmQ20CD/lmXo="}]}, {"Route": "css/app.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\okv3f23xy4-e5tk7yf482.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1797"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A="}]}, {"Route": "css/app.e5tk7yf482.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\okv3f23xy4-e5tk7yf482.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000556173526"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1797"}, {"Name": "ETag", "Value": "\"ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "label", "Value": "css/app.css"}, {"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.e5tk7yf482.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\okv3f23xy4-e5tk7yf482.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.000648508431"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1541"}, {"Name": "ETag", "Value": "\"eqlsvw/N8zKnAKc05YDjHT7YIDSUfr5wmQ20CD/lmXo=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "label", "Value": "css/app.css"}, {"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.e5tk7yf482.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3376"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "label", "Value": "css/app.css"}, {"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.e5tk7yf482.css.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\okv3f23xy4-e5tk7yf482.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1541"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"eqlsvw/N8zKnAKc05YDjHT7YIDSUfr5wmQ20CD/lmXo=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "label", "Value": "css/app.css.br"}, {"Name": "integrity", "Value": "sha256-eqlsvw/N8zKnAKc05YDjHT7YIDSUfr5wmQ20CD/lmXo="}]}, {"Route": "css/app.e5tk7yf482.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\okv3f23xy4-e5tk7yf482.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1797"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "label", "Value": "css/app.css.gz"}, {"Name": "integrity", "Value": "sha256-ismDfJHkNHfoqxdRjvM7D0cvQOeIn31cbNUC6dB4m9A="}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041912905"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23858"}, {"Name": "ETag", "Value": "\"dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\c4yfl1a1cx-6gzpyzhau4.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.000057250816"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17466"}, {"Name": "ETag", "Value": "\"4jR0OWxmfzUr920AK8Yzn3GjSgWqtt7AtQDXrJ9OWmA=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162726"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\c4yfl1a1cx-6gzpyzhau4.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17466"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4jR0OWxmfzUr920AK8Yzn3GjSgWqtt7AtQDXrJ9OWmA=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.br"}, {"Name": "integrity", "Value": "sha256-4jR0OWxmfzUr920AK8Yzn3GjSgWqtt7AtQDXrJ9OWmA="}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23858"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.gz"}, {"Name": "integrity", "Value": "sha256-dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8="}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041912905"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23858"}, {"Name": "ETag", "Value": "\"dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\c4yfl1a1cx-6gzpyzhau4.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.000057250816"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17466"}, {"Name": "ETag", "Value": "\"4jR0OWxmfzUr920AK8Yzn3GjSgWqtt7AtQDXrJ9OWmA=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162726"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\x0awlxkggx-8inm30yfxf.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.000017661604"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56619"}, {"Name": "ETag", "Value": "\"e0W3cltN7F1+gW+ilWd1J4N1PyvYvfyn8Js6/fJEqb0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\x0awlxkggx-8inm30yfxf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013397104"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\x0awlxkggx-8inm30yfxf.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56619"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"e0W3cltN7F1+gW+ilWd1J4N1PyvYvfyn8Js6/fJEqb0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map.br"}, {"Name": "integrity", "Value": "sha256-e0W3cltN7F1+gW+ilWd1J4N1PyvYvfyn8Js6/fJEqb0="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\x0awlxkggx-8inm30yfxf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E="}]}, {"Route": "css/bootstrap/bootstrap.min.css.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\c4yfl1a1cx-6gzpyzhau4.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17466"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4jR0OWxmfzUr920AK8Yzn3GjSgWqtt7AtQDXrJ9OWmA=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4jR0OWxmfzUr920AK8Yzn3GjSgWqtt7AtQDXrJ9OWmA="}]}, {"Route": "css/bootstrap/bootstrap.min.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23858"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dfRdV3FWJNHUyN/ZTK8eRHslFtdOjQxpi1BEcWXtMR8="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\x0awlxkggx-8inm30yfxf.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.000017661604"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56619"}, {"Name": "ETag", "Value": "\"e0W3cltN7F1+gW+ilWd1J4N1PyvYvfyn8Js6/fJEqb0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\x0awlxkggx-8inm30yfxf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013397104"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\x0awlxkggx-8inm30yfxf.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56619"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"e0W3cltN7F1+gW+ilWd1J4N1PyvYvfyn8Js6/fJEqb0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-e0W3cltN7F1+gW+ilWd1J4N1PyvYvfyn8Js6/fJEqb0="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\x0awlxkggx-8inm30yfxf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E="}]}, {"Route": "css/input.4jtf2pvl4b.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003891050584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "ETag", "Value": "\"dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "label", "Value": "css/input.css"}, {"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.4jtf2pvl4b.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\l2n983ggca-4jtf2pvl4b.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.005154639175"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "193"}, {"Name": "ETag", "Value": "\"xI9MSXDnobj0u7fIoymKbCgpgoCDe8qcBP++ID5L/P0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "label", "Value": "css/input.css"}, {"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.4jtf2pvl4b.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "476"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "label", "Value": "css/input.css"}, {"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.4jtf2pvl4b.css.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\l2n983ggca-4jtf2pvl4b.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "193"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xI9MSXDnobj0u7fIoymKbCgpgoCDe8qcBP++ID5L/P0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "label", "Value": "css/input.css.br"}, {"Name": "integrity", "Value": "sha256-xI9MSXDnobj0u7fIoymKbCgpgoCDe8qcBP++ID5L/P0="}]}, {"Route": "css/input.4jtf2pvl4b.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "label", "Value": "css/input.css.gz"}, {"Name": "integrity", "Value": "sha256-dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs="}]}, {"Route": "css/input.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003891050584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "ETag", "Value": "\"dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\l2n983ggca-4jtf2pvl4b.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.005154639175"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "193"}, {"Name": "ETag", "Value": "\"xI9MSXDnobj0u7fIoymKbCgpgoCDe8qcBP++ID5L/P0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "476"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.css.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\l2n983ggca-4jtf2pvl4b.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "193"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xI9MSXDnobj0u7fIoymKbCgpgoCDe8qcBP++ID5L/P0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xI9MSXDnobj0u7fIoymKbCgpgoCDe8qcBP++ID5L/P0="}]}, {"Route": "css/input.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dGKmUKHRpfSvC8bFN4dtNl2zYti+ZyETxn0OHLzjBhs="}]}, {"Route": "css/tailwind.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\0qse1rb7ey-onmyqsxya3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000189286390"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5282"}, {"Name": "ETag", "Value": "\"aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\0qse1rb7ey-onmyqsxya3.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.000226500566"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4414"}, {"Name": "ETag", "Value": "\"UFg418IGrv/KwC3toXdChBJ60LQaKz87RvgZgEP5+C4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23768"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.css.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\0qse1rb7ey-onmyqsxya3.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4414"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UFg418IGrv/KwC3toXdChBJ60LQaKz87RvgZgEP5+C4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UFg418IGrv/KwC3toXdChBJ60LQaKz87RvgZgEP5+C4="}]}, {"Route": "css/tailwind.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\0qse1rb7ey-onmyqsxya3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5282"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c="}]}, {"Route": "css/tailwind.onmyqsxya3.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\0qse1rb7ey-onmyqsxya3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000189286390"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5282"}, {"Name": "ETag", "Value": "\"aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "onmyqsxya3"}, {"Name": "label", "Value": "css/tailwind.css"}, {"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.onmyqsxya3.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\0qse1rb7ey-onmyqsxya3.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.000226500566"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4414"}, {"Name": "ETag", "Value": "\"UFg418IGrv/KwC3toXdChBJ60LQaKz87RvgZgEP5+C4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "onmyqsxya3"}, {"Name": "label", "Value": "css/tailwind.css"}, {"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.onmyqsxya3.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23768"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "onmyqsxya3"}, {"Name": "label", "Value": "css/tailwind.css"}, {"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.onmyqsxya3.css.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\0qse1rb7ey-onmyqsxya3.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4414"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UFg418IGrv/KwC3toXdChBJ60LQaKz87RvgZgEP5+C4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "onmyqsxya3"}, {"Name": "label", "Value": "css/tailwind.css.br"}, {"Name": "integrity", "Value": "sha256-UFg418IGrv/KwC3toXdChBJ60LQaKz87RvgZgEP5+C4="}]}, {"Route": "css/tailwind.onmyqsxya3.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\0qse1rb7ey-onmyqsxya3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5282"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "onmyqsxya3"}, {"Name": "label", "Value": "css/tailwind.css.gz"}, {"Name": "integrity", "Value": "sha256-aPFGnDDhbuMiRjNIMXYuiZyJtoN5BmOTaQDZN/0or2c="}]}, {"Route": "images/gatesale-logo.5vb0ielf3y.png", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\gatesale-logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12173"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"72kAsODfe9r7PSZ2qJSR/ElSeWDOU7WI5lPEao3S7ks=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:23:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5vb0ielf3y"}, {"Name": "label", "Value": "images/gatesale-logo.png"}, {"Name": "integrity", "Value": "sha256-72kAsODfe9r7PSZ2qJSR/ElSeWDOU7WI5lPEao3S7ks="}]}, {"Route": "images/gatesale-logo.png", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\gatesale-logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12173"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"72kAsODfe9r7PSZ2qJSR/ElSeWDOU7WI5lPEao3S7ks=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:23:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-72kAsODfe9r7PSZ2qJSR/ElSeWDOU7WI5lPEao3S7ks="}]}, {"Route": "images/onboarding-1.5hljwozhwy.png", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\onboarding-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55950"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"eeO6PzBdkOZFtTE8xdC+5jvPlvtrXmemdzw0S7aCSZQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:09:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5hljwozhwy"}, {"Name": "label", "Value": "images/onboarding-1.png"}, {"Name": "integrity", "Value": "sha256-eeO6PzBdkOZFtTE8xdC+5jvPlvtrXmemdzw0S7aCSZQ="}]}, {"Route": "images/onboarding-1.png", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\images\\onboarding-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55950"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"eeO6PzBdkOZFtTE8xdC+5jvPlvtrXmemdzw0S7aCSZQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:09:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eeO6PzBdkOZFtTE8xdC+5jvPlvtrXmemdzw0S7aCSZQ="}]}, {"Route": "index.55aay04al6.html", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\uv3ua5xd0n-55aay04al6.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.003246753247"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "307"}, {"Name": "ETag", "Value": "\"oGX1qxDFLGQek32k19UeG2LtrtRQB7n2Rpc9vvuhkWs=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.55aay04al6.html", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\uv3ua5xd0n-55aay04al6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002309468822"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "432"}, {"Name": "ETag", "Value": "\"HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.55aay04al6.html", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.55aay04al6.html.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\uv3ua5xd0n-55aay04al6.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "307"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"oGX1qxDFLGQek32k19UeG2LtrtRQB7n2Rpc9vvuhkWs=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "label", "Value": "index.html.br"}, {"Name": "integrity", "Value": "sha256-oGX1qxDFLGQek32k19UeG2LtrtRQB7n2Rpc9vvuhkWs="}]}, {"Route": "index.55aay04al6.html.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\uv3ua5xd0n-55aay04al6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "432"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4="}]}, {"Route": "index.html", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\uv3ua5xd0n-55aay04al6.br", "Selectors": [{"Name": "Content-Encoding", "Value": "br", "Quality": "0.003246753247"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "307"}, {"Name": "ETag", "Value": "\"oGX1qxDFLGQek32k19UeG2LtrtRQB7n2Rpc9vvuhkWs=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.html", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\uv3ua5xd0n-55aay04al6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002309468822"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "432"}, {"Name": "ETag", "Value": "\"HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.html", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.html.br", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\publish\\uv3ua5xd0n-55aay04al6.br", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "307"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"oGX1qxDFLGQek32k19UeG2LtrtRQB7n2Rpc9vvuhkWs=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "br"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oGX1qxDFLGQek32k19UeG2LtrtRQB7n2Rpc9vvuhkWs="}]}, {"Route": "index.html.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-android\\compressed\\uv3ua5xd0n-55aay04al6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "432"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HeR0Ziy3WiFlUjJYbOdLXliBz2SmKnW45YeVtvbGTT4="}]}]}