@page "/what-are-you-selling"
@inject NavigationManager Navigation

<div class="selling-container">
    <!-- Title -->
    <h1 class="main-title">What are you selling?</h1>

    <!-- Category Grid -->
    <div class="category-grid">
        <!-- Books -->
        <div class="category-card books" @onclick="SelectBooks">
            <div class="category-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                    <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V5H19V19Z" fill="white"/>
                    <path d="M7 7H17V9H7V7ZM7 11H17V13H7V11ZM7 15H14V17H7V15Z" fill="white"/>
                </svg>
            </div>
            <span class="category-label">Books</span>
        </div>

        <!-- Electronics -->
        <div class="category-card electronics" @onclick="SelectElectronics">
            <div class="category-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                    <path d="M12 1C10.34 1 9 2.34 9 4C9 5.66 10.34 7 12 7C13.66 7 15 5.66 15 4C15 2.34 13.66 1 12 1ZM21 9H20L15 14L13 12L11 14L9 12L7 14L2 9H1V11H2L7 16L9 14L11 16L13 14L15 16L20 11H21V9Z" fill="white"/>
                    <circle cx="6" cy="19" r="2" fill="white"/>
                    <circle cx="18" cy="19" r="2" fill="white"/>
                </svg>
            </div>
            <span class="category-label">Electronics</span>
        </div>

        <!-- Clothing -->
        <div class="category-card clothing" @onclick="SelectClothing">
            <div class="category-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                    <path d="M16 4L14.5 2.5C14.1 2.1 13.6 1.9 13 1.9H11C10.4 1.9 9.9 2.1 9.5 2.5L8 4H6C4.9 4 4 4.9 4 6V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V6C20 4.9 19.1 4 18 4H16ZM18 20H6V6H8.5L10.5 4H13.5L15.5 6H18V20Z" fill="white"/>
                    <path d="M8 8V12H16V8H8Z" fill="white"/>
                </svg>
            </div>
            <span class="category-label">Clothing</span>
        </div>

        <!-- Other -->
        <div class="category-card other" @onclick="SelectOther">
            <div class="category-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                    <path d="M20 8H17.19L15.19 4.65C14.96 4.26 14.53 4 14.07 4H9.93C9.47 4 9.04 4.26 8.81 4.65L6.81 8H4C2.9 8 2 8.9 2 10V19C2 20.1 2.9 21 4 21H20C21.1 21 22 20.1 22 19V10C22 8.9 21.1 8 20 8ZM9.93 6H14.07L15.38 8H8.62L9.93 6ZM20 19H4V10H20V19Z" fill="white"/>
                </svg>
            </div>
            <span class="category-label">Other</span>
        </div>
    </div>

    <!-- Next Button -->
    <button class="next-button" @onclick="GoNext">Next</button>
</div>

<style>
    .selling-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: clamp(20px, 5vw, 40px) clamp(16px, 4vw, 20px) 20px clamp(16px, 4vw, 20px);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        min-height: 100vh;
        justify-content: space-between;
        max-width: 400px;
        width: 100%;
        margin: 0 auto;
        box-sizing: border-box;
        background-color: #FFFFFF;
    }

    .main-title {
        font-size: clamp(20px, 5vw, 24px);
        font-weight: 600;
        color: #333333;
        text-align: center;
        margin: 0 0 clamp(24px, 6vw, 40px) 0;
        line-height: 1.2;
    }

    .category-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: clamp(12px, 3vw, 16px);
        width: 100%;
        max-width: 320px;
        margin-bottom: clamp(24px, 6vw, 40px);
    }

    .category-card {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 24px 16px;
        border-radius: 16px;
        cursor: pointer;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        min-height: 120px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .category-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .category-card:active {
        transform: translateY(0);
    }

    /* Category Colors */
    .category-card.books {
        background: linear-gradient(135deg, #42A5F5 0%, #1E88E5 100%);
    }

    .category-card.electronics {
        background: linear-gradient(135deg, #66BB6A 0%, #43A047 100%);
    }

    .category-card.clothing {
        background: linear-gradient(135deg, #FFB74D 0%, #FF9800 100%);
    }

    .category-card.other {
        background: linear-gradient(135deg, #EF5350 0%, #E53935 100%);
    }

    .category-icon {
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .category-label {
        color: white;
        font-size: 16px;
        font-weight: 600;
        text-align: center;
        line-height: 1.2;
    }

    .next-button {
        width: 100%;
        max-width: 320px;
        padding: 16px;
        background: linear-gradient(135deg, #81D4FA 0%, #4FC3F7 100%);
        color: white;
        border: none;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 2px 8px rgba(79, 195, 247, 0.3);
    }

    .next-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(79, 195, 247, 0.4);
    }

    .next-button:active {
        transform: translateY(0);
    }

    /* Mobile Responsiveness - Responsive styles applied via CSS classes */
</style>

@code {
    private string selectedCategory = "";

    private void SelectBooks()
    {
        selectedCategory = "Books";
        StateHasChanged();
    }

    private void SelectElectronics()
    {
        selectedCategory = "Electronics";
        StateHasChanged();
    }

    private void SelectClothing()
    {
        selectedCategory = "Clothing";
        StateHasChanged();
    }

    private void SelectOther()
    {
        selectedCategory = "Other";
        StateHasChanged();
    }

    private void GoNext()
    {
        if (!string.IsNullOrEmpty(selectedCategory))
        {
            // Navigate to next step with selected category
            Navigation.NavigateTo($"/selling/details?category={selectedCategory}");
        }
        else
        {
            // Navigate to next step anyway or show validation
            Navigation.NavigateTo("/selling/details");
        }
    }
}
