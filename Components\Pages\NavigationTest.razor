@page "/nav-test"
@inject NavigationManager Navigation

<div style="padding: 20px; font-family: Arial, sans-serif;">
    <h2>Navigation Test Page</h2>
    <p>Current URL: @Navigation.Uri</p>
    
    <div style="margin: 20px 0;">
        <h3>Test Navigation Links:</h3>
        <button @onclick="() => Navigation.NavigateTo('/')" style="margin: 5px; padding: 10px;">Go to Root (/)</button>
        <button @onclick="() => Navigation.NavigateTo('/login')" style="margin: 5px; padding: 10px;">Go to Login</button>
        <button @onclick="() => Navigation.NavigateTo('/signup')" style="margin: 5px; padding: 10px;">Go to Signup</button>
        <button @onclick="() => Navigation.NavigateTo('/welcome')" style="margin: 5px; padding: 10px;">Go to Welcome</button>
        <button @onclick="() => Navigation.NavigateTo('/welcome/true')" style="margin: 5px; padding: 10px;">Go to Welcome (with onboarding)</button>
        <button @onclick="() => Navigation.NavigateTo('/onboarding-slider')" style="margin: 5px; padding: 10px;">Go to Onboarding Slider</button>
    </div>
    
    <div style="margin: 20px 0;">
        <h3>Expected Flow:</h3>
        <ol>
            <li><strong>App Start (/):</strong> Should redirect to /login</li>
            <li><strong>Signup "Sign in" link:</strong> Should go to /login and stay there</li>
            <li><strong>Login "Sign up" link:</strong> Should go to /signup</li>
            <li><strong>Welcome without parameter:</strong> Should redirect to /login</li>
            <li><strong>Welcome with parameter:</strong> Should redirect to /onboarding-slider</li>
            <li><strong>Skip buttons:</strong> Should go to /login</li>
            <li><strong>Get Started:</strong> Should go to /signup</li>
        </ol>
    </div>
    
    <div style="margin: 20px 0;">
        <h3>Test the Sign-in Issue:</h3>
        <p>1. Go to Signup page</p>
        <p>2. Click "Already have an account? Sign in"</p>
        <p>3. Should stay on Login page (not redirect to onboarding)</p>
    </div>
</div>

@code {
    protected override void OnInitialized()
    {
        // This page should not redirect anywhere
    }
}
