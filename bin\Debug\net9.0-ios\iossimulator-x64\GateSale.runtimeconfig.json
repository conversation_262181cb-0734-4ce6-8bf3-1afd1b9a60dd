{"runtimeOptions": {"tfm": "net9.0", "includedFrameworks": [{"name": "Microsoft.NETCore.App", "version": "9.0.5"}, {"name": "Microsoft.iOS", "version": "18.4.9289"}], "configProperties": {"Microsoft.Extensions.DependencyInjection.VerifyOpenGenericServiceTrimmability": true, "System.AggressiveAttributeTrimming": true, "System.ComponentModel.TypeConverter.EnableUnsafeBinaryFormatterInDesigntimeLicenseContextSerialization": false, "System.ComponentModel.TypeDescriptor.IsComObjectDescriptorSupported": false, "System.Diagnostics.Debugger.IsSupported": true, "System.Diagnostics.Tracing.EventSource.IsSupported": false, "System.Globalization.Invariant": false, "System.Globalization.Hybrid": true, "System.Net.Http.UseNativeHttpHandler": true, "System.Reflection.NullabilityInfoContext.IsSupported": false, "System.Resources.ResourceManager.AllowCustomResourceTypes": false, "System.Resources.UseSystemResourceKeys": false, "System.Runtime.InteropServices.BuiltInComInterop.IsSupported": false, "System.Runtime.InteropServices.EnableConsumingManagedCodeFromNativeHosting": false, "System.Runtime.InteropServices.EnableCppCLIHostActivation": false, "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false, "System.StartupHookProvider.IsSupported": false, "System.Text.Encoding.EnableUnsafeUTF7Encoding": false, "System.Text.Json.JsonSerializer.IsReflectionEnabledByDefault": true, "System.Threading.Thread.EnableAutoreleasePool": true, "Microsoft.Maui.RuntimeFeature.IsIVisualAssemblyScanningEnabled": false, "Microsoft.Maui.RuntimeFeature.AreBindingInterceptorsSupported": true}}}