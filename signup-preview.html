<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Signup Page - Exact Replica</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            background: linear-gradient(90deg, #FFFFFF 0%, #F8FCFF 40%, #F0F8FF 70%, #E8F4FD 100%);
            border-radius: 25px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
            overflow: hidden;
            position: relative;
        }

        .signup-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif;
            background: linear-gradient(90deg, #FFFFFF 0%, #F8FCFF 40%, #F0F8FF 70%, #E8F4FD 100%);
            min-height: 100%;
            width: 100%;
            box-sizing: border-box;
            position: relative;
        }

        .header-section {
            text-align: center;
            margin-top: 40px;
            margin-bottom: 30px;
        }

        .main-title {
            font-size: 28px;
            font-weight: 700;
            color: #000000;
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .subtitle {
            font-size: 16px;
            color: #666666;
            margin: 0;
            line-height: 1.4;
        }

        .illustration-section {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
        }

        .illustration-circle {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #E0F2FE 0%, #BAE6FD 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
        }

        .logo-placeholder {
            font-size: 16px;
            font-weight: 800;
            text-align: center;
        }

        .form-section {
            width: 100%;
            max-width: 320px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .name-row {
            display: flex;
            gap: 12px;
        }

        .input-group {
            display: flex;
            flex-direction: column;
            flex: 1;
        }

        .input-label {
            font-size: 14px;
            font-weight: 600;
            color: #333333;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            height: 48px;
            padding: 12px 16px;
            border: 1px solid #E0E0E0;
            border-radius: 8px;
            font-size: 16px;
            color: #333333;
            background-color: #FAFAFA;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: #00BFFF;
            box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.1);
            background-color: white;
        }

        .form-input::placeholder {
            color: #AAAAAA;
        }

        .input-with-icon {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-icon {
            position: absolute;
            left: 16px;
            z-index: 2;
        }

        .input-icon-left {
            position: absolute;
            left: 16px;
            z-index: 2;
        }

        .with-icon {
            padding-left: 48px;
        }

        .with-icon-both {
            padding-left: 48px;
            padding-right: 48px;
        }

        .eye-button {
            position: absolute;
            right: 16px;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
        }

        .checkbox-group {
            margin: 8px 0;
        }

        .checkbox-label {
            display: flex;
            align-items: flex-start;
            cursor: pointer;
            font-size: 14px;
            line-height: 1.5;
            color: #374151;
        }

        .checkbox-custom {
            width: 18px;
            height: 18px;
            border: 2px solid #D1D5DB;
            border-radius: 4px;
            margin-right: 12px;
            margin-top: 2px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: white;
        }

        .checkbox-custom.checked {
            background-color: #3B82F6;
            border-color: #3B82F6;
        }

        .checkbox-custom.checked::after {
            content: '✓';
            color: white;
            font-size: 12px;
            font-weight: 600;
        }

        .terms-link {
            color: #3B82F6;
            text-decoration: none;
        }

        .terms-link:hover {
            text-decoration: underline;
        }

        .next-button {
            width: 100%;
            height: 50px;
            background-color: #00BFFF;
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 12px;
            box-shadow: 0 3px 8px rgba(0, 191, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .next-button:hover {
            background-color: #1C7ED6;
        }

        .signin-text {
            text-align: center;
            font-size: 14px;
            color: #6B7280;
            margin: 20px 0 0 0;
        }

        .signin-link {
            color: #00BFFF;
            text-decoration: none;
            font-weight: 600;
        }

        .signin-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="phone-frame">
        <div class="signup-container">
            <!-- Header -->
            <div class="header-section">
                <h1 class="main-title">Let's Get You Started</h1>
                <p class="subtitle">Create your student account in just a few steps</p>
            </div>

            <!-- Illustration -->
            <div class="illustration-section">
                <div class="illustration-circle">
                    <div class="backpack-logo">
                        <!-- Backpack SVG -->
                        <svg width="80" height="80" viewBox="0 0 100 100" fill="none">
                            <!-- Main backpack body -->
                            <rect x="30" y="35" width="40" height="50" rx="6" fill="#4A90E2"/>
                            <rect x="32" y="37" width="36" height="46" rx="4" fill="#357ABD"/>

                            <!-- Backpack straps -->
                            <rect x="25" y="25" width="6" height="30" rx="3" fill="#2E5A87"/>
                            <rect x="69" y="25" width="6" height="30" rx="3" fill="#2E5A87"/>

                            <!-- Front pocket -->
                            <rect x="35" y="45" width="30" height="18" rx="3" fill="#5BA0F2"/>
                            <rect x="37" y="47" width="26" height="14" rx="2" fill="#6BB6FF"/>

                            <!-- Backpack zipper -->
                            <rect x="48" y="35" width="4" height="8" rx="2" fill="#FFD700"/>

                            <!-- Left side books -->
                            <rect x="15" y="20" width="5" height="22" rx="1" fill="#FF6B6B"/>
                            <rect x="11" y="22" width="5" height="20" rx="1" fill="#4ECDC4"/>
                            <rect x="7" y="24" width="5" height="18" rx="1" fill="#45B7B8"/>

                            <!-- Right side books -->
                            <rect x="80" y="18" width="5" height="24" rx="1" fill="#FFE66D"/>
                            <rect x="84" y="20" width="5" height="22" rx="1" fill="#FF8A65"/>
                            <rect x="88" y="22" width="5" height="20" rx="1" fill="#FFA726"/>

                            <!-- Backpack handle -->
                            <rect x="45" y="25" width="10" height="4" rx="2" fill="#2E5A87"/>

                            <!-- Small details -->
                            <circle cx="40" cy="50" r="1.5" fill="#357ABD"/>
                            <circle cx="60" cy="50" r="1.5" fill="#357ABD"/>

                            <!-- Book spines details -->
                            <rect x="16" y="25" width="3" height="1" fill="#E74C3C"/>
                            <rect x="12" y="27" width="3" height="1" fill="#16A085"/>
                            <rect x="81" y="23" width="3" height="1" fill="#F39C12"/>
                            <rect x="85" y="25" width="3" height="1" fill="#E67E22"/>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Form -->
            <div class="form-section">
                <!-- Name Fields -->
                <div class="name-row">
                    <div class="input-group">
                        <label class="input-label">First Name</label>
                        <input type="text" class="form-input" placeholder="Enter first name" />
                    </div>
                    <div class="input-group">
                        <label class="input-label">Last Name</label>
                        <input type="text" class="form-input" placeholder="Enter last name" />
                    </div>
                </div>

                <!-- Email Field -->
                <div class="input-group">
                    <label class="input-label">School Email Address</label>
                    <div class="input-with-icon">
                        <svg class="input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M20 4H4C2.9 4 2.01 4.9 2.01 6L2 18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z" fill="#999"/>
                        </svg>
                        <input type="email" class="form-input with-icon" placeholder="<EMAIL>" />
                    </div>
                </div>

                <!-- Password Field -->
                <div class="input-group">
                    <label class="input-label">Password</label>
                    <div class="input-with-icon">
                        <svg class="input-icon-left" width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M12 17C10.89 17 10 16.1 10 15C10 13.89 10.89 13 12 13C13.11 13 14 13.89 14 15C14 16.1 13.11 17 12 17ZM18 8C19.1 8 20 8.9 20 10V20C20 21.1 19.1 22 18 22H6C4.9 22 4 21.1 4 20V10C4 8.9 4.9 8 6 8H7V6C7 3.24 9.24 1 12 1C14.76 1 17 3.24 17 6V8H18ZM12 3C10.34 3 9 4.34 9 6V8H15V6C15 4.34 13.66 3 12 3Z" fill="#999"/>
                        </svg>
                        <input type="password" class="form-input with-icon-both" placeholder="Create a password" />
                        <button type="button" class="eye-button">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M12 4.5C7 4.5 2.73 7.61 1 12C2.73 16.39 7 19.5 12 19.5S21.27 16.39 23 12C21.27 7.61 17 4.5 12 4.5ZM12 17C9.24 17 7 14.76 7 12S9.24 7 12 7S17 9.24 17 12S14.76 17 12 17ZM12 9C10.34 9 9 10.34 9 12S10.34 15 12 15S15 13.66 15 12S13.66 9 12 9Z" fill="#999"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Confirm Password Field -->
                <div class="input-group">
                    <label class="input-label">Confirm Password</label>
                    <div class="input-with-icon">
                        <svg class="input-icon-left" width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M12 17C10.89 17 10 16.1 10 15C10 13.89 10.89 13 12 13C13.11 13 14 13.89 14 15C14 16.1 13.11 17 12 17ZM18 8C19.1 8 20 8.9 20 10V20C20 21.1 19.1 22 18 22H6C4.9 22 4 21.1 4 20V10C4 8.9 4.9 8 6 8H7V6C7 3.24 9.24 1 12 1C14.76 1 17 3.24 17 6V8H18ZM12 3C10.34 3 9 4.34 9 6V8H15V6C15 4.34 13.66 3 12 3Z" fill="#999"/>
                        </svg>
                        <input type="password" class="form-input with-icon-both" placeholder="Confirm your password" />
                        <button type="button" class="eye-button">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M12 4.5C7 4.5 2.73 7.61 1 12C2.73 16.39 7 19.5 12 19.5S21.27 16.39 23 12C21.27 7.61 17 4.5 12 4.5ZM12 17C9.24 17 7 14.76 7 12S9.24 7 12 7S17 9.24 17 12S14.76 17 12 17ZM12 9C10.34 9 9 10.34 9 12S10.34 15 12 15S15 13.66 15 12S13.66 9 12 9Z" fill="#999"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Terms Checkbox -->
                <div class="checkbox-group">
                    <label class="checkbox-label">
                        <span class="checkbox-custom checked"></span>
                        <span class="checkbox-text">I am a student and agree to the <a href="#" class="terms-link">Terms of Use</a></span>
                    </label>
                </div>

                <!-- Next Button -->
                <button class="next-button">
                    Next →
                </button>

                <!-- Sign In Link -->
                <p class="signin-text">
                    Already have an account? <a href="#" class="signin-link">Sign in</a>
                </p>
            </div>
        </div>
    </div>
</body>
</html>
