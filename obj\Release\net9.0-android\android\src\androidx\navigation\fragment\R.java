/* AUTO-GENERATED FILE. DO NOT MODIFY.
 *
 * This class was automatically generated by
 * .NET for Android from the resource data it found.
 * It should not be modified by hand.
 */
package androidx.navigation.fragment;

public final class R {
	public static final class attr {
		public static final int defaultNavHost = 0x7f03017d;
	}
	public static final class dimen {
		public static final int sliding_pane_detail_pane_width = 0x7f06031d;
	}
	public static final class id {
		public static final int nav_host_fragment_container = 0x7f080143;
		public static final int sliding_pane_detail_container = 0x7f0801af;
		public static final int sliding_pane_layout = 0x7f0801b0;
	}
	public static final class styleable {
		public static final int[] DialogFragmentNavigator = new int[] { 0x01010003 };
		public static final int DialogFragmentNavigator_android_name = 0;
		public static final int[] FragmentNavigator = new int[] { 0x01010003 };
		public static final int FragmentNavigator_android_name = 0;
		public static final int[] NavHostFragment = new int[] { 0x7f03017d };
		public static final int NavHostFragment_defaultNavHost = 0;
	}
}
