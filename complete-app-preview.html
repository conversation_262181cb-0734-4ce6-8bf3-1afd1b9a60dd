<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GateSale App - Complete Preview</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .preview-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(375px, 1fr));
            gap: 30px;
            max-width: 1600px;
            margin: 0 auto;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            background: linear-gradient(90deg, #FFFFFF 0%, #F8FCFF 40%, #F0F8FF 70%, #E8F4FD 100%);
            border-radius: 25px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
            overflow: hidden;
            position: relative;
            margin: 0 auto;
        }

        .page-title {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        /* Welcome Page Styles */
        .welcome-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            height: 100%;
            box-sizing: border-box;
        }

        .content-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding-top: 60px;
        }

        .logo-container {
            margin-bottom: 30px;
        }

        .logo-image {
            width: 120px;
            height: 90px;
            object-fit: contain;
        }

        .tagline {
            font-size: 16px;
            color: #666666;
            margin: 0 0 40px 0;
            line-height: 1.4;
        }

        .illustration-card {
            position: relative;
            width: 280px;
            height: 200px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 40px;
        }

        .illustration-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .icon-heart {
            position: absolute;
            top: 12px;
            right: 12px;
            background-color: #4CAF50;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 3px 8px rgba(76, 175, 80, 0.3);
        }

        .icon-shield {
            position: absolute;
            bottom: 12px;
            left: 12px;
            background-color: #2196F3;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 3px 8px rgba(33, 150, 243, 0.3);
        }

        .bottom-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-bottom: 40px;
        }

        .next-button {
            width: 320px;
            height: 50px;
            background-color: #00BFFF;
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            cursor: pointer;
        }

        .skip-text {
            font-size: 14px;
            color: #999;
            margin-bottom: 20px;
            cursor: pointer;
        }

        .progress-dots {
            display: flex;
            gap: 8px;
        }

        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #e0e0e0;
        }

        .dot.active {
            background-color: #00BFFF;
        }

        /* Onboarding2 Styles */
        .onboarding-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            height: 100%;
            box-sizing: border-box;
        }

        .progress-indicator {
            display: flex;
            gap: 8px;
            margin-top: 60px;
            margin-bottom: 80px;
        }

        .progress-step {
            width: 24px;
            height: 4px;
            background-color: #e0e0e0;
            border-radius: 2px;
        }

        .progress-step.active {
            background-color: #00bfff;
        }

        .main-title {
            font-size: 28px;
            font-weight: 700;
            color: #333;
            text-align: center;
            margin-bottom: 80px;
        }

        .feature-icons {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 100px;
            width: 100%;
            max-width: 280px;
            position: relative;
        }

        .icon-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 2;
        }

        .icon-square {
            width: 64px;
            height: 64px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        }

        .camera-icon { background-color: #d4926f; }
        .truck-icon { background-color: #4caf50; }
        .smiley-icon { background-color: #2196f3; }

        .icon-dot {
            width: 10px;
            height: 10px;
            background-color: #2196f3;
            border-radius: 50%;
        }

        .features-list {
            width: 100%;
            max-width: 320px;
            margin-bottom: 120px;
        }

        .feature-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            font-size: 16px;
            color: #333;
        }

        .checkmark {
            width: 20px;
            height: 20px;
            background-color: #4caf50;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 12px;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .navigation-section {
            margin-top: auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-bottom: 40px;
        }

        /* Onboarding3 Styles */
        .title-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .highlight-text {
            color: #4CAF50;
        }

        .illustration-section {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
        }

        .illustration-circle {
            width: 280px;
            height: 280px;
            background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%);
            border-radius: 50%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .icon-student, .icon-parent, .icon-email {
            position: absolute;
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .icon-student {
            top: 20px;
            left: 60px;
            background-color: #2196F3;
        }

        .icon-parent {
            top: 20px;
            right: 60px;
            background-color: #FF9800;
        }

        .icon-email {
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #4CAF50;
        }

        .central-shield {
            width: 80px;
            height: 80px;
            background-color: #00BCD4;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 6px 20px rgba(0, 188, 212, 0.3);
        }

        .features-section {
            width: 100%;
            max-width: 320px;
            margin-bottom: 40px;
        }

        .feature-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            flex-shrink: 0;
        }

        .student-verify { background-color: #2196F3; }
        .parent-consent { background-color: #FF9800; }

        .feature-content h3 {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 0 0 4px 0;
        }

        .feature-content p {
            font-size: 14px;
            color: #666;
            margin: 0;
            line-height: 1.4;
        }

        .get-started-button {
            width: 100%;
            height: 50px;
            background-color: #00BFFF;
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 16px;
        }

        .disclaimer-text {
            font-size: 12px;
            color: #999;
            text-align: center;
            line-height: 1.4;
            padding: 0 20px;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <!-- Welcome Page -->
        <div>
            <div class="page-title">1. Welcome Page</div>
            <div class="phone-frame">
                <div class="welcome-container">
                    <div class="content-section">
                        <div class="logo-container">
                            <div class="logo-image" style="background: linear-gradient(135deg, #4ECDC4 0%, #FF8A65 100%); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-size: 16px; font-weight: 800;">
                                GATESALE
                            </div>
                        </div>
                        
                        <p class="tagline">A safe marketplace just for students.</p>
                        
                        <div class="illustration-card">
                            <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%); display: flex; align-items: center; justify-content: center; color: #1976D2; font-size: 14px; text-align: center;">
                                Students connecting safely
                            </div>
                            
                            <div class="icon-heart">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                    <path d="M20.84 4.61C19.32 3.04 17.06 3.04 15.54 4.61L12 8.15L8.46 4.61C6.94 3.04 4.68 3.04 3.16 4.61C1.64 6.18 1.64 8.82 3.16 10.39L12 19.23L20.84 10.39C22.36 8.82 22.36 6.18 20.84 4.61Z" fill="white"/>
                                </svg>
                            </div>
                            <div class="icon-shield">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" fill="white"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bottom-section">
                        <button class="next-button">Next</button>
                        <p class="skip-text">Skip for now</p>
                        <div class="progress-dots">
                            <span class="dot active"></span>
                            <span class="dot"></span>
                            <span class="dot"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onboarding2 Page -->
        <div>
            <div class="page-title">2. Onboarding2 Page</div>
            <div class="phone-frame">
                <div class="onboarding-container">
                    <div class="progress-indicator">
                        <div class="progress-step"></div>
                        <div class="progress-step active"></div>
                        <div class="progress-step"></div>
                    </div>
                    
                    <h1 class="main-title">Buy and Sell Easily</h1>
                    
                    <div class="feature-icons">
                        <div class="icon-container">
                            <div class="icon-square camera-icon">
                                <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                                    <path d="M9 2L7.17 4H4C2.9 4 2 4.9 2 6V18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4H16.83L15 2H9Z" fill="white"/>
                                </svg>
                            </div>
                            <div class="icon-dot"></div>
                        </div>
                        
                        <div class="icon-container">
                            <div class="icon-square truck-icon">
                                <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                                    <path d="M20 8H17V4H3C1.9 4 1 4.9 1 6V17H3C3 18.66 4.34 20 6 20S9 18.66 9 17H15C15 18.66 16.34 20 18 20S21 18.66 21 17H23V12L20 8Z" fill="white"/>
                                </svg>
                            </div>
                            <div class="icon-dot"></div>
                        </div>
                        
                        <div class="icon-container">
                            <div class="icon-square smiley-icon">
                                <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                                    <circle cx="12" cy="12" r="10" fill="white"/>
                                    <circle cx="8" cy="10" r="1.5" fill="#2196F3"/>
                                    <circle cx="16" cy="10" r="1.5" fill="#2196F3"/>
                                    <path d="M8 14s1.5 2 4 2 4-2 4-2" stroke="#2196F3" stroke-width="1.5" stroke-linecap="round"/>
                                </svg>
                            </div>
                            <div class="icon-dot"></div>
                        </div>
                    </div>
                    
                    <div class="features-list">
                        <div class="feature-item">
                            <div class="checkmark">✓</div>
                            <span>List your items in seconds</span>
                        </div>
                        <div class="feature-item">
                            <div class="checkmark">✓</div>
                            <span>Buy safely — payments and deliveries are handled by us</span>
                        </div>
                    </div>
                    
                    <div class="navigation-section">
                        <button class="next-button">Next</button>
                        <p class="skip-text">Skip</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onboarding3 Page -->
        <div>
            <div class="page-title">3. Onboarding3 Page</div>
            <div class="phone-frame">
                <div class="onboarding-container">
                    <div class="progress-indicator">
                        <div class="progress-step"></div>
                        <div class="progress-step"></div>
                        <div class="progress-step active"></div>
                    </div>

                    <div class="title-section">
                        <h1 class="main-title">
                            Safe for You — <br>
                            <span class="highlight-text">Peace of Mind</span><br>
                            for Parents
                        </h1>
                    </div>

                    <div class="illustration-section">
                        <div class="illustration-circle">
                            <!-- Connecting lines -->
                            <svg style="position: absolute; width: 100%; height: 100%; top: 0; left: 0;" viewBox="0 0 280 280">
                                <line x1="80" y1="60" x2="140" y2="120" stroke="#D3D3D3" stroke-width="2" stroke-dasharray="4,4"/>
                                <line x1="200" y1="60" x2="140" y2="120" stroke="#D3D3D3" stroke-width="2" stroke-dasharray="4,4"/>
                                <line x1="140" y1="160" x2="140" y2="220" stroke="#D3D3D3" stroke-width="2" stroke-dasharray="4,4"/>
                            </svg>

                            <div class="icon-student">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 3L1 9L12 15L21 9V16H23V9M5 13.18V17.18L12 21L19 17.18V13.18L12 17L5 13.18Z" fill="white"/>
                                </svg>
                            </div>

                            <div class="icon-parent">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 12C14.21 12 16 10.21 16 8S14.21 4 12 4 8 5.79 8 8 9.79 12 12 12M12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z" fill="white"/>
                                </svg>
                            </div>

                            <div class="central-shield">
                                <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" fill="white"/>
                                </svg>
                            </div>

                            <div class="icon-email">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M20 4H4C2.9 4 2.01 4.9 2.01 6L2 18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z" fill="white"/>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="features-section">
                        <div class="feature-item">
                            <div class="feature-icon student-verify">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 12C14.21 12 16 10.21 16 8S14.21 4 12 4 8 5.79 8 8 9.79 12 12 12M12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z" fill="white"/>
                                </svg>
                            </div>
                            <div class="feature-content">
                                <h3>We verify every student</h3>
                                <p>Your school email ensures only verified students can join your marketplace</p>
                            </div>
                        </div>

                        <div class="feature-item">
                            <div class="feature-icon parent-consent">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                    <path d="M18 16.08C17.24 16.08 16.56 16.38 16.04 16.85L8.91 12.7C8.96 12.47 9 12.24 9 12S8.96 11.53 8.91 11.3L15.96 7.19C16.5 7.69 17.21 8 18 8C19.66 8 21 6.66 21 5S19.66 2 18 2 15 3.34 15 5C15 5.24 15.04 5.47 15.09 5.7L8.04 9.81C7.5 9.31 6.79 9 6 9C4.34 9 3 10.34 3 12S4.34 15 6 15C6.79 15 7.5 14.69 8.04 14.19L15.16 18.34C15.11 18.55 15.08 18.77 15.08 19C15.08 20.61 16.39 21.92 18 21.92S20.92 20.61 20.92 19 19.61 17.08 18 17.08" fill="white"/>
                                </svg>
                            </div>
                            <div class="feature-content">
                                <h3>We get parent consent before you start selling</h3>
                                <p>Parents are notified and must approve before you can sell items on the platform</p>
                            </div>
                        </div>
                    </div>

                    <div class="navigation-section">
                        <button class="get-started-button">Get Started</button>
                        <p class="disclaimer-text">
                            By continuing, you agree to our Terms of Service and Privacy Policy. You must be 13+ to use GateSale.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
