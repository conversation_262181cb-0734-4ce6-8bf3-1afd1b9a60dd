/* AUTO-GENERATED FILE. DO NOT MODIFY.
 *
 * This class was automatically generated by
 * .NET for Android from the resource data it found.
 * It should not be modified by hand.
 */
package androidx.window;

public final class R {
	public static final class attr {
		public static final int activityAction = 0x7f030028;
		public static final int activityName = 0x7f03002a;
		public static final int alwaysExpand = 0x7f030034;
		public static final int animationBackgroundColor = 0x7f030039;
		public static final int clearTop = 0x7f0300e0;
		public static final int finishPrimaryWithPlaceholder = 0x7f0301e7;
		public static final int finishPrimaryWithSecondary = 0x7f0301e8;
		public static final int finishSecondaryWithPrimary = 0x7f0301e9;
		public static final int placeholderActivityName = 0x7f03039d;
		public static final int primaryActivityName = 0x7f0303b1;
		public static final int secondaryActivityAction = 0x7f0303da;
		public static final int secondaryActivityName = 0x7f0303db;
		public static final int splitLayoutDirection = 0x7f03040b;
		public static final int splitMaxAspectRatioInLandscape = 0x7f03040c;
		public static final int splitMaxAspectRatioInPortrait = 0x7f03040d;
		public static final int splitMinHeightDp = 0x7f03040e;
		public static final int splitMinSmallestWidthDp = 0x7f03040f;
		public static final int splitMinWidthDp = 0x7f030410;
		public static final int splitRatio = 0x7f030411;
		public static final int stickyPlaceholder = 0x7f030430;
		public static final int tag = 0x7f030464;
	}
	public static final class id {
		public static final int adjacent = 0x7f080048;
		public static final int always = 0x7f08004d;
		public static final int alwaysAllow = 0x7f08004e;
		public static final int alwaysDisallow = 0x7f08004f;
		public static final int androidx_window_activity_scope = 0x7f080050;
		public static final int bottomToTop = 0x7f080065;
		public static final int locale = 0x7f080102;
		public static final int ltr = 0x7f080103;
		public static final int never = 0x7f080150;
		public static final int rtl = 0x7f08018c;
		public static final int topToBottom = 0x7f0801f2;
	}
	public static final class styleable {
		public static final int[] ActivityFilter = new int[] { 0x7f030028, 0x7f03002a };
		public static final int ActivityFilter_activityAction = 0;
		public static final int ActivityFilter_activityName = 1;
		public static final int[] ActivityRule = new int[] { 0x7f030034, 0x7f030464 };
		public static final int ActivityRule_alwaysExpand = 0;
		public static final int ActivityRule_tag = 1;
		public static final int[] SplitPairFilter = new int[] { 0x7f0303b1, 0x7f0303da, 0x7f0303db };
		public static final int SplitPairFilter_primaryActivityName = 0;
		public static final int SplitPairFilter_secondaryActivityAction = 1;
		public static final int SplitPairFilter_secondaryActivityName = 2;
		public static final int[] SplitPairRule = new int[] { 0x7f030039, 0x7f0300e0, 0x7f0301e8, 0x7f0301e9, 0x7f03040b, 0x7f03040c, 0x7f03040d, 0x7f03040e, 0x7f03040f, 0x7f030410, 0x7f030411, 0x7f030464 };
		public static final int SplitPairRule_animationBackgroundColor = 0;
		public static final int SplitPairRule_clearTop = 1;
		public static final int SplitPairRule_finishPrimaryWithSecondary = 2;
		public static final int SplitPairRule_finishSecondaryWithPrimary = 3;
		public static final int SplitPairRule_splitLayoutDirection = 4;
		public static final int SplitPairRule_splitMaxAspectRatioInLandscape = 5;
		public static final int SplitPairRule_splitMaxAspectRatioInPortrait = 6;
		public static final int SplitPairRule_splitMinHeightDp = 7;
		public static final int SplitPairRule_splitMinSmallestWidthDp = 8;
		public static final int SplitPairRule_splitMinWidthDp = 9;
		public static final int SplitPairRule_splitRatio = 10;
		public static final int SplitPairRule_tag = 11;
		public static final int[] SplitPlaceholderRule = new int[] { 0x7f030039, 0x7f0301e7, 0x7f03039d, 0x7f03040b, 0x7f03040c, 0x7f03040d, 0x7f03040e, 0x7f03040f, 0x7f030410, 0x7f030411, 0x7f030430, 0x7f030464 };
		public static final int SplitPlaceholderRule_animationBackgroundColor = 0;
		public static final int SplitPlaceholderRule_finishPrimaryWithPlaceholder = 1;
		public static final int SplitPlaceholderRule_placeholderActivityName = 2;
		public static final int SplitPlaceholderRule_splitLayoutDirection = 3;
		public static final int SplitPlaceholderRule_splitMaxAspectRatioInLandscape = 4;
		public static final int SplitPlaceholderRule_splitMaxAspectRatioInPortrait = 5;
		public static final int SplitPlaceholderRule_splitMinHeightDp = 6;
		public static final int SplitPlaceholderRule_splitMinSmallestWidthDp = 7;
		public static final int SplitPlaceholderRule_splitMinWidthDp = 8;
		public static final int SplitPlaceholderRule_splitRatio = 9;
		public static final int SplitPlaceholderRule_stickyPlaceholder = 10;
		public static final int SplitPlaceholderRule_tag = 11;
	}
}
