@page "/test-slider"

<div class="test-slider-container">
    <div class="test-slides-wrapper" style="transform: translateX(@($"{-currentSlide * 100}vw"))">
        
        <!-- Slide 1 -->
        <div class="test-slide" style="background-color: #ff6b6b;">
            <div class="test-content">
                <h1>Slide 1 - Welcome</h1>
                <p>Current Slide: @currentSlide</p>
                <button @onclick="() => GoToSlide(1)">Next</button>
            </div>
        </div>

        <!-- Slide 2 -->
        <div class="test-slide" style="background-color: #4ecdc4;">
            <div class="test-content">
                <h1>Slide 2 - Onboarding2</h1>
                <p>Current Slide: @currentSlide</p>
                <button @onclick="() => GoToSlide(0)">Previous</button>
                <button @onclick="() => GoToSlide(2)">Next</button>
            </div>
        </div>

        <!-- Slide 3 -->
        <div class="test-slide" style="background-color: #45b7d1;">
            <div class="test-content">
                <h1>Slide 3 - Onboarding3</h1>
                <p>Current Slide: @currentSlide</p>
                <button @onclick="() => GoToSlide(1)">Previous</button>
            </div>
        </div>
    </div>
</div>

<style>
    .test-slider-container {
        width: 100vw;
        height: 100vh;
        overflow: hidden;
        position: relative;
    }

    .test-slides-wrapper {
        display: flex;
        width: 300vw;
        height: 100vh;
        transition: transform 0.3s ease-in-out;
    }

    .test-slide {
        width: 100vw;
        height: 100vh;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .test-content {
        text-align: center;
        color: white;
        font-family: Arial, sans-serif;
    }

    .test-content h1 {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .test-content p {
        font-size: 1.2rem;
        margin-bottom: 2rem;
    }

    .test-content button {
        background: white;
        color: #333;
        border: none;
        padding: 10px 20px;
        margin: 0 10px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 1rem;
    }

    .test-content button:hover {
        background: #f0f0f0;
    }
</style>

@code {
    private int currentSlide = 0;

    private void GoToSlide(int slideIndex)
    {
        if (slideIndex >= 0 && slideIndex <= 2)
        {
            currentSlide = slideIndex;
            StateHasChanged();
        }
    }
}
