@page "/onboarding2"
@inject NavigationManager Navigation

<div class="onboarding-container">
    <!-- Progress Indicator -->
    <div class="progress-indicator">
        <div class="progress-line"></div>
    </div>

    <!-- Title -->
    <h1 class="main-title">Buy and Sell Easily</h1>

    <!-- Feature Icons -->
    <div class="feature-icons">
        <!-- Camera Icon -->
        <div class="icon-container">
            <div class="icon-circle camera-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M9 2L7.17 4H4C2.9 4 2 4.9 2 6V18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4H16.83L15 2H9ZM12 17C9.24 17 7 14.76 7 12C7 9.24 9.24 7 12 7C14.76 7 17 9.24 17 12C17 14.76 14.76 17 12 17ZM12 9C10.34 9 9 10.34 9 12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12C15 10.34 13.66 9 12 9Z" fill="white"/>
                </svg>
            </div>
            <div class="connecting-line"></div>
            <div class="icon-dot"></div>
        </div>

        <!-- Money Icon -->
        <div class="icon-container">
            <div class="icon-circle money-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.4 5H10.6C9.68 5 8.76 5.06 7.83 5.17L10.5 2.5L9 1L3 7V9H5V19C5 20.1 5.9 21 7 21H17C18.1 21 19 20.1 19 19V9H21ZM12 8C13.1 8 14 8.9 14 10S13.1 12 12 12S10 11.1 10 10S10.9 8 12 8Z" fill="white"/>
                </svg>
            </div>
            <div class="connecting-line"></div>
            <div class="icon-dot"></div>
        </div>

        <!-- Chat Icon -->
        <div class="icon-container">
            <div class="icon-circle chat-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M20 2H4C2.9 2 2 2.9 2 4V22L6 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2ZM20 16H5.17L4 17.17V4H20V16ZM7 9H17V11H7V9ZM7 12H15V14H7V12Z" fill="white"/>
                </svg>
            </div>
            <div class="icon-dot"></div>
        </div>
    </div>

    <!-- Features List -->
    <div class="features-list">
        <div class="feature-item">
            <div class="checkmark">✓</div>
            <span>List your items in seconds</span>
        </div>
        <div class="feature-item">
            <div class="checkmark">✓</div>
            <span>Buy safely — payments and deliveries are handled by us</span>
        </div>
    </div>

    <!-- Navigation Buttons -->
    <div class="navigation-section">
        <button class="next-button" @onclick="GoToNextPage">Next</button>
        <p class="skip-text" @onclick="SkipOnboarding">Skip</p>
    </div>
</div>

<style>
    .onboarding-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        font-family: 'Segoe UI', sans-serif;
        background-color: white;
        height: 100vh;
        width: 375px;
        margin: 0 auto;
        position: relative;
    }

    .progress-indicator {
        width: 60px;
        height: 4px;
        background-color: #e0e0e0;
        border-radius: 2px;
        margin-top: 40px;
        margin-bottom: 40px;
        position: relative;
    }

    .progress-line {
        width: 66%;
        height: 100%;
        background-color: #00bfff;
        border-radius: 2px;
    }

    .main-title {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        text-align: center;
        margin-bottom: 60px;
        margin-top: 0;
    }

    .feature-icons {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 80px;
        width: 100%;
        max-width: 280px;
    }

    .icon-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
    }

    .icon-circle {
        width: 56px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 12px;
    }

    .camera-icon {
        background-color: #d4926f;
    }

    .money-icon {
        background-color: #4caf50;
    }

    .chat-icon {
        background-color: #2196f3;
    }

    .connecting-line {
        position: absolute;
        top: 28px;
        left: 56px;
        width: 40px;
        height: 2px;
        background-color: #e0e0e0;
        z-index: -1;
    }

    .icon-container:last-child .connecting-line {
        display: none;
    }

    .icon-dot {
        width: 8px;
        height: 8px;
        background-color: #2196f3;
        border-radius: 50%;
    }

    .features-list {
        width: 100%;
        max-width: 320px;
        margin-bottom: 60px;
    }

    .feature-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;
        font-size: 16px;
        color: #333;
        line-height: 1.4;
    }

    .checkmark {
        width: 20px;
        height: 20px;
        background-color: #4caf50;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
        margin-right: 12px;
        flex-shrink: 0;
        margin-top: 2px;
    }

    .navigation-section {
        margin-top: auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-bottom: 40px;
    }

    .next-button {
        width: 320px;
        height: 50px;
        background-color: #2aaeff;
        color: white;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        margin-bottom: 16px;
        box-shadow: 0 3px 6px rgba(0,0,0,0.1);
    }

    .next-button:hover {
        background-color: #1c7ed6;
    }

    .skip-text {
        font-size: 16px;
        color: #999;
        margin: 0;
        cursor: pointer;
    }

    .skip-text:hover {
        color: #666;
    }
</style>

@code {
    private void GoToNextPage()
    {
        // Navigate to third onboarding page or main app
        Navigation.NavigateTo("/onboarding3");
    }

    private void SkipOnboarding()
    {
        // Skip to main app
        Navigation.NavigateTo("/main");
    }
}
