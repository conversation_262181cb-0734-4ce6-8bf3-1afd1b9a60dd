<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Background Gradient Analysis</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .test-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        
        .gradient-test {
            width: 300px;
            height: 400px;
            border-radius: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            padding: 20px;
            box-sizing: border-box;
            color: #333;
        }
        
        .test1 {
            background: linear-gradient(to right, #FFFFFF 0%, #E8F4FD 100%);
        }
        
        .test2 {
            background: linear-gradient(to right, #FFFFFF 0%, #F0F8FF 60%, #E0F2FE 100%);
        }
        
        .test3 {
            background: linear-gradient(135deg, #FFFFFF 0%, #F0F8FF 50%, #E8F4FD 100%);
        }
        
        .test4 {
            background: linear-gradient(to right, #FFFFFF 0%, #FFFFFF 30%, #F0F8FF 70%, #E0F2FE 100%);
        }
        
        .test5 {
            background: linear-gradient(90deg, #FFFFFF 0%, #F8FCFF 40%, #F0F8FF 70%, #E8F4FD 100%);
        }
        
        .test6 {
            background: linear-gradient(to right, #FFFFFF 0%, #FAFCFF 25%, #F0F8FF 50%, #E8F4FD 75%, #E0F2FE 100%);
        }
        
        h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            font-weight: 600;
        }
        
        p {
            margin: 5px 0;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .phone-mockup {
            width: 300px;
            height: 400px;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        
        .signup-mockup {
            padding: 30px 20px;
            text-align: center;
        }
        
        .title {
            font-size: 24px;
            font-weight: 700;
            color: #000;
            margin: 0 0 8px 0;
        }
        
        .subtitle {
            font-size: 14px;
            color: #666;
            margin: 0 0 20px 0;
        }
        
        .illustration {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #E0F2FE 0%, #BAE6FD 100%);
            border-radius: 50%;
            margin: 0 auto 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30px;
        }
        
        .form-field {
            width: 100%;
            height: 40px;
            background: rgba(255,255,255,0.8);
            border: 1px solid #E0E0E0;
            border-radius: 8px;
            margin: 8px 0;
        }
        
        .button {
            width: 100%;
            height: 40px;
            background: #00BFFF;
            color: white;
            border: none;
            border-radius: 20px;
            margin: 15px 0;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <h1>Background Gradient Analysis for Signup Page</h1>
    <p>Testing different gradient combinations to match the image exactly:</p>
    
    <div class="test-container">
        <div class="gradient-test test1">
            <div class="signup-mockup">
                <h3>Test 1: Simple Right Fade</h3>
                <p>linear-gradient(to right, #FFFFFF 0%, #E8F4FD 100%)</p>
                <div class="title">Let's Get You Started</div>
                <div class="subtitle">Create your student account</div>
                <div class="illustration">🎒</div>
                <div class="form-field"></div>
                <div class="form-field"></div>
                <div class="button">Next →</div>
            </div>
        </div>
        
        <div class="gradient-test test2">
            <div class="signup-mockup">
                <h3>Test 2: Multi-stop Right</h3>
                <p>linear-gradient(to right, #FFFFFF 0%, #F0F8FF 60%, #E0F2FE 100%)</p>
                <div class="title">Let's Get You Started</div>
                <div class="subtitle">Create your student account</div>
                <div class="illustration">🎒</div>
                <div class="form-field"></div>
                <div class="form-field"></div>
                <div class="button">Next →</div>
            </div>
        </div>
        
        <div class="gradient-test test3">
            <div class="signup-mockup">
                <h3>Test 3: Diagonal Fade</h3>
                <p>linear-gradient(135deg, #FFFFFF 0%, #F0F8FF 50%, #E8F4FD 100%)</p>
                <div class="title">Let's Get You Started</div>
                <div class="subtitle">Create your student account</div>
                <div class="illustration">🎒</div>
                <div class="form-field"></div>
                <div class="form-field"></div>
                <div class="button">Next →</div>
            </div>
        </div>
        
        <div class="gradient-test test4">
            <div class="signup-mockup">
                <h3>Test 4: Extended White</h3>
                <p>linear-gradient(to right, #FFFFFF 0%, #FFFFFF 30%, #F0F8FF 70%, #E0F2FE 100%)</p>
                <div class="title">Let's Get You Started</div>
                <div class="subtitle">Create your student account</div>
                <div class="illustration">🎒</div>
                <div class="form-field"></div>
                <div class="form-field"></div>
                <div class="button">Next →</div>
            </div>
        </div>
        
        <div class="gradient-test test5">
            <div class="signup-mockup">
                <h3>Test 5: Subtle Transition</h3>
                <p>linear-gradient(90deg, #FFFFFF 0%, #F8FCFF 40%, #F0F8FF 70%, #E8F4FD 100%)</p>
                <div class="title">Let's Get You Started</div>
                <div class="subtitle">Create your student account</div>
                <div class="illustration">🎒</div>
                <div class="form-field"></div>
                <div class="form-field"></div>
                <div class="button">Next →</div>
            </div>
        </div>
        
        <div class="gradient-test test6">
            <div class="signup-mockup">
                <h3>Test 6: Multi-step Fade</h3>
                <p>linear-gradient(to right, #FFFFFF 0%, #FAFCFF 25%, #F0F8FF 50%, #E8F4FD 75%, #E0F2FE 100%)</p>
                <div class="title">Let's Get You Started</div>
                <div class="subtitle">Create your student account</div>
                <div class="illustration">🎒</div>
                <div class="form-field"></div>
                <div class="form-field"></div>
                <div class="button">Next →</div>
            </div>
        </div>
    </div>
    
    <h2>Analysis Notes:</h2>
    <ul>
        <li>The image shows a gradient from white on the left to light blue on the right</li>
        <li>The transition appears to be horizontal (left to right)</li>
        <li>The blue is very subtle and light</li>
        <li>Test 5 or 6 likely matches the image best</li>
    </ul>
</body>
</html>
