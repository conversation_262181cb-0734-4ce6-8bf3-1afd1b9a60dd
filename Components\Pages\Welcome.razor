﻿@page "/welcome"
@inject NavigationManager Navigation

<div class="onboarding-container">
    <!-- Content Section -->
    <div class="content-section">
        <!-- Logo -->
        <div class="logo-container">
        <div class="logo-icon">
            <img src="/images/gatesale-logo.png" alt="GATESALE Logo" class="logo-image" />
        </div>
        <div class="logo-text">
            <span class="gate-text">GATE</span><span class="sale-text">SALE</span>
        </div>
    </div>

    <!-- Tagline -->
    <p class="tagline">A safe marketplace just for students.</p>

    <!-- Illustration Card -->
    <div class="illustration-card">
        <!-- Onboarding illustration -->
        <img src="/images/onboarding-1.png" alt="Students connecting safely" class="illustration-img" />

        <!-- Icons -->
        <div class="icon-heart">❤️</div>
        <div class="icon-shield">🛡️</div>
    </div>
    </div>

    <!-- Bottom Section -->
    <div class="bottom-section">
        <!-- Button -->
        <button class="next-button" @onclick="GoToNextPage">Next</button>

        <!-- Skip text -->
        <p class="skip-text" @onclick="SkipOnboarding">Skip for now</p>

        <!-- Progress dots -->
        <div class="progress-dots">
            <span class="dot active"></span>
            <span class="dot"></span>
            <span class="dot"></span>
        </div>
    </div>
</div>

<style>
    .onboarding-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: white;
        height: 100vh;
        justify-content: space-between;
        max-width: 400px;
        margin: 0 auto;
    }

    .content-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        justify-content: center;
        width: 100%;
    }

    .logo-container {
        margin-top: 40px;
        margin-bottom: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .logo-icon {
        margin-bottom: 12px;
    }

    .logo-image {
        width: 60px;
        height: 40px;
        object-fit: contain;
    }

    .logo-text {
        font-size: 24px;
        font-weight: 800;
        letter-spacing: 0.5px;
    }

    .gate-text {
        color: #4ECDC4;
    }

    .sale-text {
        color: #FF8A65;
    }

    .tagline {
        margin: 16px 0 32px 0;
        font-size: 16px;
        color: #666;
        text-align: center;
        font-weight: 400;
    }

    .illustration-card {
        position: relative;
        margin: 0 0 40px 0;
        padding: 16px;
        border-radius: 20px;
        background: linear-gradient(135deg, #E8F4F8 0%, #F0F8FF 100%);
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        max-height: 400px;
        width: 100%;
        max-width: 320px;
    }

    .illustration-img {
        width: 280px;
        height: 240px;
        border-radius: 15px;
        object-fit: cover;
    }

    .icon-heart {
        position: absolute;
        top: 12px;
        right: 12px;
        background-color: #4CAF50;
        color: white;
        border-radius: 50%;
        padding: 8px;
        font-size: 16px;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 3px 8px rgba(76, 175, 80, 0.3);
    }

    .icon-shield {
        position: absolute;
        bottom: 12px;
        left: 12px;
        background-color: #2196F3;
        color: white;
        border-radius: 50%;
        padding: 8px;
        font-size: 16px;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 3px 8px rgba(33, 150, 243, 0.3);
    }

    .bottom-section {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        margin-top: auto;
        padding-bottom: 20px;
    }

    .next-button {
        width: 100%;
        max-width: 320px;
        background-color: #00BFFF;
        color: white;
        padding: 14px 20px;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        box-shadow: 0 3px 6px rgba(0, 191, 255, 0.3);
        cursor: pointer;
        margin-bottom: 12px;
    }

    .next-button:hover {
        background-color: #1C7ED6;
    }

    .skip-text {
        font-size: 14px;
        color: #999;
        margin-bottom: 16px;
        cursor: pointer;
    }

    .skip-text:hover {
        color: #666;
    }

    .progress-dots {
        display: flex;
        gap: 8px;
        margin-bottom: 20px;
    }

    .dot {
        width: 8px;
        height: 8px;
        background-color: #e0e0e0;
        border-radius: 50%;
    }

    .dot.active {
        width: 24px;
        border-radius: 4px;
        background-color: #00BFFF;
    }
</style>

@code {
    private void GoToNextPage()
    {
        Navigation.NavigateTo("/onboarding2");
    }

    private void SkipOnboarding()
    {
        Navigation.NavigateTo("/main");
    }
}
