﻿@page "/welcome"
@inject NavigationManager Navigation

<div class="onboarding-container">
    <!-- Logo -->
    <div class="logo-container">
        <img src="gatesale_logo.png" alt="GATESALE Logo" class="logo-img" />
    </div>

    <!-- Tagline -->
    <p class="tagline">A safe marketplace just for students.</p>

    <!-- Illustration Card -->
    <div class="illustration-card">
        <!-- Onboarding illustration -->
        <img src="onboarding_1.png" alt="Students connecting safely" class="illustration-img" />

        <!-- Icons -->
        <div class="icon-heart">❤️</div>
        <div class="icon-shield">🛡️</div>
    </div>

    <!-- Button -->
    <button class="next-button" @onclick="GoToNextPage">Next</button>

    <!-- Skip text -->
    <p class="skip-text" @onclick="SkipOnboarding">Skip for now</p>

    <!-- Progress dots -->
    <div class="progress-dots">
        <span class="dot active"></span>
        <span class="dot"></span>
        <span class="dot"></span>
    </div>
</div>

<style>
    .onboarding-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        font-family: 'Segoe UI', sans-serif;
        background-color: white;
        height: 100vh;
    }

    .logo-container {
        margin-top: 30px;
    }

    .logo-img {
        width: 140px;
        height: auto;
    }

    .tagline {
        margin-top: 10px;
        font-size: 14px;
        color: #555;
        text-align: center;
    }

    .illustration-card {
        position: relative;
        margin-top: 30px;
        padding: 10px;
        border-radius: 20px;
        background: #f7fafe;
        box-shadow: 0 2px 6px rgba(0,0,0,0.05);
    }

    .illustration-img {
        width: 220px;
        height: 160px;
        border-radius: 15px;
        object-fit: cover;
    }

    .icon-heart {
        position: absolute;
        top: 8px;
        right: 8px;
        background-color: #38e17d;
        color: white;
        border-radius: 50%;
        padding: 6px;
        font-size: 12px;
    }

    .icon-shield {
        position: absolute;
        bottom: 8px;
        left: 8px;
        background-color: #00c6ff;
        color: white;
        border-radius: 50%;
        padding: 6px;
        font-size: 12px;
    }

    .next-button {
        margin-top: 30px;
        background-color: #2aaeff;
        color: white;
        padding: 14px 50px;
        border: none;
        border-radius: 12px;
        font-size: 16px;
        box-shadow: 0 3px 6px rgba(0,0,0,0.1);
    }

    .skip-text {
        margin-top: 10px;
        font-size: 13px;
        color: gray;
    }

    .progress-dots {
        display: flex;
        gap: 5px;
        margin-top: 10px;
    }

    .dot {
        width: 6px;
        height: 6px;
        background-color: lightgray;
        border-radius: 50%;
    }

        .dot.active {
            width: 16px;
            border-radius: 10px;
            background-color: #2aaeff;
        }
</style>

@code {
    private void GoToNextPage()
    {
        Navigation.NavigateTo("/onboarding2");
    }

    private void SkipOnboarding()
    {
        Navigation.NavigateTo("/main");
    }
}
