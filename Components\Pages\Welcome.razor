﻿
@page "/welcome"
@inject NavigationManager Navigation

@code {
    protected override void OnInitialized()
    {
        // Redirect to the new slider component
        Navigation.NavigateTo("/onboarding-slider");
    }
}

<div class="onboarding-container">
    <!-- Content Section -->
    <div class="content-section">
        <!-- Logo -->
        <div class="logo-container">
            <div class="logo-icon">
                <img src="/images/gatesale-logo.png" alt="GATESALE Logo" class="logo-image" />
            </div>
        </div>

        <!-- Tagline -->
        <p class="tagline">A safe marketplace just for students.</p>

        <!-- Illustration Card -->
        <div class="illustration-card">
            <!-- Onboarding illustration -->
            <img src="/images/onboarding-1.jpg" alt="Students connecting safely" class="illustration-img" />

            <!-- Floating icons (match reference image) -->
            <div class="icon-heart" title="Trusted Community">
                <svg width="16" height="16" viewBox="0 0 24 24" aria-hidden="true" focusable="false">
                    <path d="M20.84 4.61C19.32 3.04 17.06 3.04 15.54 4.61L12 8.15L8.46 4.61C6.94 3.04 4.68 3.04 3.16 4.61C1.64 6.18 1.64 8.82 3.16 10.39L12 19.23L20.84 10.39C22.36 8.82 22.36 6.18 20.84 4.61Z" fill="white"/>
                </svg>
            </div>

            <div class="icon-shield" title="Safe & Secure">
                <svg width="16" height="16" viewBox="0 0 24 24" aria-hidden="true" focusable="false">
                    <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1ZM12 7C13.1 7 14 7.9 14 9S13.1 11 12 11S10 10.1 10 9S10.9 7 12 7ZM12 17C10.67 17 9.67 16.33 9.67 15.5C9.67 14.67 10.67 14 12 14S14.33 14.67 14.33 15.5C14.33 16.33 13.33 17 12 17Z" fill="white"/>
                </svg>
            </div>
        </div>
    </div>

    <!-- Bottom Section -->
    <div class="bottom-section">
        <!-- Button -->
        <button class="next-button" @onclick="GoToNextPage">Next</button>

        <!-- Skip text -->
        <p class="skip-text" @onclick="SkipOnboarding">Skip for now</p>

        <!-- Progress dots -->
        <div class="progress-indicator">
            <div class="progress-step active"></div>
            <div class="progress-step"></div>
            <div class="progress-step"></div>
        </div>
    </div>
</div>

<style>
    .onboarding-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        /*background: linear-gradient(90deg, #FFFFFF 0%, #F8FCFF 40%, #F0F8FF 70%, #E8F4FD 100%);*/
        height: 100vh;
        justify-content: space-between;
        max-width: 400px;
        margin: 0 auto;
    }

    .content-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        justify-content: center;
        width: 100%;
    }

    .logo-container {
        margin-top: 40px;
        margin-bottom: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .logo-icon {
        margin-bottom: 12px;
    }

    .logo-image {
        width: 165px;
        height: 121px;
        object-fit: contain;
    }

    .tagline {
        margin: 16px 0 32px 0;
        font-size: 16px;
        color: #666;
        text-align: center;
        font-weight: 400;
    }

    .illustration-card {
        --icon-offset: 12px; /* how far icons float outside the card */
        position: relative;
        margin: 0 0 40px 0;
        padding: 16px;
        border-radius: 20px;
        background: linear-gradient(135deg, #E8F4F8 0%, #F0F8FF 100%);
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        max-height: 400px;
        width: 100%;
        max-width: 320px;
    }

    .illustration-img {
        width: 280px;
        height: 240px;
        border-radius: 15px;
        object-fit: cover;
    }

    /* Floating icon bases */
    .icon-heart,
    .icon-shield {
        position: absolute;
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow:
            0 2px 4px rgba(0,0,0,0.08),
            0 0 0 2px rgba(255,255,255,0.9); /* subtle white ring like reference */
    }

    /* Heart: top-right, green background */
    .icon-heart {
        top: calc(-1 * var(--icon-offset));
        right: calc(-1 * var(--icon-offset));
        background: #27C46D; /* green */
    }

    /* Shield: bottom-left, blue background */
    .icon-shield {
        bottom: calc(-1 * var(--icon-offset));
        left: calc(-1 * var(--icon-offset));
        background: #00BFFF; /* sky blue */
    }

    .bottom-section {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        margin-top: auto;
        padding-bottom: 20px;
    }

    .next-button {
        width: 100%;
        max-width: 320px;
        background-color: #00BFFF;
        color: white;
        padding: 14px 20px;
        border: none;
        border-radius: 15px;
        font-size: 16px;
        font-weight: 600;
        box-shadow: 0 3px 6px rgba(0, 191, 255, 0.3);
        cursor: pointer;
        margin-bottom: 12px;
    }

    
    .skip-text {
        font-size: 14px;
        color: #999;
        margin-bottom: 16px;
        cursor: pointer;
    }


    .progress-indicator {
        display: flex;
        gap: 8px;
        margin-top: 20px; /* reduced spacing */
        margin-bottom: 20px; /* reduced spacing */
        justify-content: center;
    }

    .progress-step {
        width: 24px;
        height: 4px;
        background-color: #e0e0e0;
        border-radius: 2px;
    }

        .progress-step.active {
            background-color: #00bfff;
        }
</style>

@code {
    private void GoToNextPage()
    {
        Navigation.NavigateTo("/onboarding2");
    }

    private void SkipOnboarding()
    {
        Navigation.NavigateTo("/login");
    }
}
```
