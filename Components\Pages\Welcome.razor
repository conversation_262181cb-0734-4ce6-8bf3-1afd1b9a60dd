﻿
@page "/welcome"
@page "/welcome/{showOnboarding:bool?}"
@inject NavigationManager Navigation

@code {
    [Parameter] public bool? ShowOnboarding { get; set; }

    protected override void OnInitialized()
    {
        // Only redirect to onboarding if explicitly requested
        if (ShowOnboarding == true)
        {
            Navigation.NavigateTo("/onboarding-slider");
        }
        else
        {
            // If accessed directly without parameter, redirect to login
            Navigation.NavigateTo("/login");
        }
    }
}

<!-- This page redirects automatically, no content needed -->




