﻿@page "/welcome"
@inject NavigationManager Navigation

<div class="onboarding-container">
    <!-- Logo -->
    <div class="logo-container">
        <div class="logo-text">
            <img src="/images/gatesale-logo.png" alt="GATESALE" class="illustration-img" />
        </div>
    </div>

    <!-- Tagline -->
    <p class="tagline">A safe marketplace just for students.</p>

    <!-- Illustration Card -->
    <div class="illustration-card">
        <!-- Onboarding illustration -->
        <img src="/images/onboarding-1.png" alt="Students connecting safely" class="illustration-img" />

        <!-- Icons -->
        <div class="icon-heart">❤️</div>
        <div class="icon-shield">🛡️</div>
    </div>

    <!-- Button -->
    <button class="next-button" @onclick="GoToNextPage">Next</button>

    <!-- Skip text -->
    <p class="skip-text" @onclick="SkipOnboarding">Skip for now</p>

    <!-- Progress dots -->
    <div class="progress-dots">
        <span class="dot active"></span>
        <span class="dot"></span>
        <span class="dot"></span>
    </div>
</div>

<style>
    .onboarding-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 16px;
        font-family: 'Segoe UI', sans-serif;
        background-color: white;
        height: 100vh;
        justify-content: space-between;
        max-width: 400px;
        margin: 0 auto;
    }

    .logo-container {
        margin-top: 20px;
    }

    .logo-text {
        font-size: 28px;
        font-weight: 800;
        letter-spacing: 1px;
    }

    .gate-text {
        color: #4ECDC4;
    }

    .sale-text {
        color: #FF8A65;
    }

    .tagline {
        margin-top: 8px;
        font-size: 16px;
        color: #666;
        text-align: center;
        font-weight: 400;
    }

    .illustration-card {
        position: relative;
        margin: 20px 0;
        padding: 12px;
        border-radius: 20px;
        background: linear-gradient(135deg, #E8F4F8 0%, #F0F8FF 100%);
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        max-height: 300px;
    }

    .illustration-img {
        width: 260px;
        height: 200px;
        border-radius: 15px;
        object-fit: cover;
    }

    .icon-heart {
        position: absolute;
        top: 12px;
        right: 12px;
        background-color: #4CAF50;
        color: white;
        border-radius: 50%;
        padding: 8px;
        font-size: 16px;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 3px 8px rgba(76, 175, 80, 0.3);
    }

    .icon-shield {
        position: absolute;
        bottom: 12px;
        left: 12px;
        background-color: #2196F3;
        color: white;
        border-radius: 50%;
        padding: 8px;
        font-size: 16px;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 3px 8px rgba(33, 150, 243, 0.3);
    }

    .next-button {
        width: 100%;
        max-width: 320px;
        background-color: #00BFFF;
        color: white;
        padding: 14px 20px;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        box-shadow: 0 3px 6px rgba(0, 191, 255, 0.3);
        cursor: pointer;
        margin-bottom: 12px;
    }

    .next-button:hover {
        background-color: #1C7ED6;
    }

    .skip-text {
        font-size: 14px;
        color: #999;
        margin-bottom: 16px;
        cursor: pointer;
    }

    .skip-text:hover {
        color: #666;
    }

    .progress-dots {
        display: flex;
        gap: 8px;
        margin-bottom: 20px;
    }

    .dot {
        width: 8px;
        height: 8px;
        background-color: #e0e0e0;
        border-radius: 50%;
    }

    .dot.active {
        width: 24px;
        border-radius: 4px;
        background-color: #00BFFF;
    }
</style>

@code {
    private void GoToNextPage()
    {
        Navigation.NavigateTo("/onboarding2");
    }

    private void SkipOnboarding()
    {
        Navigation.NavigateTo("/main");
    }
}
