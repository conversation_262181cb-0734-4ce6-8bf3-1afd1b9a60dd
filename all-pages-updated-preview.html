<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GateSale - All Pages Updated Preview</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f0f0;
        }

        .preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            justify-content: center;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: white;
            border-radius: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
            position: relative;
        }

        .page-title {
            text-align: center;
            margin-bottom: 15px;
            font-weight: bold;
            color: #333;
        }

        /* Welcome Page Styles */
        .welcome-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            background: linear-gradient(90deg, #FFFFFF 0%, #F8FCFF 40%, #F0F8FF 70%, #E8F4FD 100%);
            height: 100%;
            justify-content: space-between;
        }

        .content-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            justify-content: center;
            width: 100%;
        }

        .logo-container {
            margin-top: 40px;
            margin-bottom: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .logo-icon {
            margin-bottom: 12px;
            background: white;
            border-radius: 50%;
            padding: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 180px;
            height: 140px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .logo-image {
            width: 160px;
            height: 120px;
            object-fit: contain;
            border-radius: 8px;
            background: #4ECDC4;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .tagline {
            margin: 16px 0 32px 0;
            font-size: 16px;
            color: #666;
            text-align: center;
            font-weight: 400;
        }

        .illustration-card {
            position: relative;
            margin: 0 0 40px 0;
            padding: 16px;
            border-radius: 20px;
            background: linear-gradient(135deg, #E8F4F8 0%, #F0F8FF 100%);
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            max-height: 400px;
            width: 100%;
            max-width: 320px;
        }

        .illustration-img {
            width: 280px;
            height: 240px;
            border-radius: 15px;
            background: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }

        .icon-heart {
            position: absolute;
            top: 8px;
            right: 8px;
            background-color: transparent;
            border: 2px solid #87CEEB;
            color: #4CAF50;
            border-radius: 12px;
            padding: 6px;
            font-size: 18px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 6px rgba(135, 206, 235, 0.2);
        }

        .icon-shield {
            position: absolute;
            bottom: 8px;
            left: 8px;
            background-color: transparent;
            border: 2px solid #87CEEB;
            color: #2196F3;
            border-radius: 12px;
            padding: 6px;
            font-size: 18px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 6px rgba(135, 206, 235, 0.2);
        }

        .bottom-section {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
            margin-top: auto;
            padding-bottom: 20px;
        }

        .next-button {
            width: 100%;
            max-width: 320px;
            background-color: #00BFFF;
            color: white;
            padding: 14px 20px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            box-shadow: 0 3px 6px rgba(0, 191, 255, 0.3);
            cursor: pointer;
            margin-bottom: 12px;
        }

        .skip-text {
            font-size: 14px;
            color: #999;
            margin-bottom: 16px;
            cursor: pointer;
        }

        .progress-dots {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
        }

        .dot {
            width: 8px;
            height: 8px;
            background-color: #e0e0e0;
            border-radius: 50%;
        }

        .dot.active {
            width: 24px;
            border-radius: 4px;
            background-color: #00BFFF;
        }

        /* Login Page Styles */
        .login-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            background: linear-gradient(90deg, #FFFFFF 0%, #F8FCFF 40%, #F0F8FF 70%, #E8F4FD 100%);
            height: 100%;
        }

        .logo-section {
            margin-top: 80px;
            margin-bottom: 120px;
            display: flex;
            justify-content: center;
            background: white;
            border-radius: 50%;
            padding: 8px;
            width: 180px;
            height: 140px;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .form-section {
            width: 100%;
            max-width: 320px;
            display: flex;
            flex-direction: column;
            gap: 24px;
            margin-bottom: 40px;
        }

        .input-group {
            display: flex;
            flex-direction: column;
        }

        .input-label {
            font-size: 14px;
            font-weight: 600;
            color: #333333;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            height: 48px;
            padding: 12px 16px;
            border: 1px solid #E0E0E0;
            border-radius: 8px;
            font-size: 16px;
            color: #333333;
            background-color: #FAFAFA;
            box-sizing: border-box;
        }

        .login-button {
            width: 100%;
            max-width: 320px;
            height: 50px;
            background-color: #00BFFF;
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 24px;
            box-shadow: 0 3px 8px rgba(0, 191, 255, 0.3);
        }

        .signup-text {
            font-size: 14px;
            color: #666666;
            text-align: center;
            margin: 0;
        }

        .signup-link {
            color: #00BFFF;
            text-decoration: none;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <h1 style="text-align: center; color: #333; margin-bottom: 30px;">GateSale App - Updated Pages Preview</h1>
    
    <div class="preview-container">
        <!-- Welcome Page -->
        <div>
            <div class="page-title">Welcome Page - Updated</div>
            <div class="phone-container">
                <div class="welcome-container">
                    <div class="content-section">
                        <div class="logo-container">
                            <div class="logo-icon">
                                <div class="logo-image">GATESALE</div>
                            </div>
                        </div>
                        <p class="tagline">A safe marketplace just for students.</p>
                        <div class="illustration-card">
                            <div class="illustration-img">Students Image</div>
                            <div class="icon-heart">♥</div>
                            <div class="icon-shield">🛡</div>
                        </div>
                    </div>
                    <div class="bottom-section">
                        <button class="next-button">Next</button>
                        <p class="skip-text">Skip for now</p>
                        <div class="progress-dots">
                            <span class="dot active"></span>
                            <span class="dot"></span>
                            <span class="dot"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Login Page -->
        <div>
            <div class="page-title">Login Page - Updated</div>
            <div class="phone-container">
                <div class="login-container">
                    <div class="logo-section">
                        <div class="logo-image">GATESALE</div>
                    </div>
                    <div class="form-section">
                        <div class="input-group">
                            <label class="input-label">School Email Address</label>
                            <input type="email" class="form-input" placeholder="<EMAIL>" />
                        </div>
                        <div class="input-group">
                            <label class="input-label">Password</label>
                            <input type="password" class="form-input" placeholder="Enter password" />
                        </div>
                    </div>
                    <button class="login-button">Login →</button>
                    <p class="signup-text">
                        Don't have an account? <a href="#" class="signup-link">Sign Up</a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div style="margin-top: 30px; padding: 20px; background: white; border-radius: 10px; max-width: 800px; margin-left: auto; margin-right: auto;">
        <h2>Changes Implemented:</h2>
        <ul>
            <li><strong>Welcome Page Logo:</strong> Changed to solid white background, increased size to touch container edges</li>
            <li><strong>Welcome Page Icons:</strong> Changed to light blue outlined corners instead of solid backgrounds, reduced spacing</li>
            <li><strong>Login Page Logo:</strong> Applied same white background treatment for consistency</li>
            <li><strong>Onboarding3 Connecting Lines:</strong> Separated from icons, positioned in center area</li>
        </ul>
    </div>
</body>
</html>
