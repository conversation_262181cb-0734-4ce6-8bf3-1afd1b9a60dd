D:\Projects\GateSale\bin\Release\net9.0-android\package-lock.json
D:\Projects\GateSale\bin\Release\net9.0-android\package.json
D:\Projects\GateSale\bin\Release\net9.0-android\GateSale.staticwebassets.endpoints.json
D:\Projects\GateSale\bin\Release\net9.0-android\GateSale.runtimeconfig.json
D:\Projects\GateSale\bin\Release\net9.0-android\GateSale.dll
D:\Projects\GateSale\bin\Release\net9.0-android\GateSale.pdb
D:\Projects\GateSale\bin\Release\net9.0-android\com.companyname.gatesale.apk
D:\Projects\GateSale\bin\Release\net9.0-android\com.companyname.gatesale.aab
D:\Projects\GateSale\bin\Release\net9.0-android\com.companyname.gatesale-Signed.aab
D:\Projects\GateSale\bin\Release\net9.0-android\com.companyname.gatesale-Signed.apk
D:\Projects\GateSale\bin\Release\net9.0-android\com.companyname.gatesale-Signed.apk.idsig
D:\Projects\GateSale\obj\Release\net9.0-android\.NETCoreApp,Version=v9.0.AssemblyAttributes.cs
D:\Projects\GateSale\obj\Release\net9.0-android\build.props
D:\Projects\GateSale\obj\Release\net9.0-android\adb.props
D:\Projects\GateSale\obj\Release\net9.0-android\scopedcss\bundle\GateSale.styles.css
D:\Projects\GateSale\obj\Release\net9.0-android\compressed\okv3f23xy4-e5tk7yf482.gz
D:\Projects\GateSale\obj\Release\net9.0-android\compressed\c4yfl1a1cx-6gzpyzhau4.gz
D:\Projects\GateSale\obj\Release\net9.0-android\compressed\x0awlxkggx-8inm30yfxf.gz
D:\Projects\GateSale\obj\Release\net9.0-android\compressed\l2n983ggca-4jtf2pvl4b.gz
D:\Projects\GateSale\obj\Release\net9.0-android\compressed\0qse1rb7ey-onmyqsxya3.gz
D:\Projects\GateSale\obj\Release\net9.0-android\compressed\uv3ua5xd0n-55aay04al6.gz
D:\Projects\GateSale\obj\Release\net9.0-android\staticwebassets.build.json
D:\Projects\GateSale\obj\Release\net9.0-android\staticwebassets.development.json
D:\Projects\GateSale\obj\Release\net9.0-android\staticwebassets.build.endpoints.json
D:\Projects\GateSale\obj\Release\net9.0-android\staticwebassets\msbuild.GateSale.Microsoft.AspNetCore.StaticWebAssets.props
D:\Projects\GateSale\obj\Release\net9.0-android\staticwebassets\msbuild.GateSale.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
D:\Projects\GateSale\obj\Release\net9.0-android\staticwebassets\msbuild.build.GateSale.props
D:\Projects\GateSale\obj\Release\net9.0-android\staticwebassets\msbuild.buildMultiTargeting.GateSale.props
D:\Projects\GateSale\obj\Release\net9.0-android\staticwebassets\msbuild.buildTransitive.GateSale.props
D:\Projects\GateSale\obj\Release\net9.0-android\staticwebassets.pack.json
D:\Projects\GateSale\obj\Release\net9.0-android\staticwebassets.upToDateCheck.txt
D:\Projects\GateSale\obj\Release\net9.0-android\compressed\publish\okv3f23xy4-e5tk7yf482.br
D:\Projects\GateSale\obj\Release\net9.0-android\compressed\publish\c4yfl1a1cx-6gzpyzhau4.br
D:\Projects\GateSale\obj\Release\net9.0-android\compressed\publish\x0awlxkggx-8inm30yfxf.br
D:\Projects\GateSale\obj\Release\net9.0-android\compressed\publish\l2n983ggca-4jtf2pvl4b.br
D:\Projects\GateSale\obj\Release\net9.0-android\compressed\publish\0qse1rb7ey-onmyqsxya3.br
D:\Projects\GateSale\obj\Release\net9.0-android\compressed\publish\uv3ua5xd0n-55aay04al6.br
D:\Projects\GateSale\obj\Release\net9.0-android\staticwebassets.publish.json
D:\Projects\GateSale\obj\Release\net9.0-android\staticwebassets.publish.endpoints.json
D:\Projects\GateSale\obj\Release\net9.0-android\_CompileBindingJava.FileList.txt
D:\Projects\GateSale\obj\Release\net9.0-android\GateSale.csproj.AssemblyReference.cache
D:\Projects\GateSale\obj\Release\net9.0-android\mauiimage.inputs
D:\Projects\GateSale\obj\Release\net9.0-android\mauifont.inputs
D:\Projects\GateSale\obj\Release\net9.0-android\mauisplash.inputs
D:\Projects\GateSale\obj\Release\net9.0-android\mauisplash.stamp
D:\Projects\GateSale\obj\Release\net9.0-android\mauifont.stamp
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\drawable-xxxhdpi\dotnet_bot.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\drawable-xxhdpi\dotnet_bot.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\drawable-xhdpi\dotnet_bot.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\drawable-hdpi\dotnet_bot.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\drawable-mdpi\dotnet_bot.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-xxxhdpi\appicon_round.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-xxhdpi\appicon_round.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-xhdpi\appicon_round.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-hdpi\appicon_round.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-mdpi\appicon_round.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-xxxhdpi\appicon.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-xxhdpi\appicon.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-xhdpi\appicon.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-hdpi\appicon.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-mdpi\appicon.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon_round.xml
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon.xml
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-xxxhdpi\appicon_foreground.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-xxhdpi\appicon_foreground.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-xhdpi\appicon_foreground.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-hdpi\appicon_foreground.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-mdpi\appicon_foreground.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-xxxhdpi\appicon_background.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-xxhdpi\appicon_background.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-xhdpi\appicon_background.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-hdpi\appicon_background.png
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\r\mipmap-mdpi\appicon_background.png
D:\Projects\GateSale\obj\Release\net9.0-android\mauiimage.stamp
D:\Projects\GateSale\obj\Release\net9.0-android\mauiimage.outputs
D:\Projects\GateSale\obj\Release\net9.0-android\libraryprojectimports.cache
D:\Projects\GateSale\obj\Release\net9.0-android\stamp\_BuildLibraryImportsCache.stamp
D:\Projects\GateSale\obj\Release\net9.0-android\case_map.txt
D:\Projects\GateSale\obj\Release\net9.0-android\__Microsoft.Android.Resource.Designer.cs
D:\Projects\GateSale\obj\Release\net9.0-android\res.flag
D:\Projects\GateSale\obj\Release\net9.0-android\resizetizer\C91EEEF2BFBC21E0-files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\97\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\98\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\99\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\100\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\101\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\102\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\105\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\106\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\108\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\109\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\110\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\111\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\112\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\113\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\115\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\117\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\118\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\119\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\120\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\121\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\122\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\124\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\128\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\129\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\130\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\131\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\132\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\134\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\135\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\136\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\137\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\138\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\139\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\140\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\141\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\142\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\143\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\144\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\145\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\146\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\147\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\149\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\150\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\151\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\152\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\153\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\154\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\155\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\156\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\158\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\161\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\162\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\163\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\164\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\lp\167\jl\files.cache
D:\Projects\GateSale\obj\Release\net9.0-android\R.txt
D:\Projects\GateSale\obj\Release\net9.0-android\_Microsoft.Android.Resource.Designer.dll
D:\Projects\GateSale\obj\Release\net9.0-android\R.cs.flag
D:\Projects\GateSale\obj\Release\net9.0-android\GateSale.GeneratedMSBuildEditorConfig.editorconfig
D:\Projects\GateSale\obj\Release\net9.0-android\GateSale.AssemblyInfoInputs.cache
D:\Projects\GateSale\obj\Release\net9.0-android\GateSale.AssemblyInfo.cs
D:\Projects\GateSale\obj\Release\net9.0-android\GateSale.csproj.CoreCompileInputs.cache
D:\Projects\GateSale\obj\Release\net9.0-android\XamlC.stamp
D:\Projects\GateSale\obj\Release\net9.0-android\GateSale.dll
D:\Projects\GateSale\obj\Release\net9.0-android\GateSale.pdb
D:\Projects\GateSale\obj\Release\net9.0-android\GateSale.genruntimeconfig.cache
D:\Projects\GateSale\obj\Release\net9.0-android\GateSale.runtimeconfig.json.bin
D:\Projects\GateSale\obj\Release\net9.0-android\android\bin\com.companyname.gatesale.apk
D:\Projects\GateSale\obj\Release\net9.0-android\android\bin\base.zip
D:\Projects\GateSale\obj\Release\net9.0-android\android\bin\com.companyname.gatesale.aab
D:\Projects\GateSale\obj\Release\net9.0-android\android\bin\com.companyname.gatesale.apks
D:\Projects\GateSale\obj\Release\net9.0-android\res\values\colors.xml
D:\Projects\GateSale\obj\Release\net9.0-android\uploadflags.txt
D:\Projects\GateSale\obj\Release\net9.0-android\resolvedassemblies.hash
D:\Projects\GateSale\obj\Release\net9.0-android\android\typemaps.arm64-v8a.ll
D:\Projects\GateSale\obj\Release\net9.0-android\android\typemaps.x86_64.ll
D:\Projects\GateSale\obj\Release\net9.0-android\AndroidManifest.xml
D:\Projects\GateSale\obj\Release\net9.0-android\android\AndroidManifest.xml
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\MonoRuntimeProvider.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\JavaInteropTypeManager.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\assets\machine.config
D:\Projects\GateSale\obj\Release\net9.0-android\android\bin\mono.android.jar
D:\Projects\GateSale\obj\Release\net9.0-android\static.flag
D:\Projects\GateSale\obj\Release\net9.0-android\__environment__.txt
D:\Projects\GateSale\obj\Release\net9.0-android\android\environment.arm64-v8a.ll
D:\Projects\GateSale\obj\Release\net9.0-android\android\environment.x86_64.ll
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\activity\ktx\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\activity\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\annotation\experimental\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\appcompat\app\AlertDialog_IDialogInterfaceOnCancelListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\appcompat\app\AlertDialog_IDialogInterfaceOnClickListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\appcompat\app\AlertDialog_IDialogInterfaceOnMultiChoiceClickListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\appcompat\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\appcompat\resources\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\appcompat\widget\Toolbar_NavigationOnClickEventDispatcher.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\arch\core\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\browser\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\cardview\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\constraintlayout\widget\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\coordinatorlayout\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\core\ktx\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\core\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\cursoradapter\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\customview\poolingcontainer\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\customview\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\documentfile\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\drawerlayout\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\dynamicanimation\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\emoji2\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\emoji2\viewsintegration\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\exifinterface\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\fragment\ktx\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\fragment\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\interpolator\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\legacy\coreutils\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\lifecycle\ktx\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\lifecycle\livedata\core\ktx\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\lifecycle\livedata\core\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\lifecycle\livedata\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\lifecycle\process\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\lifecycle\runtime\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\lifecycle\viewmodel\ktx\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\lifecycle\viewmodel\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\lifecycle\viewmodel\savedstate\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\loader\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\localbroadcastmanager\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\navigation\common\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\navigation\fragment\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\navigation\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\navigation\ui\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\print\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\profileinstaller\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\recyclerview\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\savedstate\ktx\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\savedstate\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\security\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\slidingpanelayout\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\startup\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\swiperefreshlayout\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\tracing\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\transition\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\vectordrawable\animated\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\vectordrawable\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\versionedparcelable\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\viewpager2\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\viewpager\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\window\extensions\core\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\androidx\window\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\com\bumptech\glide\gifdecoder\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\com\bumptech\glide\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\com\google\android\material\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\com\microsoft\maui\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640a8d9a12ddbf2cf2\BatteryBroadcastReceiver.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640a8d9a12ddbf2cf2\DeviceDisplayImplementation_Listener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640a8d9a12ddbf2cf2\EnergySaverBroadcastReceiver.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ContainerView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\CustomFrameLayout.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\RecyclerViewContainer.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ScrollLayoutManager.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellContentFragment.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellFlyoutLayout.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellFlyoutRecyclerAdapter.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellFlyoutRecyclerAdapter_ElementViewHolder.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellFlyoutRecyclerAdapter_ShellLinearLayout.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellFlyoutRenderer.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellFlyoutTemplatedContentRenderer.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellFlyoutTemplatedContentRenderer_HeaderContainer.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellFragmentContainer.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellFragmentStateAdapter.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellItemRenderer.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellItemRendererBase.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellPageContainer.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellSearchView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellSearchViewAdapter.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellSearchViewAdapter_CustomFilter.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellSearchViewAdapter_ObjectWrapper.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellSearchView_ClipDrawableWrapper.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellSectionRenderer.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellSectionRenderer_ViewPagerPageChanged.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellToolbarTracker.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc640ec207abc449b2ca\ShellToolbarTracker_FlyoutIconDrawerDrawable.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64338477404e88479c\ColorChangeRevealDrawable.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64338477404e88479c\ControlsAccessibilityDelegate.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64338477404e88479c\DragAndDropGestureHandler.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64338477404e88479c\DragAndDropGestureHandler_CustomLocalStateData.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64338477404e88479c\FragmentContainer.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64338477404e88479c\GenericAnimatorListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64338477404e88479c\GenericGlobalLayoutListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64338477404e88479c\GenericMenuClickListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64338477404e88479c\GradientStrokeDrawable.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64338477404e88479c\InnerGestureListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64338477404e88479c\InnerScaleListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64338477404e88479c\MauiViewPager.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64338477404e88479c\ModalNavigationManager_ModalFragment.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64338477404e88479c\ModalNavigationManager_ModalFragment_CustomComponentDialog.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64338477404e88479c\ModalNavigationManager_ModalFragment_CustomComponentDialog_CallBack.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64338477404e88479c\MultiPageFragmentStateAdapter_1.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64338477404e88479c\PointerGestureHandler.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64338477404e88479c\TapAndPanGestureDetector.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64338477404e88479c\ToolbarExtensions_ToolbarTitleIconImageView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc643f2b18b2570eaa5a\PlatformGraphicsView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\AccessibilityDelegateCompatWrapper.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\BorderDrawable.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\ContainerView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\ContentViewGroup.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\FragmentManagerExtensions_CallBacks.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\LayoutViewGroup.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\LocalizedDigitsKeyListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiAccessibilityDelegateCompat.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiAppCompatEditText.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiBoxView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiDatePicker.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiHorizontalScrollView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiHybridWebView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiHybridWebViewClient.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiLayerDrawable.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiMaterialButton.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiMaterialButton_MauiResizableDrawable.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiPageControl.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiPageControl_TEditClickListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiPicker.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiPickerBase.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiScrollView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiSearchView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiShapeView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiStepper.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiSwipeRefreshLayout.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiSwipeView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiTextView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiTimePicker.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiWebChromeClient.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiWebView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiWebViewClient.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\NavigationRootManager_ElementBasedFragment.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\NavigationViewFragment.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\PlatformTouchGraphicsView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\ScopedFragment.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\StackNavigationManager_Callbacks.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\StepperHandlerHolder.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\StepperHandlerManager_StepperListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\SwipeViewPager.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\ViewFragment.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\WebViewExtensions_JavascriptResult.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6452ffdc5b34af3a0f\WrapperView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\CarouselSpacingItemDecoration.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\CarouselViewAdapter_2.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\CarouselViewOnScrollListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\CenterSnapHelper.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\DataChangeObserver.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\EdgeSnapHelper.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\EmptyViewAdapter.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\EndSingleSnapHelper.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\EndSnapHelper.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\GridLayoutSpanSizeLookup.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\GroupableItemsViewAdapter_2.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\ItemContentView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\ItemsViewAdapter_2.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\MauiCarouselRecyclerView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\MauiCarouselRecyclerView_CarouselViewOnGlobalLayoutListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\MauiRecyclerView_3.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\NongreedySnapHelper.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\NongreedySnapHelper_InitialScrollListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\PositionalSmoothScroller.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\RecyclerViewScrollListener_2.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\ReorderableItemsViewAdapter_2.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\ScrollHelper.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\SelectableItemsViewAdapter_2.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\SelectableViewHolder.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\SimpleItemTouchHelperCallback.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\SimpleViewHolder.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\SingleSnapHelper.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\SizedItemContentView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\SpacingItemDecoration.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\StartSingleSnapHelper.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\StartSnapHelper.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\StructuredItemsViewAdapter_2.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\TemplatedItemViewHolder.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc645d80431ce5f73f11\TextViewHolder.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6468b6408a11370c2f\WebAuthenticatorIntermediateActivity.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6488302ad6e9e4df1a\ImageLoaderCallback.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6488302ad6e9e4df1a\ImageLoaderCallbackBase_1.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6488302ad6e9e4df1a\ImageLoaderResultCallback.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6488302ad6e9e4df1a\MauiAppCompatActivity.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6488302ad6e9e4df1a\MauiApplication.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc6488302ad6e9e4df1a\MauiApplication_ActivityLifecycleCallbacks.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc649ff77a65592e7d55\TabbedPageManager_Listeners.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc649ff77a65592e7d55\TabbedPageManager_TempView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64a096dc44ad241142\PlatformTicker_DurationScaleListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64b5e713d400f589b7\LinearGradientShaderFactory.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64b5e713d400f589b7\MauiDrawable.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64b5e713d400f589b7\RadialGradientShaderFactory.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64ba438d8f48cf7e75\ActivityLifecycleContextListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64ba438d8f48cf7e75\IntermediateActivity.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64d693e2d9159537db\AndroidWebKitWebViewManager_BlazorWebMessageCallback.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64d693e2d9159537db\BlazorAndroidWebView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64d693e2d9159537db\BlazorWebChromeClient.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64d693e2d9159537db\WebKitWebViewClient.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64d693e2d9159537db\WebKitWebViewClient_JavaScriptValueCallback.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\BaseCellView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\CellAdapter.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\CellRenderer_RendererHolder.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\ConditionalFocusLayout.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\EntryCellEditText.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\EntryCellView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\FrameRenderer.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\GroupedListViewAdapter.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\ListViewAdapter.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\ListViewRenderer.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\ListViewRenderer_Container.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\ListViewRenderer_ListViewScrollDetector.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\ListViewRenderer_ListViewSwipeRefreshLayoutListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\ListViewRenderer_SwipeRefreshLayoutWithFixedNestedScrolling.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\SwitchCellView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\TableViewModelRenderer.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\TableViewRenderer.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\TextCellRenderer_TextCellView.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\ViewCellRenderer_ViewCellContainer.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\ViewCellRenderer_ViewCellContainer_LongPressGestureListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\ViewCellRenderer_ViewCellContainer_TapGestureListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\ViewRenderer.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\ViewRenderer_2.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e1fb321c08285b90\VisualElementRenderer_1.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64e53d2f592022988e\ConnectivityBroadcastReceiver.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64ec91701f98ed8b85\MainActivity.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64ec91701f98ed8b85\MainApplication.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64f728827fec74e9c3\TapWindowTracker_GestureListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64f728827fec74e9c3\Toolbar_Container.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64fcf28c0e24b4cc31\ButtonHandler_ButtonClickListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64fcf28c0e24b4cc31\ButtonHandler_ButtonTouchListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64fcf28c0e24b4cc31\HybridWebViewHandler_HybridWebViewJavaScriptInterface.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64fcf28c0e24b4cc31\SearchBarHandler_FocusChangeListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64fcf28c0e24b4cc31\SliderHandler_SeekBarChangeListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64fcf28c0e24b4cc31\SwitchHandler_CheckedChangeListener.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\crc64fcf28c0e24b4cc31\ToolbarHandler_ProcessBackClick.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\microsoft\maui\essentials\fileProvider.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\microsoft\maui\platform\MauiNavHostFragment.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\activity\contextaware\OnContextAvailableListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\appcompat\app\ActionBar_OnMenuVisibilityListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\appcompat\widget\SearchView_OnCloseListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\appcompat\widget\SearchView_OnQueryTextListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\appcompat\widget\SearchView_OnSuggestionListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\appcompat\widget\Toolbar_OnMenuItemClickListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\core\view\ActionProvider_SubUiVisibilityListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\core\view\ActionProvider_VisibilityListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\core\view\WindowInsetsControllerCompat_OnControllableInsetsChangedListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\core\widget\NestedScrollView_OnScrollChangeListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\drawerlayout\widget\DrawerLayout_DrawerListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\fragment\app\FragmentManager_OnBackStackChangedListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\fragment\app\FragmentOnAttachListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\navigation\NavController_OnDestinationChangedListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\recyclerview\widget\RecyclerView_OnChildAttachStateChangeListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\recyclerview\widget\RecyclerView_OnItemTouchListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\recyclerview\widget\RecyclerView_RecyclerListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\swiperefreshlayout\widget\SwipeRefreshLayout_OnRefreshListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\viewpager\widget\ViewPager_OnAdapterChangeListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\androidx\viewpager\widget\ViewPager_OnPageChangeListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\android\app\ApplicationRegistration.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\com\google\android\material\appbar\AppBarLayout_LiftOnScrollListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\com\google\android\material\appbar\AppBarLayout_OnOffsetChangedListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\com\google\android\material\button\MaterialButton_OnCheckedChangeListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\com\google\android\material\checkbox\MaterialCheckBox_OnCheckedStateChangedListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\com\google\android\material\checkbox\MaterialCheckBox_OnErrorChangedListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\com\google\android\material\navigation\NavigationBarView_OnItemReselectedListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\com\google\android\material\navigation\NavigationBarView_OnItemSelectedListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\com\google\android\material\navigation\NavigationView_OnNavigationItemSelectedListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\com\google\android\material\tabs\TabLayout_BaseOnTabSelectedListenerImplementor.java
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\mono\MonoPackageManager_Resources.java
D:\Projects\GateSale\obj\Release\net9.0-android\assets\AboutAssets.txt
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\css\tailwind.css.gz
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\css\bootstrap\bootstrap.min.css.gz
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\css\input.css.gz
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\css\app.css.gz
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\css\tailwind.css.br
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\css\bootstrap\bootstrap.min.css.br
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\css\input.css.br
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\css\app.css.br
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\index.html.br
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\css\bootstrap\bootstrap.min.css.map.br
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\index.html.gz
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\css\bootstrap\bootstrap.min.css.map.gz
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\css\app.css
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\css\bootstrap\bootstrap.min.css
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\css\bootstrap\bootstrap.min.css.map
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\css\input.css
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\css\tailwind.css
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\images\bagpack.jpg
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\images\gatesale-logo.png
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\images\onboarding-1.jpg
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\images\school.jpg
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\index.html
D:\Projects\GateSale\obj\Release\net9.0-android\android\bin\packaged_resources
D:\Projects\GateSale\obj\Release\net9.0-android\android\src\com\companyname\gatesale\R.java
D:\Projects\GateSale\obj\Release\net9.0-android\_CompileJava.FileList.txt
D:\Projects\GateSale\obj\Release\net9.0-android\android\compressed_assemblies.arm64-v8a.ll
D:\Projects\GateSale\obj\Release\net9.0-android\android\compressed_assemblies.x86_64.ll
D:\Projects\GateSale\obj\Release\net9.0-android\android\typemaps.arm64-v8a.o
D:\Projects\GateSale\obj\Release\net9.0-android\android\typemaps.x86_64.o
D:\Projects\GateSale\obj\Release\net9.0-android\android\environment.arm64-v8a.o
D:\Projects\GateSale\obj\Release\net9.0-android\android\environment.x86_64.o
D:\Projects\GateSale\obj\Release\net9.0-android\android\compressed_assemblies.arm64-v8a.o
D:\Projects\GateSale\obj\Release\net9.0-android\android\compressed_assemblies.x86_64.o
D:\Projects\GateSale\obj\Release\net9.0-android\android\marshal_methods.arm64-v8a.o
D:\Projects\GateSale\obj\Release\net9.0-android\android\marshal_methods.x86_64.o
D:\Projects\GateSale\obj\Release\net9.0-android\android\jni_remap.arm64-v8a.o
D:\Projects\GateSale\obj\Release\net9.0-android\android\jni_remap.x86_64.o
D:\Projects\GateSale\obj\Release\net9.0-android\app_shared_libraries\arm64-v8a\libxamarin-app.so
D:\Projects\GateSale\obj\Release\net9.0-android\app_shared_libraries\x86_64\libxamarin-app.so
D:\Projects\GateSale\obj\Release\net9.0-android\android\bin\classes.dex
D:\Projects\GateSale\obj\Release\net9.0-android\android\bin\classes2.dex
D:\Projects\GateSale\obj\Release\net9.0-android\stamp\_CleanIntermediateIfNeeded.stamp
D:\Projects\GateSale\obj\Release\net9.0-android\stamp\_CompileJava.stamp
D:\Projects\GateSale\obj\Release\net9.0-android\stamp\_CompileToDalvik.stamp
D:\Projects\GateSale\obj\Release\net9.0-android\stamp\_ConvertCustomView.stamp
D:\Projects\GateSale\obj\Release\net9.0-android\stamp\_ConvertResourcesCases.stamp
D:\Projects\GateSale\obj\Release\net9.0-android\stamp\_GenerateJavaStubs.stamp
D:\Projects\GateSale\obj\Release\net9.0-android\stamp\_GeneratePackageManagerJava.stamp
D:\Projects\GateSale\obj\Release\net9.0-android\stamp\_ResolveLibraryProjectImports.stamp
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\images\book.jpg
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\images\parental.jpg
D:\Projects\GateSale\obj\Release\net9.0-android\assets\wwwroot\images\phone.jpg
