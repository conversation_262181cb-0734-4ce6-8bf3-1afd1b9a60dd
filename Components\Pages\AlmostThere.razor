@page "/almostthere"
@inject NavigationManager Navigation

<div class="almost-there-container">
    <!-- Header with back button and progress -->
    <div class="header-section">
        <button class="back-button" @onclick="GoBack">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M15 18L9 12L15 6" stroke="#333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </button>
        <div class="progress-dots">
            <div class="progress-dot inactive"></div>
            <div class="progress-dot inactive"></div>
            <div class="progress-dot active"></div>
        </div>
    </div>

    <!-- Title section -->
    <div class="title-section">
        <h1 class="main-title">Almost There!</h1>
        <div class="celebration-emojis">🎉🚀</div>
    </div>

    <!-- Phone mockup with email sent -->
    <div class="phone-mockup-section">
        <!-- Decorative elements -->
        <div class="heart-icon">❤️</div>
        <div class="star-icon">⭐</div>
        <div class="thumbs-up-icon">👍</div>
        
        <!-- Phone mockup -->
        <div class="phone-mockup">
            <div class="phone-screen">
                <div class="email-sent-content">
                    <img src="/images/emailsent.png" alt="Email Sent" class="email-sent-image" />
                    <div class="status-dots">
                        <div class="status-dot"></div>
                        <div class="status-dot"></div>
                        <div class="status-dot"></div>
                    </div>
                    <p class="email-sent-text">Email Sent!</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Description section -->
    <div class="description-section">
        <p class="description-text">
            We've sent an email to your parent.<br>
            As soon as they approve, you'll be<br>
            ready to start <span class="buying-text">buying</span> & <span class="selling-text">selling</span>!
        </p>
    </div>

    <!-- Buttons section -->
    <div class="buttons-section">
        <button class="window-shop-button" @onclick="WindowShop">
            🛍️ Window Shop While You Wait
        </button>
        
        <button class="back-home-button" @onclick="BackToHome">
            🏠 Back to Home
        </button>
    </div>

    <!-- Footer section -->
    <div class="footer-section">
        <p class="help-text">
            Need help? <span class="contact-link" @onclick="ContactSupport">Contact Support</span>
        </p>
    </div>
</div>

<style>
    .almost-there-container {
        display: flex;
        flex-direction: column;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: #F8F9FA;
        min-height: 100vh;
        width: 375px;
        margin: 0 auto;
        position: relative;
    }

    .header-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        margin-top: 40px;
    }

    .back-button {
        background: none;
        border: none;
        padding: 8px;
        cursor: pointer;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .back-button:hover {
        background-color: rgba(0,0,0,0.05);
    }

    .progress-dots {
        display: flex;
        gap: 8px;
    }

    .progress-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
    }

    .progress-dot.inactive {
        background-color: #D1D5DB;
    }

    .progress-dot.active {
        background-color: #3B82F6;
    }

    .title-section {
        text-align: center;
        margin: 20px 0 30px 0;
    }

    .main-title {
        font-size: 32px;
        font-weight: 800;
        color: #000000;
        margin: 0 0 8px 0;
        line-height: 1.2;
    }

    .celebration-emojis {
        font-size: 24px;
    }

    .phone-mockup-section {
        position: relative;
        display: flex;
        justify-content: center;
        margin: 40px 0;
        height: 280px;
    }

    /* Decorative elements */
    .heart-icon {
        position: absolute;
        top: 20px;
        left: 60px;
        font-size: 24px;
        z-index: 1;
    }

    .star-icon {
        position: absolute;
        top: 40px;
        right: 80px;
        font-size: 20px;
        z-index: 1;
    }

    .thumbs-up-icon {
        position: absolute;
        bottom: 60px;
        left: 40px;
        font-size: 20px;
        z-index: 1;
    }

    /* Phone mockup */
    .phone-mockup {
        width: 180px;
        height: 220px;
        background: white;
        border-radius: 20px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        padding: 12px;
        z-index: 2;
        position: relative;
    }

    .phone-screen {
        width: 100%;
        height: 100%;
        background: white;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }

    .email-sent-content {
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
    }

    .email-sent-image {
        width: 80px;
        height: 60px;
        object-fit: contain;
    }

    .status-dots {
        display: flex;
        gap: 4px;
    }

    .status-dot {
        width: 6px;
        height: 6px;
        background-color: #3B82F6;
        border-radius: 50%;
    }

    .email-sent-text {
        font-size: 14px;
        font-weight: 600;
        color: #3B82F6;
        margin: 0;
    }

    .description-section {
        text-align: center;
        margin: 0 20px 40px 20px;
    }

    .description-text {
        font-size: 16px;
        color: #666666;
        line-height: 1.5;
        margin: 0;
    }

    .buying-text {
        color: #10B981;
        font-weight: 600;
    }

    .selling-text {
        color: #EF4444;
        font-weight: 600;
    }

    .buttons-section {
        padding: 0 20px;
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-bottom: 30px;
    }

    .window-shop-button {
        width: 100%;
        height: 50px;
        background-color: #3B82F6;
        color: white;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        box-shadow: 0 3px 8px rgba(59, 130, 246, 0.3);
    }

    .window-shop-button:hover {
        background-color: #2563EB;
    }

    .back-home-button {
        width: 100%;
        height: 50px;
        background-color: white;
        color: #666666;
        border: 1px solid #E5E7EB;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .back-home-button:hover {
        background-color: #F9FAFB;
    }

    .footer-section {
        text-align: center;
        margin-top: auto;
        padding: 20px;
    }

    .help-text {
        font-size: 14px;
        color: #9CA3AF;
        margin: 0;
    }

    .contact-link {
        color: #3B82F6;
        cursor: pointer;
        text-decoration: none;
    }

    .contact-link:hover {
        text-decoration: underline;
    }
</style>

@code {
    private void GoBack()
    {
        Navigation.NavigateTo("/parentalconsent");
    }

    private void WindowShop()
    {
        // Navigate to window shopping/browse mode
        Navigation.NavigateTo("/browse");
    }

    private void BackToHome()
    {
        Navigation.NavigateTo("/");
    }

    private void ContactSupport()
    {
        // Handle contact support action
        // Could open email client or navigate to support page
    }
}
