{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "css/app.css", "AssetFile": "css/app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000571102227"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1750"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw=\""}, {"Name": "ETag", "Value": "W/\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.css", "AssetFile": "css/app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3376"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.css.gz", "AssetFile": "css/app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1750"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw="}]}, {"Route": "css/app.e5tk7yf482.css", "AssetFile": "css/app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000571102227"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1750"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw=\""}, {"Name": "ETag", "Value": "W/\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}, {"Name": "label", "Value": "css/app.css"}]}, {"Route": "css/app.e5tk7yf482.css", "AssetFile": "css/app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3376"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}, {"Name": "label", "Value": "css/app.css"}]}, {"Route": "css/app.e5tk7yf482.css.gz", "AssetFile": "css/app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1750"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "integrity", "Value": "sha256-qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw="}, {"Name": "label", "Value": "css/app.css.gz"}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css", "AssetFile": "css/bootstrap/bootstrap.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041844506"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css", "AssetFile": "css/bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "162726"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css.gz", "AssetFile": "css/bootstrap/bootstrap.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "integrity", "Value": "sha256-LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.gz"}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "css/bootstrap/bootstrap.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041844506"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "css/bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162726"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013350065"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map.gz", "AssetFile": "css/bootstrap/bootstrap.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map.gz"}]}, {"Route": "css/bootstrap/bootstrap.min.css.gz", "AssetFile": "css/bootstrap/bootstrap.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013350065"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map.gz", "AssetFile": "css/bootstrap/bootstrap.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU="}]}, {"Route": "css/input.4jtf2pvl4b.css", "AssetFile": "css/input.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003891050584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU=\""}, {"Name": "ETag", "Value": "W/\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}, {"Name": "label", "Value": "css/input.css"}]}, {"Route": "css/input.4jtf2pvl4b.css", "AssetFile": "css/input.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "476"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}, {"Name": "label", "Value": "css/input.css"}]}, {"Route": "css/input.4jtf2pvl4b.css.gz", "AssetFile": "css/input.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "integrity", "Value": "sha256-ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU="}, {"Name": "label", "Value": "css/input.css.gz"}]}, {"Route": "css/input.css", "AssetFile": "css/input.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003891050584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU=\""}, {"Name": "ETag", "Value": "W/\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.css", "AssetFile": "css/input.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "476"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.css.gz", "AssetFile": "css/input.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU="}]}, {"Route": "css/tailwind.css", "AssetFile": "css/tailwind.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000191607588"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5218"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gIApGFSMjV7e0ktFpMgV49KaXV1XPi1hbYZndhnOSIw=\""}, {"Name": "ETag", "Value": "W/\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.css", "AssetFile": "css/tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23768"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}]}, {"Route": "css/tailwind.css.gz", "AssetFile": "css/tailwind.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5218"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gIApGFSMjV7e0ktFpMgV49KaXV1XPi1hbYZndhnOSIw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gIApGFSMjV7e0ktFpMgV49KaXV1XPi1hbYZndhnOSIw="}]}, {"Route": "css/tailwind.onmyqsxya3.css", "AssetFile": "css/tailwind.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000191607588"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5218"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gIApGFSMjV7e0ktFpMgV49KaXV1XPi1hbYZndhnOSIw=\""}, {"Name": "ETag", "Value": "W/\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "onmyqsxya3"}, {"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}, {"Name": "label", "Value": "css/tailwind.css"}]}, {"Route": "css/tailwind.onmyqsxya3.css", "AssetFile": "css/tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "23768"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "onmyqsxya3"}, {"Name": "integrity", "Value": "sha256-NIEYxis1p4dR6uW5F41wODiMohAY8LRe3vkepyI33ow="}, {"Name": "label", "Value": "css/tailwind.css"}]}, {"Route": "css/tailwind.onmyqsxya3.css.gz", "AssetFile": "css/tailwind.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5218"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gIApGFSMjV7e0ktFpMgV49KaXV1XPi1hbYZndhnOSIw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:59:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "onmyqsxya3"}, {"Name": "integrity", "Value": "sha256-gIApGFSMjV7e0ktFpMgV49KaXV1XPi1hbYZndhnOSIw="}, {"Name": "label", "Value": "css/tailwind.css.gz"}]}, {"Route": "images/bagpack.jpg", "AssetFile": "images/bagpack.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "9109"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"xOoLewrcV24WWrZig0WoK6pOM/8ibw0qDYlAxUz/8UI=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xOoLewrcV24WWrZig0WoK6pOM/8ibw0qDYlAxUz/8UI="}]}, {"Route": "images/bagpack.k0z6ov860a.jpg", "AssetFile": "images/bagpack.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9109"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"xOoLewrcV24WWrZig0WoK6pOM/8ibw0qDYlAxUz/8UI=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k0z6ov860a"}, {"Name": "integrity", "Value": "sha256-xOoLewrcV24WWrZig0WoK6pOM/8ibw0qDYlAxUz/8UI="}, {"Name": "label", "Value": "images/bagpack.jpg"}]}, {"Route": "images/book.59aobo96p5.jpg", "AssetFile": "images/book.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"F1uLNJm3kGCEmNH8p8blXl4SOGkgja83lBRE9Lz0qfw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "59aobo96p5"}, {"Name": "integrity", "Value": "sha256-F1uLNJm3kGCEmNH8p8blXl4SOGkgja83lBRE9Lz0qfw="}, {"Name": "label", "Value": "images/book.jpg"}]}, {"Route": "images/book.jpg", "AssetFile": "images/book.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"F1uLNJm3kGCEmNH8p8blXl4SOGkgja83lBRE9Lz0qfw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-F1uLNJm3kGCEmNH8p8blXl4SOGkgja83lBRE9Lz0qfw="}]}, {"Route": "images/gatesale-logo.2ndi59cmif.png", "AssetFile": "images/gatesale-logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "63944"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"jj6JMxc5paIu5Hijgah8/q3lGtPZDfS6U//EC75tSK4=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 05:12:59 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2ndi59cmif"}, {"Name": "integrity", "Value": "sha256-jj6JMxc5paIu5Hijgah8/q3lGtPZDfS6U//EC75tSK4="}, {"Name": "label", "Value": "images/gatesale-logo.png"}]}, {"Route": "images/gatesale-logo.png", "AssetFile": "images/gatesale-logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "63944"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"jj6JMxc5paIu5Hijgah8/q3lGtPZDfS6U//EC75tSK4=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 05:12:59 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jj6JMxc5paIu5Hijgah8/q3lGtPZDfS6U//EC75tSK4="}]}, {"Route": "images/mailbox.7xp01cncge.jpg", "AssetFile": "images/mailbox.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "25776"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"O0SXcM9J0IEX59JNCdBXfDZDWBEqKDpDmsGJNBmCDzQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 07:08:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7xp01cncge"}, {"Name": "integrity", "Value": "sha256-O0SXcM9J0IEX59JNCdBXfDZDWBEqKDpDmsGJNBmCDzQ="}, {"Name": "label", "Value": "images/mailbox.jpg"}]}, {"Route": "images/mailbox.jpg", "AssetFile": "images/mailbox.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "25776"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"O0SXcM9J0IEX59JNCdBXfDZDWBEqKDpDmsGJNBmCDzQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 07:08:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O0SXcM9J0IEX59JNCdBXfDZDWBEqKDpDmsGJNBmCDzQ="}]}, {"Route": "images/onboarding-1.jpg", "AssetFile": "images/onboarding-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "41637"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"234oSg737ZpiLcPlvxkrDR/NPxTPadosJYscwqMks0U=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-234oSg737ZpiLcPlvxkrDR/NPxTPadosJYscwqMks0U="}]}, {"Route": "images/onboarding-1.rw05rgbojm.jpg", "AssetFile": "images/onboarding-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "41637"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"234oSg737ZpiLcPlvxkrDR/NPxTPadosJYscwqMks0U=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rw05rgbojm"}, {"Name": "integrity", "Value": "sha256-234oSg737ZpiLcPlvxkrDR/NPxTPadosJYscwqMks0U="}, {"Name": "label", "Value": "images/onboarding-1.jpg"}]}, {"Route": "images/parental.jpg", "AssetFile": "images/parental.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "24367"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"mp+E7uF1kcokT6UusbZH66mccFYN0n56q1WBn8aCvFA=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mp+E7uF1kcokT6UusbZH66mccFYN0n56q1WBn8aCvFA="}]}, {"Route": "images/parental.yybck5txrc.jpg", "AssetFile": "images/parental.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "24367"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"mp+E7uF1kcokT6UusbZH66mccFYN0n56q1WBn8aCvFA=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:55 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yybck5txrc"}, {"Name": "integrity", "Value": "sha256-mp+E7uF1kcokT6UusbZH66mccFYN0n56q1WBn8aCvFA="}, {"Name": "label", "Value": "images/parental.jpg"}]}, {"Route": "images/phone.itqnu1e2xx.jpg", "AssetFile": "images/phone.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10423"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"hsmAzWzQ4/UmXqCo3jL1dqgoHy8bu4BUKQlAGOXGcTg=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "itqnu1e2xx"}, {"Name": "integrity", "Value": "sha256-hsmAzWzQ4/UmXqCo3jL1dqgoHy8bu4BUKQlAGOXGcTg="}, {"Name": "label", "Value": "images/phone.jpg"}]}, {"Route": "images/phone.jpg", "AssetFile": "images/phone.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "10423"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"hsmAzWzQ4/UmXqCo3jL1dqgoHy8bu4BUKQlAGOXGcTg=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 04:31:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hsmAzWzQ4/UmXqCo3jL1dqgoHy8bu4BUKQlAGOXGcTg="}]}, {"Route": "images/school.f7vz61kynb.jpg", "AssetFile": "images/school.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "36526"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"u7KCsrGks9t8ZHrP62EGsvjyOidEqMaQ/S0/Cf5qlNw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 15:05:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f7vz61kynb"}, {"Name": "integrity", "Value": "sha256-u7KCsrGks9t8ZHrP62EGsvjyOidEqMaQ/S0/Cf5qlNw="}, {"Name": "label", "Value": "images/school.jpg"}]}, {"Route": "images/school.jpg", "AssetFile": "images/school.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "36526"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"u7KCsrGks9t8ZHrP62EGsvjyOidEqMaQ/S0/Cf5qlNw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 15:05:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u7KCsrGks9t8ZHrP62EGsvjyOidEqMaQ/S0/Cf5qlNw="}]}, {"Route": "index.55aay04al6.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002314814815"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "431"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0=\""}, {"Name": "ETag", "Value": "W/\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.55aay04al6.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.55aay04al6.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "431"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "integrity", "Value": "sha256-7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002314814815"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "431"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0=\""}, {"Name": "ETag", "Value": "W/\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "431"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0="}]}]}