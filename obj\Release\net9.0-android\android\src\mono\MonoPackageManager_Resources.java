package mono;
public class MonoPackageManager_Resources {
	public static String[] Assemblies = new String[]{
		/* We need to ensure that "GateSale.dll" comes first in this list. */
		"GateSale.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"_Microsoft.Android.Resource.Designer.dll",
		"Microsoft.AspNetCore.Components.dll",
		"Microsoft.AspNetCore.Components.Web.dll",
		"Microsoft.AspNetCore.Components.WebView.dll",
		"Microsoft.AspNetCore.Components.WebView.Maui.dll",
		"Microsoft.Extensions.Configuration.dll",
		"Microsoft.Extensions.Configuration.Abstractions.dll",
		"Microsoft.Extensions.DependencyInjection.dll",
		"Microsoft.Extensions.DependencyInjection.Abstractions.dll",
		"Microsoft.Extensions.FileProviders.Abstractions.dll",
		"Microsoft.Extensions.FileProviders.Composite.dll",
		"Microsoft.Extensions.FileProviders.Embedded.dll",
		"Microsoft.Extensions.FileProviders.Physical.dll",
		"Microsoft.Extensions.FileSystemGlobbing.dll",
		"Microsoft.Extensions.Logging.dll",
		"Microsoft.Extensions.Logging.Abstractions.dll",
		"Microsoft.Extensions.Options.dll",
		"Microsoft.Extensions.Primitives.dll",
		"Microsoft.JSInterop.dll",
		"Microsoft.Maui.Controls.dll",
		"Microsoft.Maui.Controls.Xaml.dll",
		"Microsoft.Maui.dll",
		"Microsoft.Maui.Essentials.dll",
		"Microsoft.Maui.Graphics.dll",
		"Xamarin.AndroidX.Activity.dll",
		"Xamarin.AndroidX.AppCompat.dll",
		"Xamarin.AndroidX.AppCompat.AppCompatResources.dll",
		"Xamarin.AndroidX.CardView.dll",
		"Xamarin.AndroidX.Collection.Jvm.dll",
		"Xamarin.AndroidX.CoordinatorLayout.dll",
		"Xamarin.AndroidX.Core.dll",
		"Xamarin.AndroidX.CursorAdapter.dll",
		"Xamarin.AndroidX.CustomView.dll",
		"Xamarin.AndroidX.DrawerLayout.dll",
		"Xamarin.AndroidX.Fragment.dll",
		"Xamarin.AndroidX.Lifecycle.Common.Jvm.dll",
		"Xamarin.AndroidX.Lifecycle.LiveData.Core.dll",
		"Xamarin.AndroidX.Lifecycle.ViewModel.Android.dll",
		"Xamarin.AndroidX.Lifecycle.ViewModelSavedState.dll",
		"Xamarin.AndroidX.Loader.dll",
		"Xamarin.AndroidX.Navigation.Common.dll",
		"Xamarin.AndroidX.Navigation.Fragment.dll",
		"Xamarin.AndroidX.Navigation.Runtime.dll",
		"Xamarin.AndroidX.Navigation.UI.dll",
		"Xamarin.AndroidX.RecyclerView.dll",
		"Xamarin.AndroidX.SavedState.dll",
		"Xamarin.AndroidX.SwipeRefreshLayout.dll",
		"Xamarin.AndroidX.ViewPager.dll",
		"Xamarin.AndroidX.ViewPager2.dll",
		"Xamarin.Google.Android.Material.dll",
		"Xamarin.Kotlin.StdLib.dll",
		"Xamarin.KotlinX.Coroutines.Core.Jvm.dll",
		"Xamarin.KotlinX.Serialization.Core.Jvm.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"_Microsoft.Android.Resource.Designer.dll",
		"Microsoft.AspNetCore.Components.dll",
		"Microsoft.AspNetCore.Components.Web.dll",
		"Microsoft.AspNetCore.Components.WebView.dll",
		"Microsoft.AspNetCore.Components.WebView.Maui.dll",
		"Microsoft.Extensions.Configuration.dll",
		"Microsoft.Extensions.Configuration.Abstractions.dll",
		"Microsoft.Extensions.DependencyInjection.dll",
		"Microsoft.Extensions.DependencyInjection.Abstractions.dll",
		"Microsoft.Extensions.FileProviders.Abstractions.dll",
		"Microsoft.Extensions.FileProviders.Composite.dll",
		"Microsoft.Extensions.FileProviders.Embedded.dll",
		"Microsoft.Extensions.FileProviders.Physical.dll",
		"Microsoft.Extensions.FileSystemGlobbing.dll",
		"Microsoft.Extensions.Logging.dll",
		"Microsoft.Extensions.Logging.Abstractions.dll",
		"Microsoft.Extensions.Options.dll",
		"Microsoft.Extensions.Primitives.dll",
		"Microsoft.JSInterop.dll",
		"Microsoft.Maui.Controls.dll",
		"Microsoft.Maui.Controls.Xaml.dll",
		"Microsoft.Maui.dll",
		"Microsoft.Maui.Essentials.dll",
		"Microsoft.Maui.Graphics.dll",
		"Xamarin.AndroidX.Activity.dll",
		"Xamarin.AndroidX.AppCompat.dll",
		"Xamarin.AndroidX.AppCompat.AppCompatResources.dll",
		"Xamarin.AndroidX.CardView.dll",
		"Xamarin.AndroidX.Collection.Jvm.dll",
		"Xamarin.AndroidX.CoordinatorLayout.dll",
		"Xamarin.AndroidX.Core.dll",
		"Xamarin.AndroidX.CursorAdapter.dll",
		"Xamarin.AndroidX.CustomView.dll",
		"Xamarin.AndroidX.DrawerLayout.dll",
		"Xamarin.AndroidX.Fragment.dll",
		"Xamarin.AndroidX.Lifecycle.Common.Jvm.dll",
		"Xamarin.AndroidX.Lifecycle.LiveData.Core.dll",
		"Xamarin.AndroidX.Lifecycle.ViewModel.Android.dll",
		"Xamarin.AndroidX.Lifecycle.ViewModelSavedState.dll",
		"Xamarin.AndroidX.Loader.dll",
		"Xamarin.AndroidX.Navigation.Common.dll",
		"Xamarin.AndroidX.Navigation.Fragment.dll",
		"Xamarin.AndroidX.Navigation.Runtime.dll",
		"Xamarin.AndroidX.Navigation.UI.dll",
		"Xamarin.AndroidX.RecyclerView.dll",
		"Xamarin.AndroidX.SavedState.dll",
		"Xamarin.AndroidX.SwipeRefreshLayout.dll",
		"Xamarin.AndroidX.ViewPager.dll",
		"Xamarin.AndroidX.ViewPager2.dll",
		"Xamarin.Google.Android.Material.dll",
		"Xamarin.Kotlin.StdLib.dll",
		"Xamarin.KotlinX.Coroutines.Core.Jvm.dll",
		"Xamarin.KotlinX.Serialization.Core.Jvm.dll",
	};
	public static String[] Dependencies = new String[]{
	};
}
